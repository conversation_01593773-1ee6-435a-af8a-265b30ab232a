<template>
    <div class="principal">
        <div>
            <vx-card :title="`Refacturación - ${sesion.sesion_sucursal_nombre}`">    
                <vs-row>
                        <ConfiguracionCaja  ref="ConfiguracionCajaRef"
                                           @CargarCaja="CargarCaja" 
                                           TipoCajaSeleccionada="TipoCajaFacturacion" CajaSeleccionada="CajaFacturacion" 
                                           class="w-full">
                        </ConfiguracionCaja>
                        <vs-row class="sm:w-full md:w-full lg:w-full xl:w-1/2">
                            <vs-row class="w-full">
                                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-3/12 p-1">
                                    <vs-input label="Serie" 
                                            ref="inputSerie"
                                            v-model="facturacion.serie" 
                                            class="w-full"
                                            />   
                                </vs-col>  
                                <vs-col class="sm:w-full md:w-full lg:w-7/12 xl:w-5/12 p-1">  
                                    <vs-input label="No. Factura" 
                                            ref="inputNumeroFactura"
                                            v-model="facturacion.numeroFactura" 
                                            class="w-full"
                                            v-on:keydown.tab.prevent="Validaciones()"
                                            />   
                                </vs-col>
                                <vs-col class="sm:w-full md:w-full lg:w-full xl:w-4/12 p-1" style="color:gray; font-size:11pt;"> 
                                    <div class="align-self-center lg:p-1"> 
                                        <label style="font-weight:bold;" > Fecha: </label> {{ facturacion.fecha ? moment(facturacion.fecha).format('YYYY-MM-DD'):''}}
                                    </div>
                                    <div class="align-self-center lg:p-1">
                                        <label style="font-weight:bold;" > Monto: </label>   {{ $formato_moneda(facturacion.total) }}
                                    </div>
                                </vs-col>
                            </vs-row>
                            <vs-row class="w-full">
                                <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-3/12 p-1" style="height: 60px;">   
                                    <label class="typo__label label-shrink" style="white-space: nowrap; color:#626262; font-size:12px;  padding-bottom:2px; padding-top:0px;"> Tipo Documento  </label>  
                                    <multiselect    
                                                    class="m-0 p-0"
                                                    v-model="documento.tipoDocumento" :custom-label="tipo_documento_seleccionado" 
                                                    :options="documento.tiposDocumentoLista"  :allow-empty="false" :showLabels="false"                                                                                    
                                                    placeholder="" >
                                    </multiselect>      
                                </vs-col>           
                                <vs-col class="sm:w-full md:w-full lg:w-7/12 xl:w-5/12 p-1">
                                    <vs-input label="No. documento" 
                                            v-model="documento.noDocumento" 
                                            ref="inputNoDocumento"
                                            class="w-full"
                                            @keydown.enter="ValidacionDocumentoTributarioTab()" @keydown.tab.prevent="ValidacionDocumentoTributarioTab()"
                                            />      
                                </vs-col> 
                            </vs-row>
                            <vs-row class="w-full"> 
                                <vs-col class="sm:w-full md:w-full lg:w-11/12 xl:w-8/12 p-1">
                                    <vs-input id="idNombre"
                                            label="Nombre"  
                                            class="w-full"
                                            v-model="documento.nombre" />      
                                </vs-col> 
                            </vs-row>
                            <vs-row>
                                <vs-col class="sm:w-full md:w-full lg:w-11/12 xl:w-8/12 p-1">
                                    <vs-textarea label="Dirección"   
                                                 class="w-full"  
                                                 rows="3"     
                                                 v-model="documento.direccion" />      
                                </vs-col>
                            </vs-row>
                            <vs-row class="w-full">                                                       
                                <vs-col class="sm:w-full md:w-full lg:w-11/12 xl:w-8/12 p-1">
                                        <vs-textarea label="Razón"   
                                                    class="w-full"                                          
                                                    rows="3" 
                                                    counter="25"                                                     
                                                    v-model="documento.razon" />   
                                </vs-col>  
                            </vs-row>
                        </vs-row>                   
                        <vs-row class = 'w-full pt-3 pb-3'>
                            <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1">
                                <vs-button color="success" class="w-full" :disabled="!configuracion.activarFacturacion" 
                                    id="botonFactura"
                                    :type="facturaSeleccionado?'border':'filled'" 
                                    @keyup.tab="facturaSeleccionado=true"
                                    v-on:blur="facturaSeleccionado=false"
                                    style="font-size:12pt"                                                                                                          
                                    @click="GrabarRefacturacionHospital()">
                                    Refacturar
                                </vs-button>
                            </vs-col>
                            <vs-col class="sm:w-full md:w-full lg:w-4/12 xl:w-2/12 p-1">
                                <vs-button color="warning" class="w-full"
                                    style="font-size:12pt"                                                                                                                                             
                                    @click="LimpiarPantalla()">
                                    Limpiar
                                </vs-button>
                            </vs-col>
                        </vs-row>        
                        <vs-divider></vs-divider>                        
                </vs-row>            
            </vx-card>
        </div>
    </div>
</template>
    
<script>    
    import ConfiguracionCaja from "/src/components/sermesa/modules/caja/ConfiguracionCaja.vue";    
    import "vue-multiselect/dist/vue-multiselect.min.css"; 
    import moment from "moment";
    export default {
        components:{                   
            ConfiguracionCaja
        },    
        data() {    
            return {   
                moment:moment, 
                facturaSeleccionado:false, 
                configuracion:{                
                    cajaSeleccionada: null,
                    activarFacturacion: false,
                    activarRecibos: false                      
                },                    
                info:{  serie:null,
                        numeroAdmision:null,                  
                        tipoDescuento:null
                     },
                documento:{ tiposDocumentoLista:[],
                            tipoDocumento:null,
                            noDocumento:null,
                            nombre:null,
                            direccion:null,
                            razon:null,
                            codigo:1			
                    },	 
                caja: { serieFactura:[],
                        serieFacturaFiltrada:[]
                    }, 
                facturacion:{
                    serie:null,
                    numeroFactura:null,
                    serieInterna:null,
                    numeroFacturaInterna:null,
                    fecha:null,
                    tipoFactura:null,
                    total:null
                },
                quitar_espacio_buscar : { padding: '0 !important;' },                            
            }
        },
        computed:{ 
            tipoAdmision(){
                return this.info.tipoDescuento == 'N' ? 'Privada' : this.info.tipoDescuento == 'R' ? 'Reingreso' : 
                       this.info.tipoDescuento == 'Q' ? 'Paquete' : this.info.tipoDescuento == 'S' ? 'Seguro' :
                       this.info.tipoDescuento == 'E' ? 'Empleado' : ''
            },
            sesion() {
                return this.$store.state.sesion
            }
        },
        methods: {   
            CargarCaja(cajaSeleccionada){                
                this.configuracion.cajaSeleccionada   = cajaSeleccionada.Cajero
                this.configuracion.activarFacturacion = cajaSeleccionada.Factura
                this.configuracion.activarRecibos     = cajaSeleccionada.Recibo
            },
            ValidarAnulacionFel(){
                if(!this.facturacion.serie){
                    this.focusSerie()
                    return
                }

                if(!this.facturacion.numeroFactura){
                    this.focusNoFactura()
                    return
                }

                this.axios.post("/app/v1_caja/ValidaRefacturacion", {
                    "serieFactura": this.facturacion.serieInterna,
                    "numeroFactura": this.facturacion.numeroFacturaInterna,
                    "tipoProceso": 2
                })
                .then(resp => {


                    if(resp.data.codigo == '0' && resp.data.json.length>0 && resp.data.json[0].codigo=="0"){
                        
                        const { Status } = resp.data.json[0]                      
                                              
                        if(Status=='X'){
                            //Mandar a anular
                            this.Consulta()
                                .MostrarSatisfactorio('Generando Anulación de Factura:'+this.facturacion.serie+'-'+this.facturacion.numeroFactura,2000);
                            this.Guardar().AnulacionFacturaFel(this.facturacion.serieInterna,this.facturacion.numeroFacturaInterna)
                        }
                        this.LimpiarPantalla()
                    }      
                })
                .catch(() => {                         
                    this.focusSerie()
                })
            },
            Validaciones() {
                if(!this.facturacion.serie){
                    this.focusSerie()
                    return
                }

                if(!this.facturacion.numeroFactura){
                    this.focusNoFactura()
                    return
                }

                this.axios.post("/app/v1_caja/ValidaRefacturacion", {
                    "serieFactura": this.facturacion.serie,
                    "numeroFactura": this.facturacion.numeroFactura,
                    "tipoProceso": 1
                })
                .then(resp => {


                    if(resp.data.codigo == '0' && resp.data.json.length>0 && resp.data.json[0].codigo=="0"){
                        
                        const { Nit, Fecha, SerieAdmision, Admision,
                                Nombre,Total, TipoReceptor, Direccion, 
                                Tipo, SerieFac, Factura  } = resp.data.json[0]
                        
                        let TipoReceptorObj
                        if(TipoReceptor && TipoReceptor > 0){
                             TipoReceptorObj = this.documento.tiposDocumentoLista.find(tipoDoc=>tipoDoc.Valor==TipoReceptor)
                        }else{
                             TipoReceptorObj = {
                                                    "Id": "3",
                                                    "Descripcion": "NIT",
                                                    "Valor": "4"
                                                }
                        }
                        
                        
                        this.documento.tipoDocumento = TipoReceptorObj
                        this.documento.noDocumento = Nit
                        this.documento.nombre = Nombre
                        this.documento.direccion = Direccion

                        this.info.serie = SerieAdmision
                        this.info.numeroAdmision = Admision
                        
                        this.facturacion.serieInterna = SerieFac
                        this.facturacion.numeroFacturaInterna = Factura
                        this.facturacion.tipoFactura = Tipo
                        this.facturacion.fecha = Fecha
                        this.facturacion.total = Total
                        this.ValidacionDocumentoTributario()
                        this.focusNoDocumento()
                    }else if (resp.data.codigo == '0' && resp.data.json.length>0 && resp.data.json[0].codigo!="0"){
                        this.LimpiarDatosCliente()
                        this.Consulta().MostrarError(resp.data.json[0].descripcion,3000)
                        this.focusSerie()
                    }
                    

                })
                .catch(() => {                         
                    this.focusSerie()
                })
                
            }, 
            focusSerie(){
                this.$refs.inputSerie.$el.querySelector('input').focus()
            },
            focusNoFactura(){
                this.$refs.inputNumeroFactura.$el.querySelector('input').focus()
            },
            focusNoDocumento(){
                this.$refs.inputNoDocumento.$el.querySelector('input').focus()                
            },          
            tipo_documento_seleccionado(tipoDocumento){
                return `${tipoDocumento.Descripcion}`
            },
            GrabarRefacturacionHospital(){
                if(!this.ValidarVariable(this.facturacion.serie) || !this.ValidarVariable(this.facturacion.numeroFactura)){
                    this.Consulta().MostrarError('Debe ingresar la serie y no. de factura a refacturar',3000);  
                    return
                }

                if(!this.ValidarVariable(this.facturacion.serieInterna) || !this.ValidarVariable(this.facturacion.numeroFacturaInterna)){
                    this.Consulta().MostrarError('Ingrese el numero interno de la factura',3000);  
                    return
                }                

                if(!this.ValidarVariable(this.documento.tipoDocumento.Valor) || !this.ValidarVariable(this.documento.noDocumento) || this.documento.codigo > 0){
                    this.Consulta().MostrarError('Debe ingresar un tipo y no. de documento valido',3000);  
                    return
                }

                if(!this.ValidarVariable(this.documento.razon)){
                    this.Consulta().MostrarError('Ingresar la razón',3000);  
                    return                
                }

                if(!(this.ValidarVariable(this.documento.direccion))){
                    this.Consulta().MostrarError('Ingresar dirección',3000);  
                    return                
                }

                if(this.documento.razon && this.documento.razon.length>25){
                    this.Consulta().MostrarError('Reducir el campo razón a 25 caracteres ',3000);  
                    return
                }
                
                const botonFactura = document.getElementById("botonFactura");
                if(botonFactura) botonFactura.blur();

                this.axios.post('/app/v1_caja/Refacturacion',
                                {
                                    serieFactura:this.facturacion.serie,
                                    numeroFactura:this.facturacion.numeroFactura,
                                    serieNuevaFac:this.configuracion.cajaSeleccionada.SerieFac,
                                    tipoReceptor:this.documento.tipoDocumento.Valor,
                                    nitCliente:this.documento.noDocumento,
                                    nombreFactura:this.documento.nombre,
                                    direccion:this.documento.direccion,
                                    numeroCaja:this.configuracion.cajaSeleccionada.CajaLocal,
                                    Razon:this.documento.razon                                
                                }).then(resp=>{
                                    if(resp.data.codigo == 0){
                                        this.$vs.dialog({
                                            type: 'confirm',
                                            color: 'success',
                                            title: 'Refacturación',
                                            acceptText: 'Aceptar',
                                            text: resp.data.descripcion,                                               
                                        })

                                        this.Guardar().Fel(resp.data.Serie,resp.data.Factura);                                        
                                        this.ValidarAnulacionFel()                                        
                                    }
                                });
            },
            ValidarVariable(entrada){   
                if(typeof(entrada) == 'number'){
                    return entrada ? true : false;
                }else if(typeof(entrada) == 'string'){
                    return entrada && entrada.trim().length > 0 ? true : false;
                }else if(typeof(entrada) == 'object'){
                    return entrada ? true : false;
                }            
                return false;
            }, 
            async ValidacionDocumentoTributarioTab(){
                await this.ValidacionDocumentoTributario();
                if(this.documento.codigo == 0){
                    const campoNombre = document.getElementById("idNombre");
                    if(campoNombre) campoNombre.focus();
                }
            },                        
            ValidacionDocumentoTributario(){
                if(this.Consulta().EsVacia(this.documento.tipoDocumento)){
                    this.Consulta().MostrarError('Seleccione un tipo de documento');
                    return;
                }
                if(this.Consulta().EsVacia(this.documento.noDocumento)){
                    this.Consulta().MostrarError('Ingrese el numero de documento');
                    return;
                }
                this.axios.post('/app/v1_caja/ValidacionDocumentoTributario',{
                                    tipoReceptor: parseInt(this.documento.tipoDocumento.Valor),
                                    documento: this.documento.noDocumento
                                })
                          .then(resp=>{
                                    if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo == 0){
                                        this.documento.nombre = resp.data.json[0].NOMBRE
                                        this.documento.direccion = resp.data.json[0].DIRECCION
                                        this.documento.codigo = 0
                                    }else if ( resp.data.codigo == 0 && resp.data.json.length > 0 && resp.data.json[0].codigo != 0){
                                        this.Consulta().MostrarError(resp.data.json[0].descripcion);
                                        this.documento.nombre = null
                                        this.documento.direccion = null
                                        this.documento.codigo = 1
                                    }
                                    else if(resp.data.json.length == 0){
                                        this.documento.nombre = null
                                        this.documento.direccion = null
                                        this.documento.codigo = 1
                                        this.Consulta().MostrarError('No se encontraron datos para el documento ingresado');
                                    }
                                })
                          .catch(()=>{
                                        this.documento.nombre = null
                                        this.documento.direccion = null
                                        this.documento.codigo = 1
                                })
                            
            },
            CargarTipoDocumento(){
                this.axios.post('/app/v1_caja/MantenimientoCajeroAdmision', {
                                    opcion:'C',
                                    subOpcion:2,
                                    agrupacion:'TipoReceptor'
                                })
                          .then(resp => {                            
                                    if (resp.data && resp.data.json.length > 0){
                                        this.documento.tiposDocumentoLista = resp.data.json;
                                    }
                                }
                               )
            },                     
            LimpiarPantalla(){
                this.info.serie = null
                this.info.numeroAdmision = null
                this.info.tipoDescuento = null

                this.documento.noDocumento = null
                this.documento.nombre = null
                this.documento.direccion = null
                this.documento.razon = null
 
                this.facturacion.serie = null
                this.facturacion.numeroFactura = null
                this.facturacion.serieInterna = null
                this.facturacion.numeroFacturaInterna = null
                this.facturacion.fecha = null
                this.facturacion.tipoFactura = null
                this.facturacion.total = null
            },
            LimpiarDatosCliente(){
                this.info.serie = null
                this.info.numeroAdmision = null
                this.info.tipoDescuento = null

                this.documento.noDocumento = null
                this.documento.nombre = null
                this.documento.direccion = null
                this.documento.razon = null
 
                this.facturacion.serieInterna = null
                this.facturacion.numeroFacturaInterna = null
                this.facturacion.fecha = null
                this.facturacion.tipoFactura = null
                this.facturacion.total = null
            },
            async GenerarFactura(Serie,NumeroFactura) {
                let reporte = "Impresion FEL"

                let postData = {
                    SerieFactura: Serie ,
                    NumFactura: NumeroFactura,
                    nombrereporte: this.configuracion.cajaSeleccionada.FormatoImpresion
                }


                this.$reporte_modal({
                    Nombre: reporte,
                    Opciones: {
                        ...postData
                    }
                })                

            },   
            actualizarCajero(){  
                this.$refs.ConfiguracionCajaRef.CargarCajaInicial();
            },
            Consulta() {
                return {                  
                    init: async () => {
                        this.LimpiarPantalla();
                        this.CargarTipoDocumento(); 
                    },
                    EsVacia:(variable)=>{
                        return variable?false:true;
                    },
                    MostrarError:(mensaje,tiempo=2000)=>{
                        this.$vs.notify({
                            time:tiempo,
                            title: 'Cajero',
                            color: 'danger',
                            text:mensaje,
                            position: 'top-center'
                        })
                    },
                    MostrarSatisfactorio:(mensaje)=>{
                        this.$vs.notify({
                            time:2000,
                            title: 'Cajero',
                            color: 'success',
                            text:mensaje,
                            position: 'top-center'
                        })
                    }
                }
            },
            Guardar() {
                return {
                    Fel:(serieFactura,numeroFactura)=>{
                        this.axios.post('/app/v1_FacturaElectronica/GeneraFel',
                                        {serieFactura: serieFactura,
                                         numeroFactura: numeroFactura
                                        })                                  
                                  .then(resp=>{       
                                            if(resp.data.codigo == 0 && resp.data.json[0].codigo == 0){
                                                this.GenerarFactura(serieFactura,numeroFactura);                                 
                                                this.Consulta().MostrarSatisfactorio(resp.data.json[0].descripcion);

                                            }else{
                                                this.$vs.dialog({
                                                        type: 'confirm',
                                                        color: 'danger',
                                                        title: 'Facturación',
                                                        acceptText: 'Aceptar',
                                                        text: resp.data.json[0].descripcion,                                               
                                                    })                                               
                                            }
                                        })
                    },
                    AnulacionFacturaFel:(serieFactura,numeroFactura)=>{
                        this.axios.post('/app/v1_FacturaElectronica/AnulaFel',
                                        {serieFactura: serieFactura,
                                         numeroFactura: numeroFactura,
                                         tipoOperacion: 0
                                        })                                  
                                  .then(() => {
                                    })
                                  .catch(()=>{
                                                this.$vs.dialog({
                                                        type: 'confirm',
                                                        color: 'danger',
                                                        title: 'Anulación',
                                                        acceptText: 'Aceptar',
                                                        text: 'Complete la anulación de la factura: '+serieFactura +'-'+numeroFactura+' en el monitor de contigencias.',                                               
                                                    })
                                          })
                    },
                }
            },
        },
        mounted() {
            this.Consulta().init()                     
        },
        activated() {
            window.addEventListener('focus', this.actualizarCajero);   
        },
        deactivated() {
            window.removeEventListener('focus',this.actualizarCajero);
        },

    }
    </script>
    <style>
    .multiselect__tags {
        border:1px solid rgba(0, 0, 0, 0.4); 
        border-radius:5px;
    }

    .multiselect__single {
        color:#2980B9;
    }
    .vs-con-textarea h4{
        color:#626262;
        font-weight: normal;
        font-size:14px;
    }

    .fuente-label {
        font-size: 10pt;
        white-space: nowrap;
        color: red;
    }

    .cantidad button {
        height: 25px;
        width: 30px;
        border: 1px solid #ccc;
        border-radius: 5px;    
    }
    
    .cantidad div {
        display: inline-block;
        height: 25px;
        width: 30px;
        text-align: center;
    }

    .quitar_espacio {
        padding: 0;
        margin: 0;        
    }

    .quitar_espacio_buscar > .label{
        padding: 0 !important;
    }
    
    .label-shrink{
        display: block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        width: 100%;
    }

</style>    