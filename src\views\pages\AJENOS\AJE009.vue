<template>
<div>
    <vs-popup ref="buscador" title="Editar Servicio de Interpretación" :active.sync="editarTabla.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container">
        <Nuevo  :callback="Consulta().Consulta_Listado" :editarTabla.sync="editarTabla" />
    </vs-popup>
    <vx-card title="Tabla Interpretación">
        <Nuevo :callback="Consulta().Consulta_Listado"/>
        <vs-divider></vs-divider>
            <!-- {{interpretacion}} -->
            <vs-table2 max-items="10" search tooltip pagination :data="interpretacion">
                <template slot="thead">
                    <th order="Categoria;CategoriaNombre">Categoría</th>
                    <th order="Producto">Servicio</th>
                    <th width="120px">Valor</th>
                    <th width="140px">Estado</th>
                    <th width="140px">Acción</th>
                </template>

                <template slot-scope="{data}">
                    <tr :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td2>
                            ({{ tr.Categoria }}) {{tr.CategoriaNombre}}
                        </vs-td2>
                        <vs-td2>
                            ({{tr.Producto}}) {{tr.ProductoNombre}}
                        </vs-td2>
                        <vs-td2>
                            {{(tr.ValorNominal > 0 )?tr.ValorNominal: tr.ValorPorcentual+'%'}}
                        </vs-td2>
                        <vs-td2>
                            <vs-switch v-model="tr.Status" disabled="true" style="height: 23px;" />
                        </vs-td2>
                        <vs-td2 noTooltip>
                            <vs-button color="primary" class="mr-1" style="display:inline-block" v-on:click="editarTabla.mostrar=true;editarTabla.info = tr">Editar</vs-button>
                        </vs-td2>
                    </tr>
                </template>
            </vs-table2>
            <vs-divider></vs-divider>
            <!-- <vs-button color="success" style="float:right" type="filled" @click.native="handleSubmit(()=>info.MostrarFactura=true)" :disabled="invalid || pagos.filter(f=>f.seleccion).length==0">Ingresar Factura</vs-button>
            <div style="clear:both"></div> -->
        
    </vx-card>
    <!-- <div class="facturas" v-if="info.MostrarFactura">
        <Facturas v-model="info" :cancelar="Otros().facturasCancelar" :tabla="pagos.filter(f=>f.seleccion)" />
    </div> -->
</div>
</template>

<script>
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Permite indicar el mónto asignado a un servicio de interpretación.',
            ],

            /**
             * Editar el monto de la tabla
             */
            info: {
                Categoria: null,
                CategoriaNombre: null,
                Producto: null,
                ProductoNombre: null,
                ValorNominal: null,
                ValorPorcentual: null,
                Status: null
            },

            editarTabla:{
                mostrar:false,
                info:null
            },

            /**
             * Listado de permisos
             */
            permisos: {
                editar: false,
                nuevo: true
            },

            /**
             * Información de los pagos
             */
            pagos: [],
            pagosSeleccion: [],

            interpretacion: []

        }
    },
    computed: {
        calculcar() {
            const arr = this.pagos.filter(f => f.seleccion)
            return this.$formato_moneda(arr.reduce((acumulador, valor) => acumulador + parseFloat(valor.Valor), 0))
        }
    },
    components: {
        Nuevo: () => import('./AJE009_Nuevo')
    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {
                Consulta_Listado: () => {
                    return this.axios.post('/app/ajenos/Tabla_Interpretacion_Listado', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.interpretacion = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Status: (m.Status == 'S') ? 1 : 0
                                    }
                                })
                            }
                        })
                },
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {}
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                    this.pagos = []
                }
            }
        }
    },
    mounted() {
        this.Consulta().Consulta_Listado()
    }
}
</script>

<style scoped>
.facturas {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100em;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 50px;
    z-index: 99999;
}
</style>
