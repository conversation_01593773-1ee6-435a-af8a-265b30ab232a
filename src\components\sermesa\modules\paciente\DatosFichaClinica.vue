
<template>
    <div class="container">


            <ValidationObserver ref="clinicInfoData" v-slot="{invalid, handleSubmit }" mode="lazy">
                <form @submit.prevent="handleSubmit(submitForm(GestionPacientes().Crear))">

                    <!----------------------------------------------FICHA CLINICA    -------------------------------------------------------------------->
                    <div class="tab-text">

                        <div class="container_div  p-1">

                            <div class="container">

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="10">
                                        <div class="w-full  pl-4 pt-2">
                                            <h6>Información Hospitalaria</h6>
                                            <hr
                                                style="height: 8px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3 pl-2" >
                                            <validationProvider name="Fecha Primera Visita">
                                                <vs-input label="Fecha Primera Visita" class="w-full" type="date"
                                                    v-model.trim="PacienteData.FechaPrimeraVisita" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3">
                                            <validationProvider name="Fecha Ultima Visita" >
                                                <vs-input label="Fecha Ultima Visita" class="w-full" type="date"
                                                    v-model="PacienteData.FechaUltimaVisita"  />
                                            </validationProvider>
                                        </div>
                                    </vs-col>
                                </vs-row>



                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3 pl-2">
                                            <validationProvider name="Medico de cabecera">
                                                <vs-input label="Medico de cabecera" class="w-full"
                                                    v-model="PacienteData.MedicoCabecera" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3  p-2">
                                            <validationProvider name="Contacto Telefonico medico cabecera">
                                                <vs-input label="Contacto Telefonico medico cabecera" class="w-full"
                                                    v-model="PacienteData.TelefonoMedicoCabecera" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3 pl-2">
                                            <validationProvider name="Otros Medicos">
                                                <vs-input label="Otros Medicos" class="w-full"
                                                    v-model="PacienteData.OtrosMedicos" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3">
                                            <validationProvider name="Contacto telefonico otros Medicos">
                                                <vs-input label="Contacto telefonico otros Medicos" class="w-full"
                                                    v-model="PacienteData.TelefonoOtrosMedicos" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="10">
                                        <div class="w-full  pl-4 pt-2 ">
                                            <h6>Ficha Clinica</h6>
                                            <hr
                                                style="height: 8px; border: 0; box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5)">
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">
                                        <div class="w-2/3 ml-2">
                                                <label class="vs-input--label">Grupo Sanguineo </label>
                                               <multiselect
                                                 v-model="SelTipoSanguineo"
                                                 :options="TiposSanguineos"
                                                 :disabled="false"
                                                 :allow-empty="false"
                                                 placeholder="Selección de Tipo Sanguineo"
                                                 track-by="Nombre"
                                                 label="Nombre"
                                                 >
                                                 </multiselect>                                            
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">

                                        <div class="w-full  pl-4 pt-2">
                                            <h6>Alergias</h6>
                                            <hr style="width:100%;text-align:left;margin-left:0" />
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="6" class="pt-4">

                                        <div class="w-full  pl-4">

                                            <label class="vs-input--label">Alergias a Alimentos </label>
                                            <multiselect
                                              v-model="SelAlergias[1].Seleccion"
                                              :height="300"
                                              :options="SelAlergias[1].ListaAlergias"
                                              :multiple="true"
                                              :taggable="true"
                                              :close-on-select="false"
                                              :clear-on-select="false"
                                              :preserve-search="true"
                                              tag-placeholder="Add this as new tag"
                                              placeholder="Alergias Alimentos"
                                              :id="1"
                                              @tag="addTag"
                                              label="Etiqueta"
                                              track-by="Codigo"
                                              :preselect-first="false">
                                              </multiselect>


                                            <template slot="selection" slot-scope="{ values,  isOpen }"><span
                                                    class="multiselect__single" v-if="values.length &amp;&amp; !isOpen">{{
                                                        values.length
                                                    }}
                                                    options selected</span>
                                            </template>
                                        </div>

                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="6">

                                        <div class="w-full  p-4">
                                            <label class="vs-input--label">Alergias a Medicamentos
                                            </label>
                                                    <multiselect
                                                    v-model="SelAlergias[0].Seleccion"
                                                    :height="300"
                                                    :options="SelAlergias[0].ListaAlergias"
                                                    :multiple="true"
                                                    :taggable="true"
                                                    :close-on-select="false"
                                                    :clear-on-select="false"
                                                    :preserve-search="true"
                                                    tag-placeholder="Add this as new tag"
                                                    placeholder="Alergias a Medicamentos"
                                                    @select="onSelect_selectedGood($event, 'AlergiasMedicamentos')"
                                                    :id="0"
                                                    @tag="addTag"
                                                    label="Etiqueta"
                                                    track-by="Codigo"
                                                    :preselect-first="false">
                                                    </multiselect>


                                            <template slot="selection" slot-scope="{ values, isOpen }"><span
                                                    class="multiselect__single" v-if="values.length &amp;&amp; !isOpen">{{
                                                        values.length
                                                    }}
                                                    options selected</span>
                                            </template>
                                        </div>

                                    </vs-col>

                                </vs-row>

                                <!-- <vs-row>
                                    <vs-col vs-type="flex" vs-w="6">

                                        <div class="w-full  p-2">
                                            <h6>Vacunas COVID</h6>
                                            <hr style="width:100%;text-align:left;margin-left:0" />
                                        </div>
                                    </vs-col>
                                </vs-row>

                                <vs-row>
                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="w-2/3">
                                            <validationProvider name="Medico de cabecera">
                                                <vs-input label="Primera Dosis" class="w-full p-2"
                                                    v-model="PacienteData.MedicoCabecera" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="w-2/3">
                                            <validationProvider name="Contacto Telefonico medico cabecera">
                                                <vs-input label="Segunda Dosis" class="w-full p-2"
                                                    v-model="PacienteData.TelefonoMedicoCabecera" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>

                                    <vs-col vs-type="flex" vs-w="4">
                                        <div class="w-2/3">
                                            <validationProvider name="Contacto Telefonico medico cabecera">
                                                <vs-input label="Tercera Dosis" class="w-full p-2"
                                                    v-model="PacienteData.TelefonoMedicoCabecera" />
                                            </validationProvider>
                                        </div>
                                    </vs-col>
                                </vs-row> -->


                            </div>

                            <div v-show="showButtons">
                                <vs-button style="margin-right:1px" v-on:click="handleSubmit(submit())" :disabled="invalid">
                                    Guardar
                                </vs-button>
                            </div>

                        </div>

                    </div>

                    <!----------------------------------------------------------------------------------------------------------------------------------->
                </form>
            </ValidationObserver>
        <!-- </vx-card> -->
    </div>
</template>



<script>

import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
import { mapFields } from 'vuex-map-fields';
// import { mapState } from "vuex";
import moment from "moment";


export default {
    name: "DatosFichaClinica",
    data: () => ({
        bloquear: false,
        SelTipoSanguineo: '',
        showButtons: false,
        TiposSanguineos: [
            { Codigo: 'A+',Nombre: 'A+'},
            { Codigo: 'A-', Nombre: 'A-' },
            { Codigo: 'B-', Nombre: 'B-' },
            { Codigo: 'O+', Nombre: 'O+' },
            { Codigo: 'O-', Nombre: 'O-' },
            { Codigo: 'AB+', Nombre: 'AB+' },
            { Codigo: 'AB-', Nombre: 'AB-' },
            { Codigo: 'B+', Nombre: 'B+' }
        ],
        SelAlergias: [
            {
                id: 1, name: 'AlergiasMedicamentos', Seleccion: [], ListaAlergias: [
                    {
                        "Codigo": "1",
                        "Etiqueta": "ASPIRINA",
                    },
                    {
                        "Codigo": "2",
                        "Etiqueta": "ANTIFLAMATORIOS",
                    },
                    {
                        "Codigo": "3",
                        "Etiqueta": "PENICILINA",
                    }
                ]
            },
            {
                id: 2, name: 'AlergiasAlimentos', Seleccion: [], ListaAlergias: [
                    {
                        "Codigo": "1",
                        "Etiqueta": "PEZCADO",
                    },
                    {
                        "Codigo": "2",
                        "Etiqueta": "HUEVOS",
                    },
                    {
                        "Codigo": "3",
                        "Etiqueta": "TRIGO",
                    },
                    {
                        "Codigo": "3",
                        "Etiqueta": "SOYA",
                    },
                    {
                        "Codigo": "4",
                        "Etiqueta": "LECHE",
                    },
                    {
                        "Codigo": "5",
                        "Etiqueta": "FRUTOS SECOS",
                    },
                    {
                        "Codigo": "6",
                        "Etiqueta": "MARISCOS",
                    },
                    {
                        "Codigo": "7",
                        "Etiqueta": "MANIES",
                    },
                    {
                        "Codigo": "8",
                        "Etiqueta": "GLUTEN",
                    }
                ]
            }
        ]

    }),
    components: {
        Multiselect        
    },
    computed: {
        ...mapFields('paciente', ["PacienteData"])
    },
    created() {    
        this.detectFocusOut();
    },
    mounted() {
        this.SelTipoSanguineo = this.TiposSanguineos.find(option => option.Codigo === this.PacienteData.TipoDeSangre)
    },
    activated(){
        
        this.PacienteData.FechaPrimeraVisita = moment(this.PacienteData.FechaPrimeraVisita).format('YYYY-MM-DD');
        this.PacienteData.FechaUltimaVisita = moment(this.PacienteData.FechaUltimaVisita).format('YYYY-MM-DD');
        this.SelTipoSanguineo = this.TiposSanguineos. find(option => option.Codigo === this.PacienteData.TipoDeSangre )
    },
    methods: {
        getionarDatos() {
            return new Promise((resolve) => {
                // Lógica de guardado del cliente aquí
                // Resuelve la promesa si el guardado es exitoso
                return resolve(true);
                // Rechaza la promesa si hay un error en el guardado
                // reject('Mensaje de error aquí');
            });
        },
        validacionFormulario() {
            return new Promise((resolve, reject) => {
                this.$refs.clinicInfoData.validate()
                    .then((isValid) => {
                        if (isValid) {
                            
                            resolve(true);
                        } else {
                            
                            reject('Revisar campos requeridos');
                        }
                    })
                    .catch((error) => {
                        
                        reject(error);
                    });
            });
        },
        gestionarPaciente() {
            return new Promise((resolve, reject) => {
                this.$store.dispatch('paciente/crearPaciente')
                    .then((response) => {
                        resolve(response);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        async submit() {
            this.cargarAlergias()
            this.PacienteData.TipoDeSangre = this.SelTipoSanguineo?.Codigo||''

            this.getionarDatos()
                .then(() => {
                    return this.validacionFormulario();
                })
                .then(() => {                                        
                    return this.gestionarPaciente();
                })
                .then(() => {                
                    this.$emit("ready", true);
                })
                .catch((error) => {
                    this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        title: 'Error en gestión de Paciente',
                        text: error,
                        clientWidth: 100,
                        accept: () => {
                            
                            this.$emit("ready", false);
                            
                        }
                    });
                })
        },
        cargarAlergias() {

            let listaAlergiaAlimentos = []
            let listaAlergiaMedicamentos = []

            this.SelAlergias[1].Seleccion.forEach((element) => {
                listaAlergiaAlimentos.push(element.Etiqueta)
            });

            if(listaAlergiaAlimentos)
                this.PacienteData.AlergiasComidas = listaAlergiaAlimentos.join(',')

            this.SelAlergias[0].Seleccion.forEach((element) => {
                listaAlergiaMedicamentos.push(element.Etiqueta)
            });

            if(listaAlergiaMedicamentos)
                this.PacienteData.AlergiasMedicamento = listaAlergiaMedicamentos.join(',')       
        },
        isValitInt(data) {
            if (this.isEmpty(data)) {
                data = data === null ? 0 : data;
            }
        },
        isEmpty(value) {
            if (typeof value === "undefined" || value === null || value === "")
                return true;
        },       
        addTag (newTag, listID) {

            
            const tag = {
                Etiqueta: newTag,
                Codigo: parseInt(this.SelAlergias[listID].ListaAlergias.at(-1).Codigo) + 1

            };

            this.SelAlergias[listID].ListaAlergias.push(tag)
            this.SelAlergias[listID].Seleccion.push(tag)
        },         
        onSelect_selectedGood(selectedOption, optionModel) {
        
            this.ListaActiva = optionModel;

        }, detectFocusOut() {
            let inView = false;

            const onWindowFocusChange = (e) => {
                if ({ focus: 1, pageshow: 1 }[e.type]) {
                    if (inView) return;
                    this.tabFocus = true;
                    inView = true;
                } else if (inView) {
                    this.tabFocus = !this.tabFocus;
                    inView = false;
                }
            };

            window.addEventListener('focus', onWindowFocusChange);
            window.addEventListener('blur', onWindowFocusChange);
            window.addEventListener('pageshow', onWindowFocusChange);
            window.addEventListener('pagehide', onWindowFocusChange);
        }



    }

}



</script>


<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container_div {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 100%;

}

.container_div>div {
    border: 1px solid #888;
    padding-top: 10px;
    padding-bottom: 10px;
}

/* hr {
    height: 12px;
    border: 0;
    box-shadow: inset 0 12px 12px -12px rgba(0, 0, 0, 0.5);
} */

.container_b {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 10%;
}

.container {
    max-width: 100%;
}
.radio-group.radio-group--inline div[data-radio-button] {
    width: auto;
}

.radio-group.radio-group--inline div[data-radio-button]:not(:first-of-type) {
    padding-left: var(--space-base);
}
</style>