<template>
<div>
    <DxDataGrid v-bind="DefaultDxGridConfiguration" :ref="dataGridRefKey" :data-source="Ordenes" key-expr="id" @edit-canceled="()=> {VistaPrevia=false}" :on-row-inserting="onRowInserting" :on-row-inserted="onRowInserted" @cell-prepared="onCellPrepared">
        <DxToolbar>
            <DxItem name="groupPanel" />
            <DxItem name="searchPanel" />
            <DxItem location="after" template="opcionesTemplate" />
        </DxToolbar>

        <DxGroupPanel :visible="true" />
        <DxGrouping :auto-expand-all="false" />

        <DxEditing v-bind="DxEditing" :changes.sync="Cambios" :use-icons="true">
            <DxPopup v-bind="popupConf" :title="VistaPrevia?'Visualización de orden médica':'Registro de orden médica'" :toolbarItems="VistaPrevia?[]:PopUpToolBarItems" />
            <DxForm label-mode="floating" validation-group="">
                <DxItem data-field="OrdenMedica" :col-span="2" :is-required="true" :label="{text:''}" />
            </DxForm>
        </DxEditing>

        <DxColumn :width="50" cell-template="eyeButton" alignment="center" :allow-reordering="false" />
        <DxColumn :width="50" cell-template="checkButton" alignment="center" :allow-reordering="false" />

        <DxColumn :width="150" :minWidth="100" data-field="Estado" :allow-editing="false" />
        <DxColumn :width="150" :minWidth="100" data-field="Fecha" caption="Fecha de orden" format="dd/MM/yyyy HH:mm:ss" data-type="datetime" sort-order="desc" :allow-editing="false" :allow-adding="false" />
        <DxColumn :width="700" :minWidth="100" data-field="OrdenMedica" caption="Descripción orden médica" :allowSorting="false" :allowHeaderFiltering="false" :allow-editing="!VistaPrevia" editor-type="dxTextArea" edit-cell-template="myTextArea" :validationRules="[{message:'Debe ingresar una orden médica', type:'required'}]" :customizeText="(e) => this.$saltos_tab_to_html(e.value)" :encode-html="false" />
        <DxColumn :width="150" :minWidth="100" data-field="Usuario" caption="Usuario" :allow-editing="false" />
        <DxColumn :width="150" :minWidth="100" data-field="Puesto" caption="Puesto" :allow-editing="false" />

        <template #opcionesTemplate>
            <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[{text:'Órdenes Médicas', reportName: 'Órdenes Médicas'}]" @add="addRow" @refresh="Cargar" :report-param="$props" :showItems="['add', 'exportPdf', 'refresh']">
                <DxButton id="botonBarra" width="36" height="36" :hint="verOrdenesPendientes ? 'Ver todas las órdenes' : 'Ver órdenes pendientes'" type="pendientes" @click="ordenesPendientes">
                    <font-awesome-icon :icon="['fas', verOrdenesPendientes?'clipboard-list':'clipboard']" style="font-size: 18px;" />
                </DxButton>
            </ExpedienteGridToolBar>
        </template>
        <template #myTextArea="{ data: cellInfo }">
            <div>
                <DxTextArea :value="TxtOrdenMedica" :read-only="VistaPrevia" :height="430" label="Descripción de la orden:" label-mode="floating" :on-value-changed="(e)=> {
                        cellInfo.setValue(e.value)
                        TxtOrdenMedica=e.value
                        }" @key-up="(e)=> {
                        if(e.event.key ==='Escape')
                            dataGrid.cancelEditData()
                        }">
                </DxTextArea>
            </div>
        </template>
        <template #eyeButton="{data: info}">
            <div style="display: flex; flex-wrap: wrap; justify-content: center;">
                <vs-tooltip text="Cambiar estado" position="bottom" style="cursor: pointer;">
                    <font-awesome-icon :icon="['fas', 'eye']" style="font-size: 20px; color: #337ab7;" @click="clickEye(info)" />
                </vs-tooltip>
            </div>
        </template>
        <template #checkButton="{data: info}">
            <div style="display: flex; flex-wrap: wrap; justify-content: center;">
                <vs-tooltip text="Cambiar estado" position="bottom" style="cursor: pointer;">
                    <font-awesome-icon :icon="['fas', 'list-check']" style="font-size: 20px; color: #337ab7;" @click="clickCheck(info)" />
                </vs-tooltip>
            </div>
        </template>
    </DxDataGrid>
    <InfoPopup :visible.sync="ModalCumplimiento" :width="'80%'" :height="'80%'" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Cumplimiento de Ordenes" :showCloseButton="true" @hiding="onHiding">
        <template #content>
            <DxScrollView :scroll-by-conent="true">
                <div>
                    <CumplimientoOrdenes v-bind="{
                ...$props,
                ...OrdenMedicaSeleccionada
                }" @cancelOrdenes="ModalCumplimiento=false" @refreshOrdenes="Cargar" />
                </div>
            </DxScrollView>
        </template>
    </InfoPopup>
</div>
</template>

<script>
import EXPBase from './EXPBase.vue'
import DxDataGrid, {
    DxToolbar,
    DxColumn,
    DxEditing,
    DxForm,
    DxPopup,
    DxGrouping,
    DxGroupPanel
} from 'devextreme-vue/data-grid'
import {
    DxItem
} from 'devextreme-vue/form'
import {
    DxTextArea
} from 'devextreme-vue/text-area'
import {
    DefaultDxGridConfiguration
} from './data'
import {
    ObtenerPlantilla
} from './data'
import {
    DxPopup as InfoPopup
} from 'devextreme-vue/popup'
import {
    DxScrollView
} from 'devextreme-vue/scroll-view'
import DxButton from 'devextreme-vue/button'

const dataGridRefKey = 'gridOrdenesMedicas'

export default {
    name: 'OrdenesMedicas',
    extends: EXPBase,
    components: {
        DxDataGrid,
        DxItem,
        DxToolbar,
        DxEditing,
        DxForm,
        ExpedienteGridToolBar: () => import("./ExpedienteGridToolBar.vue"),
        DxPopup,
        DxColumn,
        DxTextArea,
        DxGrouping,
        DxGroupPanel,
        CumplimientoOrdenes: () => import('./EXP016.vue'),
        InfoPopup,
        DxScrollView,
        DxButton
    },

    data() {
        return {
            Cambios: [],
            TxtOrdenMedica: '',
            dataGridRefKey,
            DefaultDxGridConfiguration,
            VistaPrevia: false,
            Ordenes: [],
            DxEditing: {
                allowUpdating: false,
                allowAdding: true,
                allowDeleting: false,
                mode: "popup",
            },
            PopUpToolBarItems: [{
                    widget: "dxButton",
                    toolbar: "bottom",
                    options: {
                        widget: 'dxButton',
                        hint: 'Carga la plantilla de orden de Ingreso',
                        text: 'Orden de ingreso',
                        icon: 'import',
                        onClick: () => {
                            ObtenerPlantilla('2', om => this.TxtOrdenMedica = om)
                        },
                    },
                    stylingMode: 'text',
                    showText: 'inMenu',
                    locateInMenu: 'always',
                    location: "before",
                },
                {
                    widget: "dxButton",
                    toolbar: "bottom",
                    options: {
                        widget: 'dxButton',
                        hint: 'Carga la plantilla de órdenes post-operatorias',
                        text: 'Post-operatorias',
                        icon: 'import',
                        stylingMode: 'text',
                        showText: 'inMenu',
                        onClick: () => {
                            ObtenerPlantilla('3', str => this.TxtOrdenMedica = str)
                        },
                    },
                    locateInMenu: 'always',
                    location: "before",
                },
                {
                    widget: "dxButton",
                    toolbar: "bottom",
                    options: {
                        widget: 'dxButton',
                        hint: 'Borra todo el texto del editor',
                        text: '',
                        icon: 'fas fa-broom',
                        stylingMode: 'text',
                        onClick: () => {
                            this.TxtOrdenMedica = ''
                        }
                    },
                    showText: 'always',
                    location: "before",
                    locateInMenu: 'always',
                },
                {
                    widget: "dxButton",
                    toolbar: "bottom",
                    options: {
                        text: 'Guardar',
                        type: 'success',
                        useSubmitBehavior: true,
                        onClick: () => {
                            this.dataGrid.saveEditData()
                        }
                    },
                    location: "after",
                },
                {
                    widget: "dxButton",
                    toolbar: "bottom",
                    options: {
                        text: 'Cancelar',
                        type: 'danger',
                        useSubmitBehavior: true,
                        onClick: () => {
                            this.dataGrid.cancelEditData()
                        }
                    },
                    location: "after",
                },
            ],
            OrdenMedicaSeleccionada: {},
            ModalCumplimiento: false,
            verOrdenesPendientes: false,
        }
    },
    methods: {
        Cargar() {
            if (this.ExpedienteCargado)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaOrdenMedica', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    this.Ordenes = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            IdOrdenMedica: x.IdOrdenmedica,
                            IdHistorial: x.IdHistorial,
                            Fecha: x.FechaOrden,
                            OrdenMedica: this.$limpiar_saltos_tabulares(x.OrdenMedica),
                            Usuario: x.Nombre,
                            Puesto: x.Puesto,
                            OrdenesCumplidas: x.OrdenesCumplidas,
                            TotalOrdenes: x.TotalOrdenes,
                            Estado: (x.OrdenesCumplidas > 0) ? 'Cumplida' : 'Pendiente'
                        }
                    })
                    this.refreshDataGrid()
                })
            else {
                this.Ordenes = []
            }
        },
        addRow() {
            this.TxtOrdenMedica = ''
            this.dataGrid.addRow()
        },
        onRowInserting(e) {
            e.cancel = new Promise((resolve, reject) => {
                this.axios.post('/app/v1_ExpedienteEvolucion/InsertarOrdenesMedicas', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    OrdenMedica: this.$reemplazar_tabulares(e.data.OrdenMedica),
                }).then((resp) => {
                    resp.data.codigo == 0 ? resolve(false) : resolve(true)
                }).catch((err) => {
                    reject(err.descripcion ? err.descripcion : err)
                })
            })
        },
        onRowInserted() {
            this.Cargar()
        },

        refreshDataGrid() {
            this.dataGrid.refresh()
            this.dataGrid.clearGrouping()
        },
        clickEye(obj) {
            this.VistaPrevia = true
            this.dataGrid.editRow(obj.row.rowIndex)
            this.TxtOrdenMedica = obj.row.data.OrdenMedica
        },
        clickCheck(obj) {
            this.OrdenMedicaSeleccionada = {
                idOrdenMedica: Number(obj.row.data.IdOrdenMedica),
                txtOrdenMedica: obj.row.data.OrdenMedica
            }
            this.ModalCumplimiento = true
        },
        onHiding() {
            this.OrdenMedicaSeleccionada = {
                idOrdenMedica: -1,
                txtOrdenMedica: ''
            }
        },
        onCellPrepared(e) {
            if (e.rowType === 'data' && e.column.dataField === 'Estado') {
                if (e.data.Estado === 'Cumplida') {
                    e.cellElement.style.cssText = "font-weight: bold;"
                }
            }
        },
        ordenesPendientes() {
            if (this.verOrdenesPendientes) {
                this.dataGrid.clearFilter();
            } else {
                let fecha = new Date(); //Fecha del día de hoy
                let fecha2 = new Date(); //Fecha de 2 días atrás
                fecha.setHours(23, 59, 59, 999) //La hora máxima será las 23:59 del día actual
                let dia = fecha.getTime() - (2 * 24 * 60 * 60 * 1000)

                fecha2.setTime(dia);
                fecha2.setHours(0, 0, 0, 0) //La hora mínima será las 00:00 de 2 días atrás

                this.dataGrid.filter([
                    ["Estado", "=", "Pendiente"], "and", ["Fecha", "<=", fecha], "and", ["Fecha", ">=", fecha2]
                ]);
            }
            this.verOrdenesPendientes = !this.verOrdenesPendientes
        }
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance
        },
    },
}
</script>

<style>
.dx-button.dx-button-pendientes {
    background-color: #FFF;
}

.dx-button-mode-contained.dx-button-pendientes .dx-icon {
    color: #672685;
}

.dx-button-mode-contained.dx-button-pendientes {
    color: #672685;
    border-color: #672685;
}
</style>
