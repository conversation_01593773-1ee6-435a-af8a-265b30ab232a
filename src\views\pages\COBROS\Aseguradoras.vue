<template>
<div class="mantenimiento-aseguradora-container">
    <DxDataGrid ref="gridAseguradoras" v-bind="GridConfiguration" :columns="gridColumns" :editing="gridEditing" :data-source="aseguradoras" :toolbar="gridToolbar" @init-new-row="onInitNewRow" @row-inserting="onRowInserting" @row-updating="onRowUpdating" @editor-preparing="onEditorPreparing" @editing-start="onEditingStart">
        <template #template-nota>
            <div class="nota-facturacion">
                <strong>NOTA:</strong>
                <p>Los datos para facturación son válidos únicamente para las facturas pre-impresas, las facturas electrónicas toman los datos de facturación de la SAT.</p>
                <p>Los seguros internacionales no utilizan este NIT, utilizan C/F.</p>
            </div>
        </template>
        <template #opcionesTemplate>
            <ExpedienteGridToolBar :visible="true" :showItems="['refresh', 'add']" :pdfExportItems="[]" @refresh="Cargar" @add="onAdd" />
        </template>
    </DxDataGrid>

    <DxPopup :visible.sync="visibleSeguros" :drag-enabled="false" :hide-on-outside-click="false" :show-close-button="true" :show-title="true" width="auto" :height="'auto'" title="Mantenimiento Pólizas de Seguro" content-template="polizas-content" :position="popupPosition">
        <template #polizas-content>
            <DxScrollView :scroll-by-conent="true" width="100%" height="100%">
                <div class="justify-center">
                    <h4 class="mb-5">{{ `[${aseguradoraSeleccionada?.Asegura}] ${aseguradoraSeleccionada?.Nombre}` }}</h4>
                    <Polizas :Aseguradora="aseguradoraSeleccionada" />
                </div>
            </DxScrollView>
        </template>
    </DxPopup>
</div>
</template>

<script>
import 'devextreme-vue/lookup'

import {
    catalogoTipoAseguradora,
    srtLenRule,
    smallMoneyRule,
    gridConfiguration,
    gridToolbar,
    buildFilterExpresion,
    setUpperCaseConfig,
} from './data.js'
import {
    popupPosition
} from '../SASI/data'
import validador_dpi_nit from '@/components/sermesa/funciones/SMVFormatsDocs'
import Polizas from './Polizas.vue'
import ExpedienteGridToolBar from '../EXPEDIENTE/ExpedienteGridToolBar.vue'
import bitacora from '@/components/sermesa/funciones/SMBitacora'
const catalogoCuentas = []
const lookupCuentas = {
    dataSource: catalogoCuentas,
    displayExpr: (x) => ''.concat(x.Codigo, ' - ', x.Nombre),
    valueExpr: 'Codigo',
    allowClearing: true,
}
const nivelPreciosRule = {
    max: 500,
    min: 0,
    type: 'range',
}

export default {
    name: 'MantenimientoAseguradoras',
    components: {
        Polizas,
        ExpedienteGridToolBar
    },
    data() {
        return {
            aseguradoras: null,
            aseguradoraSeleccionada: null,
            catalogoCuentas: null,
            tipoAseguradora: catalogoTipoAseguradora,
            visibleSeguros: false,
            popupPosition,
            GridConfiguration: gridConfiguration,
            gridToolbar,
            gridColumns: [{
                    dataField: 'Asegura',
                    caption: 'Código',
                    width: 80,
                    validationRules: [{
                        type: 'required'
                    }, srtLenRule('Código', 1, 3),
                    {
                        type: 'async',
                        reevaluate: false,
                        message: 'El código de aseguradora ya fue registrado anteriomente',
                        validationCallback: (e) => {
                            return new Promise((resolve, reject) => {
                                if (!this.editingState && this.aseguradoras.find(x=> x.Asegura == e.value))
                                    reject('El código de aseguradora ya fue registrado anteriomente')
                                else {
                                    resolve()
                                }
                            })

                        }
                    },
                    ]
                },
                {
                    dataField: 'Tipo', //local o extranjero'
                    lookup: {
                        dataSource: catalogoTipoAseguradora,
                        displayExpr: 'Nombre',
                        valueExpr: 'Codigo',
                    },
                    width: 80,
                    validationRules: [{
                        type: 'required'
                    }]
                },
                {
                    dataField: 'Nombre',
                    width: 'auto',
                    validationRules: [{
                        type: 'required'
                    }, srtLenRule('Nombre', 1, 60)],
                    calculateFilterExpression: buildFilterExpresion('Nombre')
                },
                {
                    dataField: 'Direccion',
                    caption: 'Dirección',
                    validationRules: [{
                        type: 'required'
                    }, srtLenRule('Dirección', 6, 80)],
                    visible: false,
                },
                {
                    dataField: 'Contacto',
                    width: 'auto',
                    validationRules: [srtLenRule('Contacto', 0, 60)],
                    calculateFilterExpression: buildFilterExpresion('Contacto')
                },
                {
                    dataField: 'Telefonos',
                    caption: 'Teléfonos',
                    validationRules: [srtLenRule('Teléfonos', 0, 23)],
                },
                {
                    dataField: 'NivelPreciosInt',
                    dataType: 'number',
                    caption: 'Internos',
                    visible: false,
                    validationRules: [nivelPreciosRule]
                },
                {
                    dataField: 'NivelPreciosEme',
                    dataType: 'number',
                    caption: 'Emergencia',
                    visible: false,
                    validationRules: [nivelPreciosRule]
                },
                {
                    dataField: 'NivelPreciosExt',
                    dataType: 'number',
                    caption: 'Externos',
                    visible: false,
                    validationRules: [nivelPreciosRule]
                },
                {
                    dataField: 'FechaCambioNiv',
                    caption: 'Fecha de Cambio',
                    dataType: 'date',
                    visible: false,
                },

                {
                    dataField: 'DescuentoInt',
                    dataType: 'number',
                    caption: 'Internos',
                    visible: false,
                    validationRules: [smallMoneyRule]
                },
                {
                    dataField: 'DescuentoEme',
                    dataType: 'number',
                    caption: 'Emergencia',
                    visible: false,
                    validationRules: [smallMoneyRule]
                },
                {
                    dataField: 'DescuentoExt',
                    dataType: 'number',
                    caption: 'Externos',
                    visible: false,
                    validationRules: [smallMoneyRule]
                },
                {
                    dataField: 'Nit',
                    caption: 'NIT',
                    validationRules: [{
                        type: 'custom',
                        reevaluate: true,
                        validationCallback: (e) => {
                            if (e.value && e.value.toUpperCase() != 'C/F' && !validador_dpi_nit.nitValido(e.value.trim()) && e.data.Tipo == 'L') {
                                e.rule.message = 'Ingrese NIT válido'
                                return false
                            }
                            e.rule.message = ''
                            return true
                        }
                    }, ],
                },
                {
                    dataField: 'NombreFactura',
                    caption: 'Nombre',
                    visible: false,
                    validationRules: [srtLenRule('El nombre da la factura', 5, 60)],
                },
                {
                    dataField: 'DireccionFactura',
                    caption: 'Dirección',
                    visible: false,
                    validationRules: [srtLenRule('Dirección factura', 5, 60)],
                },
                {
                    dataField: 'PagoDirecto',
                    caption: 'Paga directo al Médico',
                    visible: false,
                },
                {
                    dataField: 'ValidarNivelPrecio',
                    visible: false,
                },
                {
                    dataField: 'ValidarProcesoRecalculo',
                    visible: false,
                },
                {
                    dataField: 'UnidadPermisoDefault',
                    visible: false,
                },
                {
                    dataField: 'EmitirFacturaAlSeguro',
                    visible: false,
                },
                {
                    dataField: 'ValidarAfiliacionAlAsignar',
                    visible: false,
                },
                {
                    dataField: 'UnidadAutorizarCredito',
                    visible: false,
                },
                {
                    dataField: 'ContaDebe',
                    caption: 'Cuenta por Cobrar',
                    visible: false,
                    lookup: lookupCuentas,
                },
                {
                    dataField: 'ContaHaber',
                    visible: false,
                },
                {
                    dataField: 'GrupoFacturacion',
                    visible: false,
                },
                {
                    dataField: 'PagaCuentaAjena',
                    visible: false,
                },
                {
                    dataField: 'Email',
                    validationRules: [srtLenRule('Email', 0, 60)],
                },
                {
                    type: 'buttons',
                    caption: 'Acciones',
                    width: 80,
                    fixed: true,
                    buttons: [
                        'edit',
                        {
                            icon: 'bulletlist',
                            text: 'Pólizas',
                            onClick: this.onEditPoliza
                        },
                    ],
                },
            ],

            gridEditing: {
                ...gridConfiguration.editing,
                form: {
                    items: [{
                            itemType: 'group',
                            caption: 'Datos generales',
                            items: [{
                                    itemType: 'simple',
                                    name: 'Asegura',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'Nombre',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'Direccion',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'Telefonos',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'Contacto',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'Tipo',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'PagoDirecto',
                                },
                            ],
                        },

                        {
                            itemType: 'group',
                            caption: 'Facturación',
                            items: [{
                                    itemType: 'simple',
                                    name: 'Nit',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'NombreFactura',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'DireccionFactura',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'Email',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'ContaDebe',
                                },
                                {
                                    itemType: 'simple',
                                    template: 'template-nota',
                                },
                            ],
                        },
                        {
                            itemType: 'group',
                            caption: 'Nivel de Precios',
                            items: [{
                                    itemType: 'simple',
                                    name: 'NivelPreciosInt',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'NivelPreciosEme',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'NivelPreciosExt',
                                },
                                {
                                    itemType: 'simple',
                                    name: 'FechaCambioNiv',
                                },
                            ],
                        },
                        {
                            itemType: 'group',
                            caption: 'Descuentos',
                            items: [{
                                    itemType: 'simple',
                                    dataField: 'DescuentoInt',
                                    caption: 'Internos',
                                },
                                {
                                    itemType: 'simple',
                                    dataField: 'DescuentoEme',
                                    caption: 'Emergancia',
                                },
                                {
                                    itemType: 'simple',
                                    dataField: 'DescuentoExt',
                                    caption: 'Externos',
                                },
                            ],
                        },
                    ]
                },
            },

            validationRules: {
                Nit: {
                    type: 'custom',
                    reevaluate: true,
                    validationCallback: (e) => {
                        if (e.value && e.value.toUpperCase() != 'C/F' && !validador_dpi_nit.nitValido(e.value.trim())) {
                            e.rule.message = 'Ingrese NIT válido'
                            return false
                        }
                        e.rule.message = ''
                        return true
                    }
                },
            },

            editingState: false, //bandera para saber si se esta editando el registro
            objBitacora: null,
        }
    },
    methods: {
        Cargar() {
            this.axios.post("/app/v1_cobros/CatalogoAseguradoras", {}).then(resp => {
                this.aseguradoras = resp.data.json
            })
        },
        CargarCuentas() {
            this.axios.post("/app/v1_cobros/CatalogoCuentasContables", {}).then(resp => {
                catalogoCuentas.splice(0, catalogoCuentas.length, ...resp.data)
            })
        },
        onAdd() {
            this.editingState = false
            this.GridAseguradoras.addRow()
        },
        onRowInserting(e) {
            e.cancel = true

            this.InitBitacora({
                Empresa: e.data.Empresa,
                Asegura: e.data.Asegura,
            })
            this.axios.post('/app/v1_cobros/InsertarAseguradora', e.data)
                .then(() => {
                    this.Bitacora(e.data, 'Inserción')
                    this.GridAseguradoras.cancelEditData()
                    this.Cargar()
                })
        },
        onRowUpdating(e) {
            e.cancel = true
            this.axios.post('/app/v1_cobros/ActualizarAseguradora', {
                ...e.oldData,
                ...e.newData
            }).then(() => {
                this.Bitacora(e.newData, 'Modificación')
                this.GridAseguradoras.cancelEditData()
                this.Cargar()
            })
        },
        onEditingStart(e) {
            this.editingState=true
            this.InitBitacora(e.data)
        },
        onEditPoliza(e) {
            this.aseguradoraSeleccionada = e.row.data
            this.visibleSeguros = true
        },
        onInitNewRow(e) {
            //Colocar aqui la incializacion de valores para filas nuevas
            e.data.PagoDirecto = false
            e.data.Empresa = this.$store.state.sesion.sesion_empresa_unificadora
        },
        onEditorPreparing(e) {
            if ('Asegura' == e.dataField && this.editingState) {
                e.editorOptions.readOnly = true
            }
            //uppercase de los editores
            if (['Asegura'].includes(e.dataField)) {
                setUpperCaseConfig(e.editorOptions)
            }
        },
        InitBitacora(data) {
            this.objBitacora = data
            this.objBitacora.tipobitacora = 'Tipo'
            bitacora.registrar(this.objBitacora, {
                Empresa: data.Empresa,
                Asegura: data.Asegura,
                Codigo: data.Codigo,
            })
        },
        Bitacora(newData, tipobitacora) {
            return new Promise((resolve, reject) => {
                Object.keys(newData).forEach(key => {
                    if(key != '__KEY__')
                        this.objBitacora[key] = newData[key]
                })
                this.objBitacora.tipobitacora = tipobitacora
                this.axios.post('/app/bitacora/registro_bitacora', {
                    llave: {
                        Empresa: this.objBitacora.Empresa,
                        Asegura: this.objBitacora.Asegura,
                    },
                    tabla: 'Aseguradoras',
                    info: bitacora.obtener()
                }).then((resp) => {
                    resolve(resp)
                }).catch((err) => reject(err))
            })
        },
    },
    computed: {
        GridAseguradoras() {
            return this.$refs['gridAseguradoras'].instance
        }
    },
    beforeMount() {
        this.Cargar()
        this.CargarCuentas()
    }
}
</script>

<style>
.nota-facturacion {
    background: rgb(241, 243, 244, 1);
    border-radius: 10px;
    padding: 10px;
}

.mantenimiento-aseguradora-container .dx-datagrid-headers td,
.mantenimiento-seguros-container .dx-datagrid-headers td {
    background-color: aliceblue;
    color: black;
    font-weight: bold;
}
</style>
