<!-- TiposDescuento.vue -->

<template>
    <div id="app">

        <div class="tipos-descuento polizas_container">


            <div class="polizas_container-header tipos">
                <vs-col vs-w="12">
                    <div class="radio-group" v-show="AdmisionData.TipoAdmision !== 0">
                        <BaseRadioButtonGroup :options="radioOptions" 
                                               v-model="tipoDescuentoSel"
                                               :groupName="'tiposDescuento'" 
                                               :key="radioKey" 
                                               :selected-option="this.tipoDescuentoSel.Descripcion"/>
                    </div>
                </vs-col>
            </div>


            <vs-col vs-w="12">

                <div v-show="this.tipoDescuentoSel.Descripcion === this.TipoDescuentoSaludSiempre">

                <PolizaSaludSiempre  @poliza-seleccionada="obtenerPolizaSeleccionada"
                                     :id-paciente="parseInt(Codigo)"
                                     >
                </PolizaSaludSiempre>

                </div>
            </vs-col>

            <vs-col vs-w="12">

                <div v-show="this.tipoDescuentoSel.Descripcion === this.TipoDescuentoPaqueteQuirurgico">

                <PaquetesQuirurgicos  @paquete-seleccionado="obtenerPaqueteSeleccionado"
                                      :id-paciente="parseInt(Codigo)"
                                    >
                </PaquetesQuirurgicos>

                </div>
            </vs-col>

            <vs-row>

                <vs-col vs-w="8">
                    <div v-show="this.tipoDescuentoSel.Descripcion === this.TipoDescuentoSeguro">

                        <vs-col vs-w="2" class="pr-2">
                            <vs-input label="Codigo" class="w-full" v-model="tipoDescuentoSelected.CodigoSeguro" readonly />
                        </vs-col>

                      <SM-Buscar v-model="tipoDescuentoSelected.CodigoSeguro"
                                label="Aseguradora"
                                api="app/Ajenos/ConsultaAseguradoras"
                                api_campo_respuesta_mostrar="Aseguradora"
                                :api_campos="['Codigo', 'Aseguradora' ]"
                                api_campo_respuesta="Codigo"
                                :api_titulos="['Codigo', 'Aseguradora']"
                                :api_preload="true"
                                :api_campo_respuesta_estricto="false"
                                :disabled_texto="true"
                                :callback_buscar="CosultarDatosPoliza().Consultar" 
                        />
                    </div>
                </vs-col>


                <vs-col vs-w="8">
                    <div v-show="this.tipoDescuentoSel.Descripcion === this.TipoDescuentoPaqueteDiagnostico">

                        <vs-col vs-w="2" class="pr-2">
                            <vs-input label="Codigo" class="w-full" v-model="paqueteDiagnostico" readonly />
                        </vs-col>

                        <SM-Buscar v-model="paqueteDiagnostico" 
                                label="Paquete" 
                                api="app/v1_admision/ConsultaPaquetesDiagnostico"
                                 api_campo_respuesta_mostrar="Nombre"                                 
                                :api_campos="['Codigo', 'Nombre']"
                                api_campo_respuesta="Codigo"
                                :api_titulos="['Codigo', 'Nombre']"
                                :api_campo_respuesta_estricto="false"
                                :disabled_texto="true" 
                                :api_preload="true" 
                                :callback_buscar="ConsultarDatosPaquete().Consultar"
                                
                        />
                    </div>
                </vs-col>
            </vs-row>

        </div>


    </div>
</template>


  
<script>
import BaseRadioButtonGroup from "@/components/sermesa/global/radioButtons/BaseRadioButtonGroup";
import PolizaSaludSiempre from "/src/components/sermesa/modules/admisiones/ConsultaSaludSiempre.vue"
import PaquetesQuirurgicos from "/src/components/sermesa/modules/admisiones/ConsultaPaquetesQuirurgicos.vue"

import { mapFields } from "vuex-map-fields";


const PAQUETE = 1
const TIPO_DESCUENTO_SEGURO = 'Seguro'
const TIPO_DESCUENTO_SALUD_SIEMPRE = 'Salud Siempre/Percapitados'
const TIPO_DESCUENTO_PAQUETE_QUIRURGICO = 'Paquete Quirúrgico'
const TIPO_DESCUENTO_PAQUETE_DIAGNOSTICO = 'Paquete Diagnostico'


export default {
    name: "TiposDescuento",
    props: {
        showOptions: {
            type: Boolean,
            default: true
        },
        TipoAdmision: {
            required: true,
            type: Number
        },
        TipoDescuento: {
            required: false,
            type: String,
            default:''
        }
    },
    components: {
        BaseRadioButtonGroup,
        PolizaSaludSiempre,
        PaquetesQuirurgicos
    },
    data() {
        return {
            radioOptions: [],
            tiposDescuento: [],
            tipoDescuentoSelected: [{
                TipoAdmision: '',
                TipoDescuento: '',
                Descripcion: '',
                NivelPrecios: '',
                CodigoSeguro: '',
                CodigoAfiliado: '',

            }],
            tipoDescuentoSel: '',
            Paquete: PAQUETE,            
            Seguros: false,
            SaludSiempre: false,
            CodigoAseguradora: '',
            CodigoPaquete: '',
            NombreMedico: '',
            radioKey: 0,
            aplicarTipoDescuento:'',
            paqueteDiagnostico:'',
            TipoDescuentoSeguro: TIPO_DESCUENTO_SEGURO,
            TipoDescuentoSaludSiempre: TIPO_DESCUENTO_SALUD_SIEMPRE,
            TipoDescuentoPaqueteQuirurgico: TIPO_DESCUENTO_PAQUETE_QUIRURGICO,
            TipoDescuentoPaqueteDiagnostico:TIPO_DESCUENTO_PAQUETE_DIAGNOSTICO


        };
    },
    activated(){        
        
  
    },
    mounted() {

        
        if (!this.AdmisionData.TipoAdmision)
        return

        this.ConsultarTiposDescuento().then(() => {
            if (this.radioOptions.length > 0) {
                let valorPredefinido = this.radioOptions.find(option => option.Descripcion === this.TipoDescuento);
                if (valorPredefinido) {
                    this.tipoDescuentoSel = valorPredefinido;
                }
            }
        });
    },
    computed: {
        ...mapFields('paciente', ['PacienteData.Codigo']),
        ...mapFields('admision', ['AdmisionData'])
    },
    watch: {
        tipoDescuentoSel(newValue) {
            
            if(newValue.Descripcion == this.TipoDescuentoPaqueteQuirurgico){
                this.AdmisionData.PaqueteQx =true 
            }
            else {
                this.AdmisionData.PaqueteQx =false 
            }

            this.AdmisionData.TipoDescuento = newValue.TipoDescuento                             
            this.$emit("selected-tipodes-changed", newValue.TipoDescuento);
        },
        TipoAdmision(newTipoAdmision) {
            const tipoAdmisionBuscado = '' + newTipoAdmision;
            this.radioOptions = this.obtenerDescripciones(this.tiposDescuento, tipoAdmisionBuscado);
            this.tipoDescuentoSel = '';
            this.radioKey++;
        }
    },
    methods: {
        async ConsultarTiposDescuento() {
            const resp = await this.axios.post('/app/v1_admision/ObtenerTiposDescuento', {
                Empresa: '',
                Codigo: '',
                TipoAdmision: this.TipoAdmision
            })
            this.tiposDescuento = resp.data.json
            this.radioOptions = this.tiposDescuento
        },
        obtenerDescripciones: (arr, tipoAdmision) => {
            const descripciones = [];
            let indice = 1;
            arr.forEach((obj) => {
                if (obj.TipoAdmision === tipoAdmision) {
                    descripciones.push({
                        Id: indice,
                        Descripcion: obj.Descripcion,
                        TipoDescuento: obj.TipoDescuento
                    });
                    indice++;
                }
            });
            return descripciones;
        },
        obtenerPolizaSeleccionada(poliza) {    
            this.AdmisionData.NivelPrecios = poliza.NivelPrecios
            this.AdmisionData.Seguro = poliza.Codigo
            this.AdmisionData.IdAfiliado = poliza.CodigoAfiliado
            this.AdmisionData.Otros = poliza.CodigoPlan
        },
        obtenerPaqueteSeleccionado(paquete){

            if (paquete.Id ){
                this.AdmisionData.PaqueteQx = true
                this.AdmisionData.Paquete = paquete.Id
            }
        },
        CosultarDatosPoliza() {
            return {
                Consultar: (datos) => {
                    this.AdmisionData.NivelPrecios = datos.NivelPrecios
                    this.AdmisionData.Seguro = datos.Codigo
                    this.AdmisionData.IdAfiliado = ''
                    this.AdmisionData.Otros = datos.CodigoPlan
                }
            }
        },
        ConsultarDatosPaquete(){
            return {
                Consultar: (datos) => {
                    this.AdmisionData.Paquete = datos.Codigo

                }
            }
        }
    }
};
</script>

<style scoped>
span {
    font-size: 25px;
}

.tipos-descuento {
    display: flex;
    flex-flow: row wrap;
    justify-content: space-around;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;
    border-radius: 5px;
    margin: 5px;
}


div.polizas_container {

    height: 100%;
    background: #ffffff;
    border-radius: 5px;
    padding: 15px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100%, 1fr));
    grid-gap: 2px;

}

div.polizas_container-header {
    position: relativ;
    background: linear-gradient(9deg, rgba(0, 143, 190, 0.33657212885154064) 26%, rgba(0, 143, 190, 0.09567577030812324) 57%);
    border-radius: 5px;
    padding: 20px;
    font-size: 14px;
}

.radio-group {
    display: inline-block;
    margin-right: 10px;
}
</style>
