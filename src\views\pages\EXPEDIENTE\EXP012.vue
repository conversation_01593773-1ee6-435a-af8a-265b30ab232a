<template>
<div id="Exp12">
    <form @submit="handleSubmit">
        <DxForm :form-data.sync="PlanEgreso" label-location="top" validation-group="validacionPlanEgreso">
            <template #avatar-template="{}">
                <font-awesome-icon :icon="['fas', 'person-walking-arrow-right']" class="form-avatar p-5" />
            </template>
            <DxGroupItem :col-count="7" css-class="first-group">
                <DxSimpleItem template="avatar-template" />
                <DxGroupItem :col-span="6" :col-count="2" css-class="second-group">
                    <DxSimpleItem :col-span="2" data-field="NombreAjeno" :editor-options="{maxLength: 100}">
                        <DxLabel text="Nombre ajeno" />
                        <DxRequiredRule />
                        <DxStringLengthRule :max="100" message="Máximo de 100 caracteres" />
                    </DxSimpleItem>
                    <DxSimpleItem :col-span="2" data-field="ProximaCita">
                        <DxLabel text="Próxima cita" />
                    </DxSimpleItem>
                </DxGroupItem>

                <DxGroupItem :col-span="7" :col-count="3" css-class="second-group">
                    <DxSimpleItem :col-span="1" :editor-options="{maxLength:500, height: 200,}" data-field="TipoActividad" caption="Tipo actividad" editor-type="dxTextArea">
                        <DxLabel text="Tipo actividad" />
                    </DxSimpleItem>
                    <DxSimpleItem :col-span="1" :editor-options="{maxLength:500, height: 200,}" data-field="Dieta" editor-type="dxTextArea" />
                    <DxSimpleItem :col-span="1" :editor-options="{maxLength:500, height: 200,}" data-field="Medicamentos" editor-type="dxTextArea" />
                </DxGroupItem>

            </DxGroupItem>
            <DxButtonItem :visible=" StatusAdmision === 'A'" :button-options="{
                        text: PlanEgreso.IdEgreso? 'Actualizar' : 'Guardar',
                        type: 'success',
                        icon: 'save',
                        useSubmitBehavior: true,
                        width: 175
                    }" horizontal-alignment="center" verical-alignment="buttom" css-class="plan-egreso-buttons" />
            <DxButtonItem :visible="Boolean(PlanEgreso.IdEgreso)" :button-options="{
                        text: 'Generar Reporte',
                        type: 'danger',
                        icon: 'fas fa-file-pdf',
                        stylingMode:'outlined',
                        useSubmitBehavior: false,
                        width: 175,
                        onClick: () => {
                            this.$reporte_modal({
                                Nombre: 'Egreso',
                                Opciones: $props
                            })
                        }
                    }" horizontal-alignment="center" verical-alignment="buttom" css-class="plan-egreso-buttons" />
        </DxForm>
    </form>
</div>
</template>

<script>
import EXPBase from './EXPBase.vue'
import {
    DxForm,
    DxSimpleItem,
    DxGroupItem,
    DxButtonItem,
    DxLabel,
} from 'devextreme-vue/form';
import 'devextreme-vue/text-area'
import {
    DxRequiredRule,
    DxStringLengthRule
} from 'devextreme-vue/data-grid';
export default {
    name: 'PlanEgreso',
    extends: EXPBase,
    components: {
        DxForm,
        DxGroupItem,
        DxSimpleItem,
        DxButtonItem,
        DxRequiredRule,
        DxStringLengthRule,
        DxLabel
    },
    data() {
        return {
            PlanEgreso: {
                IdEgreso: null,
                NombreAjeno: '',
                TipoActividad: '',
                Dieta: '',
                Medicamentos: '',
                ProximaCita: '',
            }
        }
    },
    methods: {
        Cargar() {
            if (this.ExpedienteCargado)
                this.axios.post("/app/v1_ExpedienteEvolucion/BusquedaPlanEgreso", {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.PlanEgreso) {
                        this.PlanEgreso = resp.data.PlanEgreso
                    }
                })
        },
        Grabar() {
            this.PlanEgreso = this.$reemplazar_tabulares_objeto(this.PlanEgreso, /\t/g, '   ')
            const postData = {
                ...this.PlanEgreso
            }
            postData.SerieAdmision = this.SerieAdmision
            postData.Admision = this.CodigoAdmision
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarPlanEgreso", postData)
                .then(resp => {
                    if (resp.data.codigo == 0 && resp.data.IdEgreso) {
                        this.PlanEgreso = resp.data
                        this.$emit('onSaved', this.PlanEgreso)
                    }
                })
        },
        handleSubmit(e) {
            e.preventDefault()
            this.Grabar()
        },
    },
}
</script>

<style scoped>
#Exp12 .form-avatar {
    height: 72px;
    width: 72px;
    margin-right: 10px;
    border: 1px solid #d2d3d5;
    border-radius: 50%;
    background-image: "fas fa-person-walking-arrow-right ";
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    color: rgba(41, 128, 185, .9) !important;
    background-color: rgba(191, 191, 191, 0.1);
    align-items: center;
    position: relative;
}
</style>
