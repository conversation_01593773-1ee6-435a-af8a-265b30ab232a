<template>
<vx-card title="Ordenes de Compra">
    <div class="content content-pagex">
        <form>
            <div style="margin:5px" class="flex flex-wrap">

                <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                    <div class="flex flex-wrap">
                        <div style="float:left;margin:20px" class=" xs:w-full md:w-1/5 lg:w-1/5 xl:w-1/5">
                            <vs-button v-if="habilitarNueva == false" disabled="false" color="primary" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Ventana_Emergente_nuevo()"> Nueva</vs-button>
                            <vs-button v-if="habilitarNueva == true" color="primary" type="filled" icon-pack="fas" icon="fa-plus" @click="Abrir_Ventana_Emergente_nuevo()"> Nueva</vs-button>
                        </div>
                        <div div style="margin-left:5px;margin-right:10px" class="xs:w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
                            <ValidationProvider name="Agencia" v-slot="{ errors }">
                                <label class="typo__label">Departamentos</label>
                                <multiselect v-model="cbDepartamentos" :options="listaDepartamentos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="departamento_seleccionada" placeholder="Seleccionar" @input="onChangedepartamento" :danger="errors.length > 0" :danger-text="errors.length > 0 ? errors[0] : null">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                    </div>
                </div>
                <!------BÚSQUEDA DE ORDENES DE COMPRA--------->
                <div class="xs:w-full md:w-8/12 lg:w-8/12 xl:w-8/12">
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/12">
                            <label class="typo__label">Estado Orden de Compra</label>
                            <multiselect v-model="cbListaOperacion" :options="listaEstado" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="Operacion_seleccionada" placeholder="Seleccionar" @input="onChangeEstado">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>
                        <div div style="margin-left:10px;margin-right:10px" class="xs:w-full md:w-2/6 lg:w-2/6 xl:w-2/6">
                            <label class="typo__label">Fecha Inicio:</label>
                            <vs-input type="date" v-model="fechaInicio" />
                        </div>
                        <div class="xs:w-full md:w-3/6 lg:w-4/6 xl:w3/6">
                            <label class="typo__label">Fecha Final:</label>
                            <vs-input type="date" v-model="fechaFinal" />
                        </div>
                        <div style="float:left;margin: 20px" class=" md:w-1/6 lg:w-1/6 xl:w-1/6">
                            <vs-button color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="Consulta_autorizaciones()"> Búsqueda</vs-button>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin: 15px" class="flex flex-wrap">
                <div class="xs:w-full md:w-4/12 lg:w-4/12 xl:w-4/12">
                    <div class="flex flex-wrap">
                        <div style="margin-left:10px;margin-right:20px" class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/6">
                            <ValidationProvider name="selectProv">
                                <label class="typo__label">Proveedor</label>
                                <multiselect v-model="cbProveedor" :options="listaProveedores" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_proveedor" placeholder="Búsqueda" @input="onChangeProveedor">
                                    <span slot="noOptions">Lista no disponible.</span>
                                </multiselect>
                            </ValidationProvider>
                        </div>
                    </div>
                </div>

                <div class="xs:w-full md:w-8/12 lg:w-8/12 xl:w-8/12">
                    <div class="flex flex-wrap">
                        <div class="xs:w-full md:w-5/6 lg:w-5/6 xl:w-5/12">
                            <label class="typo__label">Estado de Validación</label>
                            <multiselect v-model="cb_lista_recepcion" :options="lista_recepcion" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="EstadoRecepcion" placeholder="Seleccionar" @input="onChangeRecepcion">
                                <span slot="noOptions">Lista no disponible.</span>
                            </multiselect>
                        </div>
                        <div style="margin-left:10px;margin-right:20px">
                            <ValidationProvider name="diasCredito" rules="required|numero_min:0|numero_entero">
                                <label class="typo__label">De Orden No.</label>
                                <vs-input type="number" v-model="deOrden" />
                            </ValidationProvider>
                        </div>
                        <div class="xs:w-full md:w-3/6 lg:w-3/6 xl:w-3/6">
                            <ValidationProvider name="pagos" rules="required|numero_min:0|numero_entero">
                                <label class="typo__label">A Orden No.</label>
                                <vs-input type="number" v-model="aOrden" />
                            </ValidationProvider>
                        </div>
                        <div class=" md:w-1/6 lg:w-1/6 xl:w-1/6" style="margin-left:10px;margin-right:20px">
                            <div class="w-full" style="float:left;margin: 10px">
                                <vs-radio v-model="idTipoFSeleccionado" vs-name="idTipoFSeleccionado" vs-value="S"> Servicio</vs-radio>
                            </div>
                            <div class="w-full" style="float:left;margin: 10px">
                                <vs-radio v-model="idTipoFSeleccionado" vs-name="idTipoFSeleccionado" vs-value="P"> Bien</vs-radio>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </form>
        <label class="label-sizem">Si alguno de los filtros no es seleccionado, el resultado será obtener toda la información registrada. </label>
        <br>
        <vs-divider></vs-divider>
        <div class="flex flex-wrap">
            <div class="xs:w-full" align="left">
                <vs-button style="float:right;margin: 20px" color="success" type="filled" icon-pack="fas" icon="fa-check-circle" @click="ReporteOrdenesValidadasCF() "> Órdenes Validadas C/Factura</vs-button>
                <vs-button style="float:right;margin: 20px" color="success" type="filled" icon-pack="fas" icon="fa-file-excel " @click="ReporteConsolidado()" :disabled="habilitarRpt"> Consolidado Excel</vs-button>
            </div>
        </div>
        <!---------------------- DETALLE SOLICITUDES PENDIENTES --------------->
        <vs-divider></vs-divider>
        <br>
        <vs-table2 tooltip max-items="10" pagination :data="listaAutorizaciones" noDataText="Sin datos disponibles" search id="tb_departamentos">

            <template slot="thead">
                <th width="80">Nº. Orden Compra </th>
                <th width="80">Nº. Solicitud </th>
                <th width="500">Departamento</th>
                <th width="120">Tipo</th>
                <th width="400">Proveedor</th>
                <th width="180">Fecha</th>
                <th width="400">Observación Orden Compra</th>
                <th width="170">Total Orden Compra</th>
                <th width="100">No. Validación</th>
                <th width="200">Usuario Solicita</th>
                <th width="200">Envio Proveedor</th>
                <th width="100">Usuario de Validación</th>
                <th width="200">Estado de Validación</th>
                <th width="220">Estado Orden de Compra</th>
                <th width="100">Detalle</th>
                <th width="100">Ver Orden de Compra </th>
            </template>

            <template slot-scope="{data}">
                <tr :style="getStyle(tr.VALIDADA, tr.DiasTransucrridos, tr.ORTOPEDIA)" :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td2 noTooltip :data="data[indextr].IDORDENCOMPRAENC">
                        {{data[indextr].IDORDENCOMPRAENC}}
                    </vs-td2>
                    <vs-td2 noTooltip :data="data[indextr].IDSOLICITUDENCFK">
                        {{data[indextr].IDSOLICITUDENCFK}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].IDDEPARTAMENTOFK">
                        {{data[indextr].IDDEPARTAMENTOFK}}
                        - {{data[indextr].DESCRIPCION_DEPTO }}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].TIPO_OC">
                        {{data[indextr].TIPO_OC}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].DESCRIPCION_PROVEEDOR">
                        {{data[indextr].DESCRIPCION_PROVEEDOR}}
                    </vs-td2>
                    <vs-td2 noTooltip :data="data[indextr].FECHA_CREACION_ORDEN">
                        {{data[indextr].FECHA_CREACION_ORDEN}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].OBSERVACIONES">
                        {{data[indextr].OBSERVACIONES}}
                    </vs-td2>
                    <vs-td2 align="right" :data="data[indextr].TOTAL">
                        Q. {{data[indextr].TOTAL}}
                    </vs-td2>
                    <vs-td2 noTooltip :data="data[indextr].VALIDACION">
                        {{data[indextr].VALIDACION}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].USUARIO">
                        {{data[indextr].USUARIO}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].NumeroProformaProveedor">
                        {{data[indextr].NumeroProformaProveedor}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].USUARIO_MOV">
                        {{data[indextr].USUARIO_MOV}}
                    </vs-td2>
                    <vs-td2 :data="data[indextr].VALIDADA">
                        <label v-if="data[indextr].VALIDADA == 'S'" style=" height: 30px;background-color:green;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIP_VALIDADA}} </label>
                        <label v-if="data[indextr].VALIDADA == 'N'" style="height: 30px;background-color:#C82B0A;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIP_VALIDADA}} </label>
                    </vs-td2>
                    <vs-td2 style="text-align: center;" :data="data[indextr].ESTADO">
                        <label v-if="data[indextr].ESTADO == 'P'" style="height: 30px;background-color:#B2BABB;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'A'" style=" height: 30px;background-color:green;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'R'" style="height: 30px;background-color:#C82B0A;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                        <label v-if="data[indextr].ESTADO == 'C'" style="height: 30px;background-color:#e6cb32;width:120px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> {{data[indextr].DESCRIPCION_ESTADO}} </label>
                    </vs-td2>
                    <vs-td2 align="right">
                        <vs-button v-if="data[indextr].TIPO_OC == 'SERVICIO'" disabled="false" color="warning" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-eye" @click="Consultar_detalle(data[indextr].IDORDENCOMPRAENC)"></vs-button>
                        <vs-button v-else color="warning" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-eye" @click="Consultar_detalle(data[indextr].IDORDENCOMPRAENC)"></vs-button>

                    </vs-td2>
                    <vs-td2 noTooltip align="center">
                        <vs-button color="success" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-download" @click="OrdenCompraRpt(data[indextr])"></vs-button>
                    </vs-td2>
                </tr>

            </template>
        </vs-table2>
        <!--------------- nuevo / ORDEN COMPRA MANUAL -------------->
        <vs-popup classContent="popup-example" :title="tituloEmergente" :active.sync="estadoVentanaEmergente">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>

                    <!------RESPONSABLE--------->
                    <b class="label-sizem" style="margin: 15px">
                        Autorizar por:
                    </b><br>
                    <div class="label-sizem" v-for="(item, key) in responsables" :key="key">
                        <b style="margin: 15px">
                            {{item.NOMBRE_RESPONSABLE}}
                        </b><br>

                    </div>
                    <div v-show="faltaConfigAutoriza == false">

                        <div style="margin: 15px" class="flex flex-wrap">
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                                <vs-radio v-model="idTipoSeleccionado" vs-name="idTipoSeleccionado" vs-value="S"> Servicio</vs-radio>
                            </div>
                            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3" v-show="emprasaProducto">
                                <vs-radio v-model="idTipoSeleccionado" vs-name="idTipoSeleccionado" vs-value="P"> Bien</vs-radio>
                            </div>
                        </div>

                        <div style="margin: 15px" class="flex flex-wrap" v-if="idTipoSeleccionado == 'S'">
                            <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                                <vs-radio visible="false" v-model="idClaseSeleccionado" vs-name="idClaseSeleccionado" vs-value=1> Fijo</vs-radio>
                            </div>
                            <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6" v-if="NoHonorarios">
                                <vs-radio visible="false" v-model="idClaseSeleccionado" vs-name="idClaseSeleccionado" vs-value=2> Honorarios</vs-radio>
                            </div>
                        </div>

                        <div style="margin: 15px" class="flex flex-wrap" v-if="idClaseSeleccionado ==2">
                            <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                            </div>
                            <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                                <vs-radio id="Admisiones" v-model="idTipoOCHonos" vs-name="idTipoOCHonos" vs-value="A" :disabled="estadoBotonesTipoH"> Admisiones</vs-radio>
                            </div>
                            <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6">
                                <vs-radio id="OtrosPagos" v-model="idTipoOCHonos" vs-name="idTipoOCHonos" vs-value="O" :disabled="estadoBotonesTipoH" @click=ConsultarOtrosPagos()> Otros Pagos</vs-radio>
                            </div>
                        </div>
                        <vs-divider> CONSIGNACIÓN </vs-divider>
                        <div class="flex flex-wrap" style="margin: 15px">

                            <div class="flex flex-wrap">
                                <div class="w-full md:w-2/6 lg:w-2/6 xl:w-3/6">
                                    <input labelt="Consignacion" type="checkbox" id="checkbox" v-model="Liquidacion" name="lst_check" @change="OnChangeLiq()" :disabled="SinConsignacion" />
                                    <label class="label-sizem"> Activar </label>
                                </div>
                                <br>
                                <br>
                                <div class="w-full md:w-1/4 lg:w-1/6 xl:w-2/6" v-show="Liquidacion" style="margin-right:5px;">
                                    <br>
                                    <ValidationProvider name="NoLiquidaion">
                                        <label class="typo__label"> Liquidación </label>
                                        <vs-input type="number" class="w-full" v-model="NoLiquidacion" v-on:blur="ValidacionLiquidacion" />
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/4 lg:w-1/6 xl:w-2/6" style="margin-right:5px;" v-show="Liquidacion">
                                    <br>
                                    <label class="typo__label"> Proveedor </label>
                                    <vs-input type="text" class="w-full" v-model="Proveedor" disabled />
                                </div>
                                <div class="w-full md:w-1/4 lg:w-1/6 xl:w-2/6" style="margin-right:5px;" v-show="Liquidacion">
                                    <br>
                                    <label class="typo__label"> NIT </label>
                                    <vs-input type="text" class="w-full" style="margin-right:5px;" v-model="Nit" disabled />
                                </div>
                                <div class="w-full md:w-1/4 lg:w-2/6 xl:w-2/6" style="margin-right:5px;" v-show="Liquidacion">
                                    <br>
                                    <label class="typo__label"> Nombre </label>
                                    <vs-input type="text" class="w-full" v-model="CodProveedor" disabled />
                                </div>
                                <div class="w-full md:w-1/4 lg:w-1/6 xl:w-2/6" v-show="Liquidacion">
                                    <br>
                                    <label class="typo__label"> Estado </label>
                                    <vs-input type="text" class="w-full" v-model="EstadoL" disabled />
                                </div>

                            </div>
                        </div>
                        <vs-divider> DATOS ORDEN DE COMPRAS</vs-divider>
                        <!-- FILTROS PARA INGRESO DETALLE----->
                        <div class="flex flex-wrap" style="margin: 15px">

                            <div class="w-full" v-if="(idTipoSeleccionado == 'S' && idTipoOCHonos !='O')">
                                <ValidationProvider name="observacion" class="required">
                                    <label class="typo__label"> Observaciones</label>
                                    <vs-textarea rows="2" class="w-full" v-model="observaciones" />
                                </ValidationProvider>
                                <br>
                            </div>
                            <div class="w-full" v-if="idTipoSeleccionado == 'P'">
                                <ValidationProvider name="observacion">
                                    <label class="typo__label"> Observaciones</label>
                                    <vs-textarea rows="2" class="w-full" v-model="observaciones" />
                                </ValidationProvider>
                                <br>
                                <br>
                            </div>

                            <div class="w-full" v-if="idTipoOCHonos =='O'">
                                <ValidationProvider name="selectProv" class="required">
                                    <label class="typo__label"> Descripción</label>
                                    <multiselect v-model="cbOtrosPagos" :options="listaOtrosPagos" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_otrosP" placeholder="Búsqueda" @input="onChangeOtrosP">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </ValidationProvider>
                                <br>
                                <br>
                            </div>

                            <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5" style="margin-right:10px;">
                                <ValidationProvider name="selectProv" class="required">
                                    <label class="typo__label">Proveedor</label>
                                    <multiselect v-model="cbProveedor" :disabled="Liquidacion" :options="listaProveedores" :searchable="true" :close-on-select="true" :show-labels="false" :custom-label="seleccion_proveedor" placeholder="Búsqueda" @input="onChangeProveedor">
                                        <span slot="noOptions">Lista no disponible.</span>
                                    </multiselect>
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:5px;">
                                <ValidationProvider name="MontoTotal" rules="required|numero_min:0" class="required">
                                    <label class="typo__label">Monto</label>
                                    <vs-input type="number" class="w-full" v-model="granTotal2" v-on:blur="onChangeGranTotal2" />
                                </ValidationProvider>

                            </div>

                            <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:5px;">
                                <ValidationProvider name="diasCredito" rules="required|numero_min:0|numero_entero" class="required">
                                    <label class="typo__label">Días Crédito</label>
                                    <vs-input type="number" class="w-full" v-model="diasCredito" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:5px;">
                                <ValidationProvider name="pagos" rules="required|numero_min:0|numero_entero" class="required">
                                    <label class="typo__label">Pagos</label>
                                    <vs-input type="number" class="w-full" v-model="pagos" />
                                </ValidationProvider>
                            </div>
                        </div>
                        <br>
                        <!-- HONORARIOS----->
                        <div class="flex flex-wrap" style="margin: 15px">
                            <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:5px;" v-if="idTipoOCHonos == 'A' && idClaseSeleccionado == 2">
                                <ValidationProvider name="MontoTotal" class="required">
                                    <label class="typo__label">Total Cotización</label>
                                    <vs-input disabled type="number" class="w-full" v-model="granTotal" />
                                </ValidationProvider>
                            </div>

                            <div class="w-full md:w-3/4 lg:w-1/6 xl:w-1/6" style="margin-right:5px;" v-if="idClaseSeleccionado == 2">
                                <ValidationProvider name="SerieFactura" class="required" v-slot="{ errors }">
                                    <label class="typo__label">Serie</label>
                                    <vs-input type="text" class="w-full" v-model="serieFactura" v-on:keyup="serieFactura = serieFactura.toUpperCase()" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                            <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:5px;" v-if="idClaseSeleccionado == 2">
                                <ValidationProvider name="Factura" class="required">
                                    <label class="typo__label">Factura</label>
                                    <vs-input type="number" class="w-full" v-model="factura" @change="ValidarFacturaHono" />
                                </ValidationProvider>
                            </div>

                            <div style="margin-right:5px;" v-if="idTipoOCHonos == 'A' && idClaseSeleccionado == 2">
                                <label class="typo__label">Fecha Inicio</label>
                                <vs-input type="date" v-model="fechaInicioH" name="date1" />
                            </div>
                            <div v-if="idTipoOCHonos == 'A' && idClaseSeleccionado == 2">
                                <label class="typo__label">Fecha Final</label>
                                <vs-input type="date" v-model="fechaFinalH" name="date1" />
                            </div>

                            <div class="w-full md:w-4/4 lg:w-4/4 xl:w-4/4" v-if="idTipoOCHonos == 'A' && idClaseSeleccionado == 2">
                                <br>
                                <vs-button style="float:left;margin: 5px" color="primary" type="filled" icon-pack="fas" icon="fa-search" @click="ConsultaAdmisiones()" :disabled="bloquearBtnAdmon"> Admisiones</vs-button>
                                <vs-button color="success" style="float:left;margin: 5px" type="filled" icon-pack="feather" icon="icon-x" id="btn_limpieza" @click="LimpiarHonorarios()"> Limpiar</vs-button>
                                <vs-button color="warning" style="float:left;margin: 5px" type="filled" icon-pack="feather" icon="icon-filter" id="btn_filter" @click="Filtrar()"> Filtrar</vs-button>

                                <br>
                                <br>
                                <vs-table2 max-items="10" pagination :data="listaAdmisiones" class="w-full" search id="tb_listaAdmisiones">
                                    <template slot="thead">
                                        <th></th>
                                        <th width="150px">Admisión</th>
                                        <th width="100px">Cód. Paciente</th>
                                        <th width="600px">Paciente</th>
                                        <th width="200px">Valor</th>

                                    </template>

                                    <template slot-scope="{data}">
                                        <tr :key="indextr" v-for="(tr, indextr) in data">
                                            <vs-td2>
                                                <vs-checkbox :val="tr" v-model="tr.Seleccion" @change="()=> {SumatoriaTotal}" />
                                            </vs-td2>
                                            <vs-td2>{{tr.Admision}}</vs-td2>
                                            <vs-td2>{{tr.Cod_Paciente}}</vs-td2>
                                            <vs-td2>{{tr.Paciente}}</vs-td2>
                                            <vs-td2 align="right">{{parseFloat(tr.Valor).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}</vs-td2>
                                        </tr>

                                    </template>
                                </vs-table2>
                            </div>

                        </div>

                        <!-- ------------PRODUCTO----------- -->
                        <!-- FILTROS PARA INGRESO DETALLE----->
                        <div v-if="idTipoSeleccionado == 'P'">
                            <div class="flex flex-wrap" style="margin: 15px">
                                <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5" style="margin-right:10px;" v-if="VerProducto">
                                    <ValidationProvider name="Producto" rules="required|max:50" class="required">
                                        <label class="typo__label"> Producto</label>
                                        <vs-input type="text" class="w-full" placeholder="Buscar" v-model="producto_buscar" @blur="cargar_producto" />
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:5px;" v-if="VerProducto">
                                    <ValidationProvider name="Cantidad" rules="required|max:50" class="required">
                                        <label class="typo__label"> Cantidad</label>
                                        <vs-input class="w-full" type="number" count="50" v-model="cantidadProducto" />
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:5px;" v-if="VerProducto">
                                    <ValidationProvider name="Total" rules="required|numero_min:0" class="required">
                                        <label class="typo__label"> Total</label>
                                        <vs-input id="subtotal" type="number" class="w-full" v-model="subtotal" v-on:blur="onChangeSubTotal" @blur="precio_uni()" />
                                    </ValidationProvider>
                                </div>

                                <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-right:5px;" v-if="VerProducto">
                                    <ValidationProvider name="PrecioUnitario" rules="required|numero_min:0" class="required">
                                        <label class="typo__label"> Precio Unitario</label>
                                        <vs-input disabled id="ingresoPrecioUnitario" type="number" class="w-full" v-model="ingresoPrecioUnitario" />
                                    </ValidationProvider>
                                </div>
                            </div>
                            <!-- ------------PRODUCTO----------- -->

                            <!-- ------------INICIO DETALLE PRODUCTO SELECCIONADO ----------- -->

                            <div style="margin:15px;" class="flex flex-wrap">
                                <div v-if="productoSeleccionado.Codigo > 0 && VerProducto" class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
                                    <!-- DESCUENTO-->
                                    <div style="border-radius:5px;padding:5px;font-size:15px;background-color:#09af00;color:white;">
                                        {{productoSeleccionado.Codigo}}
                                        <br>
                                        {{productoSeleccionado.marca_comercial}}
                                        <br>
                                        {{productoSeleccionado.Presentacion}}
                                        <br>

                                    </div>

                                </div>

                                <div class="w-full md:w-1/4 lg:w-1/6 xl:w-1/6" style="margin-left:10px;">
                                    <ValidationProvider name="MontoTotal" rules="required|numero_min:0" class="required">
                                        <label class="typo__label">Total Cotización</label>
                                        <vs-input disabled type="number" class="w-full" v-model="granTotal" />
                                    </ValidationProvider>
                                </div>
                                <div style="margin-right:5px;" v-if="VerProducto">
                                    <div>
                                        <vs-button color="primary" style="float:left;margin: 20px" type="filled" icon-pack="fas" icon="fa-plus" @click="Agregar_Detalle()"> Agregar</vs-button>

                                        <vs-button color="success" style="float:left;margin: 20px" type="filled" icon-pack="feather" icon="icon-x" id="btn_limpieza" @click="Limpiar()"> Limpiar</vs-button>
                                    </div>
                                </div>
                                <div style="margin-right:5px;" v-else>
                                    <div>
                                        <vs-button color="success" style="float:left;margin: 20px" type="filled" icon-pack="feather" icon="icon-x" id="btn_limpieza" @click="LimpiarCamposCons()"> Limpiar</vs-button>
                                    </div>
                                </div>

                            </div>
                            <vs-divider></vs-divider>
                            <div v-if="VerProducto" style="margin: 15px">
                                <vs-table2 max-items="10" pagination :data="detalleProductosManual" id="tb_departamentos">
                                    <template slot="thead">
                                        <th width="50px">Número</th>
                                        <th width="100px">Código</th>
                                        <th width="100px">Cantidad</th>
                                        <th width="1000px">Producto</th>
                                        <th width="200px">Total</th>
                                        <th width="150px">Precio Unitario</th>
                                        <th width="50px"></th>
                                    </template>

                                    <template slot-scope="{data}">
                                        <tr :key="indextr" v-for="(tr, indextr) in data">
                                            <vs-td2 :data="data[indextr].correlativo">
                                                {{indextr + 1 }}
                                            </vs-td2>

                                            <vs-td2 :data="data[indextr].producto">
                                                {{data[indextr].producto}}
                                            </vs-td2>
                                            <vs-td2 align="center" :data="data[indextr].cantidad">
                                                {{data[indextr].cantidad}}
                                            </vs-td2>
                                            <vs-td2 :data="data[indextr].descripcion">
                                                {{data[indextr].descripcion}}
                                            </vs-td2>
                                            <vs-td2 align="right" :data="data[indextr].subtotal">
                                                {{parseFloat(tr.subtotal).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                            </vs-td2>
                                            <vs-td2 align="right" :data="data[indextr].precio_unitario">
                                                {{parseFloat(tr.precio_unitario).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                            </vs-td2>

                                            <vs-td2 align="center">
                                                <vs-button color="danger" style="margin-left:1px;display:inline-block;" icon-pack="fas" icon="fa-trash" @click="EliminarProducto(indextr, data[indextr].subtotal)"></vs-button>

                                            </vs-td2>
                                        </tr>

                                    </template>
                                </vs-table2>
                            </div>
                        </div>
                        <div v-show="Liquidacion">
                            <vs-table2 max-items="10" pagination :data="DetalleCargos" id="DetalleCargos">
                                <template slot="thead">
                                    <th width="50px">Número</th>
                                    <th width="100px">Código</th>
                                    <th width="100px">Cantidad</th>
                                    <th width="1000px">Producto</th>
                                    <th width="200px">Total</th>
                                    <th width="150px">Precio Unitario</th>
                                </template>

                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data">
                                        <vs-td2 :data="data[indextr].correlativo">
                                            {{indextr + 1 }}
                                        </vs-td2>

                                        <vs-td2 :data="data[indextr].producto">
                                            {{data[indextr].producto}}
                                        </vs-td2>
                                        <vs-td2 align="center" :data="data[indextr].cantidad">
                                            {{data[indextr].cantidad}}
                                        </vs-td2>
                                        <vs-td2 :data="data[indextr].descripcion">
                                            {{data[indextr].descripcion}}
                                        </vs-td2>
                                        <vs-td2 align="right" :data="data[indextr].subtotal">
                                            {{parseFloat(tr.subtotal).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                        </vs-td2>
                                        <vs-td2 align="right" :data="data[indextr].precio_unitario">
                                            {{parseFloat(tr.precio_unitario).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                        </vs-td2>
                                    </tr>

                                </template>
                            </vs-table2>
                        </div>
                        <!---- agregar boton para guardar-->
                        <vs-divider></vs-divider>

                        <div>
                            <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" id="btn_confirmacion" @click="Confirmacion_Transaccion()"> Grabar</vs-button>
                        </div>
                        <vs-divider></vs-divider>
                    </div>
                </form>

            </div>
        </vs-popup>
        <!---------------Fin Ventana Emergente _---------->

        <!--------------- Resultado busqueda -------------->
        <vs-popup classContent="popup-example" title="Resultado Búsqueda" :active.sync="estadoVentanaEmergenteBusqueda" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div>
                <form>

                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="producto" id="tb_productos">

                        <template slot="thead">
                            <th width="100px">Codigo</th>
                            <th width="1000px">Descripcion</th>
                            <th width="300px">Marca</th>

                            <th width="100px">Presentación</th>
                            <th></th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].Codigo">
                                    {{data[indextr].Codigo}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].Nombre">
                                    {{data[indextr].Nombre}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].Marca">
                                    {{data[indextr].Marca}}
                                </vs-td2>
                                <vs-td2 :data="data[indextr].PresentacionNombre">
                                    {{data[indextr].PresentacionNombre}}
                                </vs-td2>

                                <vs-td2 align="right">
                                    <vs-button color="success" icon-pack="fas" icon="fa-check-circle" style="display:inline-block;margin-right:2px" @click="Seleccionar_Producto(data[indextr])"></vs-button>
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>

            </div>
        </vs-popup>
        <!---------------Fin Ventana Emergente _---------->
        <!--------------- VISOR DE DETALLE -------------->
        <vs-popup classContent="popup-example" title="Verificación de Detalle" :active.sync="Ventana_Detalle" style="z-index:99999" id="div-with-loading" class="vs-con-loading__container">
            <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">

                <form>
                    <div class="flex flex-wrap">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                            <b><label class="typo__label">No. Orden de Compra </label></b>
                            <b><label class="typo__label">{{this.OrdenCompraId}}</label></b>

                        </div>
                    </div>

                    <vs-divider></vs-divider>
                    <vs-table2 max-items="10" pagination :data="Lista_detalle" noDataText="Sin datos disponibles" search id="tb_departamentos">

                        <template slot="thead">
                            <th>Código </th>
                            <th>Producto</th>
                            <th>Cantidad</th>
                            <th>Precio</th>
                            <th>Sub Total</th>
                        </template>

                        <template slot-scope="{data}">
                            <tr :key="indextr" v-for="(tr, indextr) in data">
                                <vs-td2 :data="data[indextr].CODIGO">
                                    {{data[indextr].CODIGO}}
                                </vs-td2>

                                <vs-td2 :data="data[indextr].PRODUCTO">
                                    {{data[indextr].PRODUCTO}}
                                </vs-td2>
                                <vs-td2 align="right" :data="data[indextr].CANTIDAD">
                                    {{parseInt(tr.CANTIDAD)}}
                                </vs-td2>
                                <vs-td2 align="right" :data="data[indextr].PRECIO_UNITARIO">
                                    {{parseFloat(tr.PRECIO_UNITARIO).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                </vs-td2>
                                <vs-td2 align="right" :data="data[indextr].SUB_TOTAL">
                                    {{parseFloat(tr.SUB_TOTAL).toLocaleString("es-GT", {style: "currency",currency: "GTQ"})}}
                                </vs-td2>
                            </tr>
                        </template>
                    </vs-table2>
                </form>
            </div>
        </vs-popup>
    </div>
</vx-card>
</template>

<script>
/**
 * @General
 * Modulo permite autorizar o rechazar una solicitud de compra generada para el correlativo que inicio sesión.
 */
import Multiselect from 'vue-multiselect'

import "vue-multiselect/dist/vue-multiselect.min.css"

import moment from "moment";
export default {

    components: {
        Multiselect,

    },
    data() {
        return {

            listaDepartamentos: [],
            cbDepartamentos: '',
            idDepartamento: '',
            departamento: '',

            listaProveedores: [],
            cbProveedor: '',
            proveedor: {
                codigo: '',
                nombre: '',
                nit: ''
            },

            listaOtrosPagos: [],
            cbOtrosPagos: '',
            idOtrosPagos: '',
            nuevo: '',
            tituloEmergente: '',
            estadoVentanaEmergente: false,
            estadoVentanaEmergenteBusqueda: false,
            idTipoSeleccionado: 'S',
            idClaseSeleccionado: 1,
            idTipoOCHonos: '',
            estadoBotonesTipoDes: false,
            estadoBotonesClaseDes: false,
            estadoBotonesTipoH: false,
            observaciones: '',
            granTotal: 0,
            granTotal2: 0,
            diasCredito: '',
            pagos: '',
            cantidadProducto: '',
            ingresoPrecioUnitario: '',
            autorizarSolicitud: false,
            cbListaOperacion: '',
            fechaInicio: '',
            fechaFinal: '',

            idEstadoSeleccionado: '',
            listaEstado: [{
                    ID: 'P',
                    DESCRIPCION: 'Proceso'
                },
                {
                    ID: 'A',
                    DESCRIPCION: 'Autorizado'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazado'
                },
                {
                    ID: 'C',
                    DESCRIPCION: 'Anulado'
                }
            ],

            cbListaEstadoSolicitud: '',
            idEstadoSolicituSeleccionado: '',
            listaSolicitudEstado: [{
                    ID: 'A',
                    DESCRIPCION: 'Autorizar'
                },
                {
                    ID: 'R',
                    DESCRIPCION: 'Rechazar'
                }
            ],

            listaAutorizaciones: [],
            responsables: [],
            user: '',
            message: 'prueba mensaje',
            messages: [],
            notificaciones: [],
            tipoServicios: false,
            tipoProductos: false,
            tipoHonorarios: false,
            productoSeleccionado: {
                nombre: '',
                marca_comercial: '',
                Principio_activo: '',
                Presentacion: '',
                Codigo: '',
                codigo_interno: '',
                Activo: 'N'
            },
            producto: [],
            producto_buscar: '',
            detalleProductosManual: [{
                producto: '',
                cantidad: 0,
                precio_unitario: 0,
                subtotal: 0,
                descripcion: ''
            }],
            cantDetalle: 0,
            emprasaProducto: false,
            NoHonorarios: false,
            bloquearBtnAdmon: true,
            listaAdmisiones: [{
                SerieA: '',
                NAdmision: '',
                Cod_Paciente: 0,
                Paciente: 0,
                Valor: 0
            }],
            serieFactura: '',
            factura: '',
            fechaInicioH: '',
            fechaFinalH: '',
            Tolerancia: 0,
            medico: 0,
            cb_lista_recepcion: '',
            Id_estado_repcion: '',
            lista_recepcion: [{
                    ID: 'S',
                    DESCRIPCION: 'Validada'
                },
                {
                    ID: 'N',
                    DESCRIPCION: 'No Validada'
                }
            ],
            deOrden: 0,
            aOrden: 0,
            idTipoFSeleccionado: '',
            subtotal: '',
            Lista_detalle: [],
            OrdenCompraId: '',
            Ventana_Detalle: false,
            listado_reportes: [],
            listado_reportes1: [],
            listado_reportes2: [],
            habilitarNueva: false,
            valorEliminar: 0,
            habilitarRpt: true,
            permisos_departamentos: [],
            Liquidacion: false,
            VerProducto: true,
            NoLiquidacion: '',
            Proveedor: '',
            CodProveedor: '',
            Nit: '',
            EstadoL: '',
            DetalleCargos: [],
            SinConsignacion: true,
            OCConsignacion: 0,
            observacionesO: '',
            faltaConfigAutoriza: false

        };
    },
    async mounted() {
        this.faltaConfigAutoriza = false
        this.fechaInicio = this.getDateValue(await this.$dbdate());
        this.fechaFinal = this.getDateValue(await this.$dbdate());
        this.fechaFinalH = this.getDateValue(await this.$dbdate());
        this.fechaInicioH = moment(await this.$dbdate()).subtract(2, 'year').local().format('YYYY-MM-DD');
        this.Consulta_autorizaciones();
        this.Consultar_proveedor();
        this.cantDetalle = 0
        this.listado_reportes = this.$recupera_parametros_reporte('Orden de Compra');
        this.listado_reportes1 = this.$recupera_parametros_reporte('Consolidado');
        this.listado_reportes2 = this.$recupera_parametros_reporte('Ordenes Validadas');

        //Verifica los deparamentos a los que se tienen permisos para crear
        for (let privilegio of this.$store.state.privilegios) {
            if (privilegio.Privilegio.includes("DEPARTAMENTO")) {
                let departamento = privilegio.Privilegio.split('_')[1]
                this.permisos_departamentos.push(departamento)
            }
        }

        this.ConsultarDepartamentos(this.permisos_departamentos.join(","));
    },

    methods: {
        getDateValue(value) {
            if (value === undefined) {
                return '';
            } else if (value === null) {
                return '';
            } else if (value == '') {
                return '';
            }
            return moment(value).format('YYYY-MM-DD');
        },

        ConsultarDepartamentos(departamentos) {
            this.listaDepartamentos = []
            this.axios.post('/app/v1_OrdenCompra/consulta_departamento', {
                    tipo_consulta: 'P',
                    departamentos_Permisos: departamentos
                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.listaDepartamentos = "";
                    } else {
                        this.listaDepartamentos = resp.data.json;
                    }
                })
                .catch(() => {})
        },
        onChangedepartamento(value) {
            if (value !== null && value.length !== 0) {
                this.idDepartamento = value.CODIGO;
                this.departamento = value.NOMBRE;
                this.habilitarNueva = true
            } else {
                this.idDepartamento = '';
                this.departamento = '';
            }
        },
        departamento_seleccionada({
            CODIGO,
            NOMBRE
        }) {
            return `${CODIGO} - ${NOMBRE} `
        },
        Operacion_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstado(value) {
            if (value !== null && value.length !== 0) {
                this.idEstadoSeleccionado = value.ID;
            } else {
                this.idEstadoSeleccionado = '';
            }
        },
        estado_solicitud_seleccionada({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeEstadoSolicitud(value) {
            if (value !== null && value.length !== 0) {
                this.idEstadoSolicituSeleccionado = value.ID;
            } else {
                this.idEstadoSolicituSeleccionado = '';
            }
        },
        Consulta_autorizaciones() {

            const sesion = this.$store.state.sesion;
            const url = this.$store.state.global.url;
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/Consultar_orden_compra_manual', {
                    Empresa: sesion.sesion_empresa,
                    estado: this.idEstadoSeleccionado,
                    fecha_inicio: this.fechaInicio,
                    fecha_fin: this.fechaFinal,
                    corporativo: sesion.corporativo,
                    iddocumento: 3,
                    departamento: this.idDepartamento,
                    idProveedor: this.proveedor.codigo,
                    Recepcionada: this.Id_estado_repcion,
                    DeOrden: this.deOrden,
                    AOrden: this.aOrden,
                    TipoSeleccionado: this.idTipoFSeleccionado

                })
                .then(resp => {
                    if (resp.data.codigo == 0) {

                        this.listaAutorizaciones = [];
                        this.listaAutorizaciones = resp.data.json.map(m => {
                            return {
                                ...m,
                                TOTAL: parseFloat(m.TOTAL).toFixed(2)
                            }
                        })
                        this.habilitarRpt = false
                    } else if (this.listaAutorizaciones.length == 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Orden de compra',
                            text: 'No existen registros, según los filtros ingresados.',
                        })
                        this.habilitarRpt = true
                        this.listaAutorizaciones = [];
                    }
                })
        },
        Valida_EmpresaIngresoOC() {

            this.emprasaProducto = false
            const url = this.$store.state.global.url;
            this.axios.post(url + 'app/v1_OrdenCompra/Valida_EmpresaOC', {
                    productoClase: 'P',
                    iddocumento: 3
                })
                .then(resp => {
                    if (resp.data.resultado == 1) {
                        this.emprasaProducto = false
                        this.NoHonorarios = true
                    } else {
                        this.emprasaProducto = true
                        this.NoHonorarios = false
                        this.idTipoSeleccionado = 'S'
                    }
                })
        },
        OrdenCompraRpt(datos) {

            this.$reporte_modal({
                Nombre: "Orden de Compra",
                Opciones: {
                    IdOrdenCompra: datos.IDORDENCOMPRAENC
                }
            })
        },

        Abrir_Ventana_Emergente_nuevo() {

            this.detalleProductosManual = [];
            this.listaAdmisiones = [];
            this.nuevo = 'S';
            this.Id_OrdenCompra_enc = 0;

            this.Valida_EmpresaIngresoOC();
            this.Consultar_proveedor();
            this.Consultar_responsables();

            if (this.idDepartamento == '' || this.idDepartamento == '0' || this.idDepartamento == null || this.idDepartamento == '  ') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Ordenes de Compra',
                    text: 'Seleccionar departamento',
                })
                return;
            }
            this.tituloEmergente = 'Nueva Orden de Compra Manual';
            this.estadoVentanaEmergente = true;

            //Estado de los campos de la ventana emergente
            this.Deshabilitar_campo_codigo = false;
            this.Deshabilitar_campo_multiempresa = false;
            this.Deshabilitar_campo_uniempresa = false;
            this.Operacion = 'N'; // Indica la variable que es un nuevo registro
            this.Limpiar_Campos();
            this.producto_buscar = '',
                this.Limpiar_CamposDetP();
            this.LimpiarCamposCons(); //Datos de Consignación 

        },
        cargar_producto: function () {

            this.producto = [];
            if (this.idDepartamento == '' || this.idDepartamento == null || this.IdDepartamento == '0') {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Departamento',
                    text: 'Seleccionar Departamento',
                })
                return;
            }
            if (this.idTipoSeleccionado == 'P') {
                this.$vs.loading();
                this.axios.post('/app/inventario/invProductolist', {
                        Pagina: 1,
                        Busqueda: '',
                        SubClase: 0,
                        Operacion: 'B',
                        Nombre: this.producto_buscar.trim(),
                        Departamento: this.idDepartamento
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                            this.producto = []

                            resp.data.json.map(data => {                                
                                this.producto.push({
                                    ...data
                                })
                                this.producto = this.producto.filter(ab => ab.TipoBodega != 'O');
                            })

                            this.Mostrar_resultado_producto(this.producto);

                        } else {

                            this.producto = []
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Producto',
                                text: (resp.data.codigo < 0) ? resp.data.descripcion : 'Sin Registros',
                            })
                        }
                    })

            }
        },
        precio_uni: function () {
            this.ingresoPrecioUnitario = (parseFloat(this.subtotal / this.cantidadProducto).toFixed(2))
        },
        Mostrar_resultado_producto(value) {
            if (value.length == 1) {
                this.productoSeleccionado.Codigo = value.find(obj => obj.Codigo != '').Codigo;
                this.productoSeleccionado.marca_comercial = value.find(obj => obj.Nombre != '').Nombre;
                this.productoSeleccionado.Principio_activo = value.find(obj => obj.Principio_activo != '').Principio_activo;
                this.productoSeleccionado.codigo_interno = value.find(obj => obj.codigo_interno != '').codigo_interno;
                this.productoSeleccionado.Presentacion = value.find(obj => obj.PresentacionNombre != '').PresentacionNombre;
            } else if (value.length > 1) {
                this.estadoVentanaEmergenteBusqueda = true;
            }

        },
        Seleccionar_Producto(value) {
            this.producto = [];
            this.productoSeleccionado.Codigo = value.Codigo;
            this.productoSeleccionado.marca_comercial = value.Nombre;
            this.productoSeleccionado.Principio_activo = value.Principio_activo;
            this.productoSeleccionado.codigo_interno = value.codigo_interno;
            this.productoSeleccionado.Presentacion = value.PresentacionNombre;
            this.estadoVentanaEmergenteBusqueda = false;

        },
        Limpiar_CamposDetP() {
            this.productoSeleccionado.Codigo = '';
            this.productoSeleccionado.marca_comercial = '';
            this.productoSeleccionado.Principio_activo = '';
            this.productoSeleccionado.codigo_interno = '';
            this.productoSeleccionado.Presentacion = '';
            this.cantidadProducto = '';
            this.ingresoPrecioUnitario = '';
            this.subtotal = '';

        },
        Limpiar_CamposAdmon() {
            this.listaAdmisiones = [];
            this.serieFactura = '';
            this.factura = '';
            this.idOtrosPagos = ''
            this.observaciones = '';
            this.idClaseSeleccionado = 1;
            this.idTipoOCHonos = '';
            this.cbOtrosPagos = '';
            this.observacionesO = ''
        },
        Agregar_Detalle() {

            //    Función Permite  actualizar y  almacenar un nuevo registro detalle;

            if (this.productoSeleccionado.Codigo === 0 || this.productoSeleccionado.Codigo === '' || this.productoSeleccionado.Codigo === null || this.productoSeleccionado.Codigo === undefined) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Ordenes de Compra',
                    text: 'Seleccionar producto.',
                })
                return;
            }
            if (this.subtotal === 0 || this.subtotal === '') {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Ordenes de Compra',
                    text: 'Ingresar Total',
                })
                return;
            }

            if (this.cantidadProducto <= 0) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Ordenes de Compra',
                    text: 'Cantidad Inválida',
                })
            }
            this.ValidacionTolerancia()

        },

        ValidacionTolerancia() {
            this.ingresoPrecioUnitario = (this.subtotal / this.cantidadProducto),
                this.axios.post('/app/inventario/MargenTolerancia', {
                    PrecioProducto: this.ingresoPrecioUnitario,
                    Codigo: this.productoSeleccionado.Codigo,
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        if (this.VerProducto == true) {

                            this.Tolerancia = 0
                            if (this.Tolerancia == 0) {
                                if ((!this.detalleProductosManual.some(data => data.producto === this.productoSeleccionado.Codigo))) {
                                    this.detalleProductosManual.push({
                                        producto: this.productoSeleccionado.Codigo,
                                        cantidad: this.cantidadProducto,
                                        precio_unitario: this.ingresoPrecioUnitario,
                                        subtotal: this.subtotal,
                                        descripcion: this.productoSeleccionado.marca_comercial
                                    })
                                    this.producto_buscar = ''
                                    var total = 0;
                                    this.detalleProductosManual.forEach(element => {
                                        total += (Number(element.subtotal));

                                    });
                                    this.granTotal = (parseFloat(total).toFixed(2));

                                    this.Limpiar_CamposDetP()
                                    this.cantDetalle = this.cantDetalle + 1
                                } else {
                                    this.Tolerancia = 1
                                    this.Limpiar_CamposDetP()
                                    return;
                                }
                            }
                            this.Limpiar_CamposDetP()
                        } else {
                            if (this.Liquidacion == true) {
                                this.SumaCargos()
                            }
                        }
                    }
                })
                .catch(() => {
                    this.Tolerancia = 1
                    if (this.Liquidacion == true) {
                        let indiceBorrar = this.DetalleCargos.findIndex(cargo => cargo.producto == this.productoSeleccionado.Codigo)
                        this.DetalleCargos.splice(indiceBorrar, 1)
                        this.SumaCargos()
                    }
                    this.Limpiar_CamposDetP()
                })

        },
        Consultar_proveedor() {
            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion;
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_proveedor', {
                    empresa: sesion.sesion_empresa
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {

                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Proveedores',
                            text: resp.data.mensaje,
                        })
                        //Limpia la tabla si no existen registros
                        this.listaProveedores = "";
                    } else {
                        //Decodificación
                        this.listaProveedores = resp.data.json;

                    }

                })
                .catch(() => {
                    this.listaProveedores = "";
                })
        },
        seleccion_proveedor({
            NIT,
            NOMBRE,
        }) {
            return `${NIT} - ${NOMBRE} `;
        },
        onChangeProveedor(value) {
            if (value !== null && value.length !== 0) {

                this.proveedor.codigo = value.CODIGO,
                    this.proveedor.nombre = value.NOMBRE.toUpperCase();
                this.proveedor.nit = value.NIT;
                if (this.idTipoSeleccionado == 'S' && this.idClaseSeleccionado == 2) {
                    this.ValidarProveedorHono();
                }
            } else {
                this.proveedor.codigo = '';
                this.proveedor.nombre = '';
                this.proveedor.nit = '';
            }
        },
        ConsultarOtrosPagos() {
            const url = this.$store.state.global.url

            this.axios.post(url + 'app/v1_OrdenCompra/ConsultaOtrosP', {
                    productoClase: this.idTipoSeleccionado,
                    produtoSub: this.idClaseSeleccionado,
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.listaProveedores = [];
                    } else {
                        //Decodificación
                        this.listaOtrosPagos = resp.data.json;
                    }
                })
                .catch(() => {})
        },
        seleccion_otrosP({
            Descripcion
        }) {
            return `${Descripcion}`
        },
        onChangeOtrosP(value) {
            if (value !== null && value.length !== 0) {
                this.idOtrosPagos = value.id
                this.observacionesO = value.Descripcion

            } else {
                this.idOtrosPagos = '';
                this.observacionesO = '';
            }
        },
        Consultar_responsables() {
            this.faltaConfigAutoriza = false
            const url = this.$store.state.global.url
            const sesion = this.$store.state.sesion
            this.$vs.loading();
            this.axios.post(url + 'app/v1_OrdenCompra/consulta_listado_autorizaciones', {
                    empresa: sesion.sesion_empresa,
                    id_documento: 3,
                    num_documento: '1',
                    tipo_consulta: 'T',
                    id_departamento: 0,
                    departamento: this.idDepartamento,
                    tipo: this.idTipoSeleccionado
                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Autorizaciones',
                            text: resp.data.mensaje
                        })
                        //Limpia la tabla si no existen registros

                    } else {
                        this.responsables = resp.data.json
                        if (this.responsables != '') {
                            this.faltaConfigAutoriza = false
                        } else {
                            this.faltaConfigAutoriza = true
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Autorizaciones',
                                text: 'No se encuentra configurado el usuario que autoriza.'
                            })
                        }

                    }
                })
        },
        EstadoRecepcion({
            DESCRIPCION
        }) {
            return `${DESCRIPCION} `
        },
        onChangeRecepcion(value) {

            if (value !== null && value.length !== 0) {
                this.Id_estado_repcion = value.ID;
            } else {
                this.Id_estado_repcion = '';

            }
        },
        ConsultaAdmisiones() {
            if (this.proveedor.codigo != '') {
                const url = this.$store.state.global.url
                this.axios.post(url + 'app/v1_OrdenCompra/ConsultaAdmisionesH', {
                        productoClase: this.idTipoSeleccionado,
                        produtoSub: this.idClaseSeleccionado,
                        fecha_inicioh: this.fechaInicioH,
                        fecha_finh: this.fechaFinalH,
                        idProveedor: this.proveedor.codigo

                    })

                    .then(resp => {
                        if (!this.isEmptyObject(resp.data.json)) {

                            this.listaAdmisiones = resp.data.json.map(data => {
                                return {
                                    ...data,
                                    Seleccion: false,
                                    Valor: parseFloat(data.Valor).toFixed(2)
                                }

                            })
                            this.listaAdmisiones.forEach(element => {

                            });
                        } else {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Ordenes de Compra',
                                text: 'No se encontraron Admisiones',

                            })
                            return;

                        }
                    })
            } else {
                this.listaAdmisiones = []
                this.$vs.notify({
                    color: 'danger',
                    title: 'Ordenes de Compra',
                    text: 'Ingrese Proveedor',

                })
                return;

            }

        },
        RecuperarMedico() {

            this.axios.post('/app/v1_OrdenCompra/RecuperaMedico', {
                    productoClase: this.idTipoSeleccionado,
                    produtoSub: this.idClaseSeleccionado,
                    idProveedor: this.proveedor.codigo

                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.bloquearBtnAdmon = true
                        this.cbProveedor = ''
                        this.medico = ''
                    } else {
                        this.medico = resp.data.json[0].Medico
                    }
                })
        },
        ValidarProveedorHono() {
            this.axios.post('/app/v1_OrdenCompra/ValidaProveedorHono', {
                    productoClase: this.idTipoSeleccionado,
                    produtoSub: this.idClaseSeleccionado,
                    fecha_inicioh: this.fechaInicioH,
                    fecha_finh: this.fechaFinalH,
                    idProveedor: this.proveedor.codigo

                })
                .then(resp => {
                    if (resp.data.codigo != 0) {
                        this.bloquearBtnAdmon = true
                        this.Limpiar_Campos()

                    } else {
                        this.bloquearBtnAdmon = false
                    }
                })
            this.RecuperarMedico()
        },
        ValidarFacturaHono() {
            if (this.factura > 0) {
                this.axios.post('/app/v1_OrdenCompra/ValidarFacturaHono', {
                        productoClase: this.idTipoSeleccionado,
                        produtoSub: this.idClaseSeleccionado,
                        serieF: this.serieFactura,
                        factura: this.factura,
                        idProveedor: this.proveedor.codigo
                    })
                    .then(resp => {
                        if (resp.data.codigo != 0) {
                            this.Limpiar_Campos()
                        } else {
                            this.bloquearBtnAdmon = false
                        }

                    })
            }
        },
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0;
        },
        LimpiarHonorarios() {
            this.cbProveedor = ''
            this.serieFactura = ''
            this.factura = ''
            this.granTotal2 = ''
            this.diasCredito = ''
            this.pagos = ''
            this.granTotal = ''
            this.listaAdmisiones = []
            this.proveedor.codigo = ''
            this.observaciones = ''
            this.observacionesO = ''
            this.seleccion_otrosP = ''
        },
        EliminarProducto(index, datos) {
            /**
             * @General
             * Función eliminar registro;
             */
            this.valorEliminar = datos
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Eliminar detalle? ',
                acceptText: 'Aceptar',
                cancelText: 'Cancelar',
                accept: () => {
                    this.$delete(this.detalleProductosManual, index)
                    this.granTotal = (this.granTotal - this.valorEliminar)
                }
            })
        },
        Confirmacion_Transaccion() {
            /**
             * @General
             * Función Permite   almacenar un nuevo registro;
             */
            if (this.nuevo === 'S') {

                if (!this.idDepartamento > 0) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Ordenes de Compra',
                        text: 'Seleccionar departamento',
                    })
                    return;
                }
                if (!this.proveedor.codigo > 0 || this.proveedor.codigo == '') {
                    if (this.Liquidacion == true) {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Liquidación no tiene registrado Proveedor',
                        })
                        return;
                    } else {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Seleccione Proveedor',
                        })
                        return;
                    }

                }
                if (!this.granTotal2 > 0) {
                    this.$vs.notify({
                        color: 'danger',
                        title: 'Ordenes de Compra',
                        text: 'Ingrese Monto Total Proveedor',
                    })
                    return;
                }

                if (this.idTipoSeleccionado == 'S') {
                    if (!this.diasCredito > 0) {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Ingrese días crédito',
                        })
                        return;
                    }
                    if (!this.pagos > 0) {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Ingrese pagos',
                        })
                        return;
                    }

                    if (this.idClaseSeleccionado == 1) {
                        this.granTotal = this.granTotal2
                        if (this.observaciones == '') {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Ordenes de Compra',
                                text: 'Ingresar Observación',
                            })
                            return;
                        }
                    }
                    if (this.idTipoOCHonos == 'O') {
                        this.granTotal = this.granTotal2

                    }

                } else {
                    if (Number(this.granTotal) != Number(this.granTotal2)) {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'No cuadra Monto ingresado con el Monto Cotización o Monto total del detalle',
                        })
                        return;
                    }
                }

                //Honorarios
                if (this.idTipoOCHonos == 'A' && this.idClaseSeleccionado == 2) {
                    if (this.observaciones == '') {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Ingresar Observación',
                        })
                        return;
                    }
                    if (this.serieFactura == '') {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Ingresar Serie',
                        })
                        return;
                    }
                    if (this.factura == '') {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Ingresar Factura',
                        })
                        return;
                    }
                    if (this.fechaInicioH == '') {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Ingresar Fecha Inicio',
                        })
                        return;
                    }
                    if (this.fechaFinalH == '') {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Ingresar Fecha Final',
                        })
                        return;
                    }
                    if (this.listaAdmisiones == []) {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'No existe Admisiones a cargar',
                        })
                        return;
                    } else {
                        this.listaAdmisiones = this.listaAdmisiones.filter(ab => ab.Seleccion == true);
                    }

                } else {
                    if (this.Liquidacion == true) {
                        if (!this.NoLiquidacion > 0) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Ordenes de Compra',
                                text: 'Ingrese Número de Consignación.',
                            })
                            return;
                        }
                    }
                    if (Number(this.granTotal) === Number(this.granTotal2)) {
                        this.granTotal = this.granTotal2
                    } else {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'No cuadra Monto ingresado con el Monto Cotización o Monto total del detalle',
                        })
                        return;
                    }
                    if (!this.diasCredito > 0) {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Ingrese días crédito',
                        })
                        return;
                    }
                    if (!this.pagos > 0) {
                        this.$vs.notify({
                            color: 'danger',
                            title: 'Ordenes de Compra',
                            text: 'Ingrese pagos',
                        })
                        return;
                    }

                }
                let JsonDetalle = null

                if (this.idTipoSeleccionado == 'P') { //Para productos
                    //Consignación
                    if (this.Liquidacion == true) {
                        JsonDetalle = JSON.stringify(this.DetalleCargos);
                        this.detalleProductosManual = []
                        this.cantDetalle = this.DetalleCargos.length
                    } else {
                        JsonDetalle = JSON.stringify(this.detalleProductosManual);
                    }

                } else {
                    //Honorarios
                    if (this.idTipoOCHonos == 'A' && this.idClaseSeleccionado == 2) {
                        JsonDetalle = JSON.stringify(this.listaAdmisiones);
                        this.detalleProductosManual = []
                        this.cantDetalle = this.listaAdmisiones.length
                    }
                }

                const url = this.$store.state.global.url

                if (this.idTipoSeleccionado == 'P' || this.idTipoSeleccionado == 'S' && this.idClaseSeleccionado == 1) {
                    //this.axios.post(url + 'app/v1_OrdenCompra/Carga_orden_compra_manual', {
                    this.axios.post(url + 'app/v1_OrdenCompra/InsertarOrdenCompra', {
                            productoClase: this.idTipoSeleccionado,
                            produtoSub: this.idClaseSeleccionado,
                            departamento: this.idDepartamento,
                            observaciones: this.observaciones,
                            idProveedor: this.proveedor.codigo,
                            total: this.granTotal,
                            dias_credito: this.diasCredito,
                            cant_pagos: this.pagos,
                            iddocumento: 3,
                            cantLinea: this.cantDetalle,
                            Consignacion: this.Liquidacion == true ? 'S' : 'N',
                            Liquidacion: this.NoLiquidacion,
                            datos: JsonDetalle
                        })
                        .then(resp => {
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Orden de compra',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                if (this.Liquidacion == true) {
                                    this.OCConsignacion = resp.data.resultado
                                    this.ActualizaLiquidacion()

                                }
                                this.estadoVentanaEmergente = false;
                            }
                            this.Limpiar_Campos()
                            this.Limpiar_CamposDetP()
                            this.Limpiar_CamposAdmon()
                            this.Consulta_autorizaciones()
                        })

                } else if (this.idTipoSeleccionado == 'S' && this.idClaseSeleccionado == 2) {
                    if (this.idTipoOCHonos == 'O') {
                        this.granTotal = this.granTotal2
                        this.observaciones = this.observacionesO
                        this.cantDetalle = 0
                        if (this.idOtrosPagos == '') {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Ordenes de Compra',
                                text: 'Seleccione Descripción',
                            })
                            return;
                        }
                        if (this.serieFactura == '') {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Ordenes de Compra otros pagos**',
                                text: 'Ingresar Serie',
                            })
                            return;
                        }
                        if (this.factura == '') {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Ordenes de Compra',
                                text: 'Ingresar Factura',
                            })
                            return;
                        }

                    }

                    //this.axios.post(url + 'app/v1_OrdenCompra/Carga_orden_compra_manual', {
                    this.axios.post(url + 'app/v1_OrdenCompra/InsertarOrdenCompra', {
                            productoClase: this.idTipoSeleccionado,
                            produtoSub: this.idClaseSeleccionado,
                            departamento: this.idDepartamento,
                            observaciones: this.observaciones,
                            idProveedor: this.proveedor.codigo,
                            total: this.granTotal,
                            dias_credito: this.diasCredito,
                            cant_pagos: this.pagos,
                            iddocumento: 3,
                            cantLinea: this.idTipoOCHonos == 'O' ? 0 : this.cantDetalle,
                            datos: JsonDetalle,
                            tipoHono: this.idTipoOCHonos,
                            medico: this.medico,
                            serieF: this.serieFactura,
                            factura: this.factura,
                            idOtroPagos: this.idOtrosPagos,
                        })
                        .then(resp => {
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Orden de compra',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                this.estadoVentanaEmergente = false;
                            }
                            this.Limpiar_Campos()
                            this.Limpiar_CamposDetP()
                            this.Limpiar_CamposAdmon()
                            this.Consulta_autorizaciones()
                        })
                }

            }

        },
        Filtrar() {
            this.listaAdmisiones = this.listaAdmisiones.filter(ab => ab.Seleccion == true);
        },
        Limpiar() {

            this.producto_buscar = '',
                this.Limpiar_CamposDetP()
        },
        Limpiar_Campos() {
            this.responsables = [],
                this.observaciones = '',
                this.proveedor.codigo = '',
                this.cbProveedor = []
            this.granTotal = '',
                this.granTotal2 = '',
                this.diasCredito = '',
                this.pagos = '',
                this.ingresoPrecioUnitario = '',
                this.cantidadProducto = ''
            this.serieFactura = ''
            this.factura = ''
            this.idOtrosPagos = ''
        },
        BloqueoVariables() {
            this.estadoBotonesTipoDes = true;

        },
        DesbloqueoVariables() {
            this.estadoBotonesTipoDes = false;
        },
        Consultar_detalle(datos) {

            const url = this.$store.state.global.url
            this.Lista_detalle = [];
            this.OrdenCompraId = datos
            this.axios.post(url + 'app/v1_OrdenCompra/Consulta_dOrdenCompraManual', {

                    IdOrdenCompraEnc: this.OrdenCompraId

                })
                .then(resp => {

                    if (resp.data.codigo != 0) {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Orden de Compra',
                            text: resp.data.mensaje,
                        })

                    } else {
                        this.Lista_detalle = resp.data.json;
                        this.Ventana_Detalle = true
                    }
                })
        },
        ReporteOrdenesValidadasCF() {
            this.$reporte_modal({
                    Nombre: "Ordenes Validadas",
                    Opciones: {
                        finicial: this.fechaInicio,
                        ffinal: this.fechaFinal,
                        departamento: this.idDepartamento,
                        ordenD: this.deOrden,
                        ordenA: this.aOrden,
                    },
                    Formato: "EXCEL"
                })
                .catch(() => {
                    this.$vs.loading.close();
                })
            this.$vs.loading.close();
        },
        ReporteConsolidado() {
            this.$reporte_modal({
                Nombre: "Consolidado",
                Opciones: {
                    estado: this.idEstadoSeleccionado,
                    finicial: this.fechaInicio,
                    ffinal: this.fechaFinal,
                    departamento: this.idDepartamento,
                    ordenD: this.deOrden,
                    ordenA: this.aOrden,
                    proveedor: this.proveedor.codigo,
                    recepcionada: this.Id_estado_repcion,
                    tipo: this.idTipoFSeleccionado,
                    opcion: '3'
                },
                Formato: "EXCEL"
            })
        },
        onChangeGranTotal2: function () {
            this.granTotal2 = this.granTotal2 != '' ? parseFloat(this.granTotal2).toFixed(2) : ''
        },
        onChangeSubTotal: function () {
            this.subtotal = this.subtotal != '' ? parseFloat(this.subtotal).toFixed(2) : ''
        },
        //Consignación
        ValidacionLiquidacion() {
            this.LimpiarCamposCons()
            if (this.NoLiquidacion != 0 || this.NoLiquidacion != '') {

                this.axios.post('/app/v1_OrdenCompra/ValidarLiquidacion', {
                        Liquidacion: this.NoLiquidacion
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            this.ConsultaLiquidacion()
                        }
                    })
                    .catch(() => {

                        this.NoLiquidacion = ''
                        this.DetalleCargos = []
                        this.LimpiarCamposCons()
                    })
            }

        },
        ConsultaLiquidacion() {
            this.axios.post('/app/v1_OrdenCompra/ObtenerLiquidacion', {
                    Liquidacion: this.NoLiquidacion
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.Proveedor = resp.data.json[0].Proveedor
                        this.CodProveedor = resp.data.json[0].Nombre
                        this.Nit = resp.data.json[0].Nit
                        this.EstadoL = resp.data.json[0].DesStatus
                        this.cbProveedor = {
                            NIT: resp.data.json[0].Nit,
                            NOMBRE: resp.data.json[0].Nombre
                        }
                        this.proveedor.codigo = resp.data.json[0].Proveedor
                        this.observaciones = 'Liquidación de consignación ' + this.NoLiquidacion
                        this.diasCredito = 30
                        this.pagos = 1
                        this.ObtenerDetalleLiq()
                    }
                })
        },
        ObtenerDetalleLiq() {

            this.axios.post('/app/v1_OrdenCompra/ObtenerDetalle', {
                    Liquidacion: this.NoLiquidacion
                })
                .then(resp => {
                    this.DetalleCargos = []
                    if (resp.data.codigo == 0) {

                        this.DetalleCargos = resp.data.json.map(m => {
                            return {
                                ...m,
                                cantidad: parseInt(m.cantidad),
                            }
                        })
                        //let subtotal = 0;
                        this.DetalleCargos.forEach(element => {
                            //subtotal += (Number(element.subtotal));
                            this.subtotal = Number(element.subtotal)
                            this.cantidadProducto = (element.cantidad)
                            this.productoSeleccionado.Codigo = (element.producto);
                            this.ValidacionTolerancia()
                        });

                        if (this.DetalleCargos.length === 0) {
                            this.$vs.notify({
                                color: '#B71C1C',
                                title: 'Orden de Compra',
                                text: 'La liquidación ingresada no tiene cargos.',
                            })
                        }
                    }

                })
        },
        SumaCargos() {
            var total = 0;
            this.DetalleCargos.forEach(element => {
                total += (Number(element.subtotal));
            });
            this.granTotal = parseFloat(total).toFixed(2);
            this.granTotal2 = this.granTotal
            if (this.DetalleCargos.length === 0) {
                this.$vs.notify({
                    color: '#B71C1C',
                    title: 'Orden de Compra',
                    text: 'La liquidación ingresada no tiene cargos, correctos.',
                })
            }
        },

        LimpiarCamposCons() {
            // this.NoLiquidacion = '',
            this.Proveedor = '',
                this.CodProveedor = '',
                this.Nit = '',
                this.EstadoL = '',
                this.DetalleCargos = [],
                this.granTotal = '',
                this.granTotal2 = ''
            //
            this.observaciones = '',
                this.proveedor.codigo = '',
                this.cbProveedor = []
            this.diasCredito = '',
                this.pagos = '',
                this.ingresoPrecioUnitario = '',
                this.cantidadProducto = ''

        },
        ActualizaLiquidacion() {
            this.axios.post('/app/v1_OrdenCompra/ActualizaLiquidacion', {
                    Liquidacion: this.NoLiquidacion,
                    IdOrdenCompraEnc: this.OCConsignacion
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.Proveedor = resp.data.json[0].Proveedor
                        this.CodProveedor = resp.data.json[0].Nombre
                        this.Nit = resp.data.json[0].Nit
                        this.EstadoL = resp.data.json[0].DesStatus
                        this.cbProveedor = {
                            NIT: resp.data.json[0].Nit,
                            NOMBRE: resp.data.json[0].Nombre
                        }
                        this.proveedor.codigo = resp.data.json[0].Proveedor
                        this.observaciones = 'Liquidación de consignación ' + this.NoLiquidacion
                        this.diasCredito = 30
                        this.pagos = 1
                        this.ObtenerDetalleLiq()
                    }
                })
        },
		getStyle(estado, dias, ortopedia){
            return estado != 'S' && dias > 3 && ortopedia == 'S' ? 'background-color: #FFD404; opacity: 0.7;': 'background-color: white;'
        }
    },
    watch: {

        'idTipoSeleccionado'() {
            this.Consultar_responsables()
            if (this.idTipoSeleccionado == 'S') {
                this.idClaseSeleccionado = 1
                this.SinConsignacion = true
                this.Liquidacion = false
                this.LimpiarCamposCons()
                this.Limpiar()
            } else {
                this.LimpiarCamposCons()
                this.Limpiar()
                this.SinConsignacion = false
            }
        },
        'idTipoOCHonos'() {
            if (this.idTipoOCHonos == 'O') {
                this.ConsultarOtrosPagos()
            }
        },
        'idClaseSeleccionado'() {
            if (this.idClaseSeleccionado == 1) {
                this.idTipoOCHonos = ''
            }
        },
        'Liquidacion'() {
            if (this.Liquidacion == true) {
                this.VerProducto = false
                if (this.NoLiquidacion == '') {
                    this.LimpiarCamposCons()
                    this.Limpiar()
                }
            } else {
                this.VerProducto = true
                this.NoLiquidacion = ''
                this.LimpiarCamposCons()
                this.Limpiar()
            }
        }
    },
    computed: {
        SumatoriaTotal() {
            var ListaSeleccionada = [];
            var total = 0;
            if (this.listaAdmisiones.length > 0) {
                ListaSeleccionada = this.listaAdmisiones.filter(ab => ab.Seleccion == true);
                for (let i = 0; i < ListaSeleccionada.length; i++) {
                    total = parseFloat(total) + parseFloat(ListaSeleccionada[i].Valor);
                    total = parseFloat(total).toFixed(2)
                }
                this.granTotal = total;
            }
            return total;
        },
        sesion() {
            return this.$store.state.sesion
        },
    },

    async beforeCreate() {
        this.listado_reportes = await this.$recupera_parametros_reporte('Orden de Compra')
        this.listado_reportes1 = await this.$recupera_parametros_reporte('Consolidado')
        this.listado_reportes2 = await this.$recupera_parametros_reporte('Ordenes Validadas')
    }

}
</script>

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 18px;
    font-weight: bold;
}

.label-sizem {
    font-size: 16px;
    font-weight: bold;
}
</style>
