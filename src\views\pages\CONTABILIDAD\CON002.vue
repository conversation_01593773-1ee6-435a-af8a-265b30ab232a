<template>
<vx-card title="Contabilidad General">
    <div class="content content-pagex">
        <form>
            <div class="flex flex-wrap mb-1">
                <div class="w-full p-1">
                    <vs-alert color="danger" v-for="(item, key) in CuentasNValidas" :key="key" style="text-align:center;height:40px">
                        {{ item.Nombre }} - {{ item.Cuenta }}
                    </vs-alert>
                </div>
            </div>

            <vs-divider class="label-size">OPCIONES BÁSICAS PARA LA CONTABILIDAD</vs-divider>
            <div class="btn-group">
                <vs-button class="label-size" :type="(Opcion==1)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=1" icon="icon-layers">Cuentas</vs-button>
                <vs-button class="label-size" :type="(Opcion==2)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=2" icon="icon-layers">Hospital</vs-button>
                <vs-button class="label-size" :type="(Opcion==3)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=3" icon="icon-layers">Nómina</vs-button>
                <vs-button class="label-size" :type="(Opcion==4)?'filled':'line'" style="display:inline;height:50px" color="primary" icon-pack="feather" @click="Opcion=4" icon="icon-layers">Ajenos</vs-button>
            </div>
            <!-------------------------CUENTAS----------------- -->
            <div v-show="Opcion==1">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="flex flex-wrap">
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="IvaPorPagar">
                                <label class="label-size"> IVA por Pagar </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas1.IvaPorPagar" :disabled="BloqueoC==true" />
                            </ValidationProvider>
                        </div>
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="IvaPorCobrar">
                                <label class="label-size"> IVA por Cobrar </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas1.IvaPorCobrar" :disabled="BloqueoC==true" />
                            </ValidationProvider>
                        </div>
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="IvaPorCobrarExencion">
                                <label class="label-size"> IVA por Cobrar Exención </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas1.IvaPorCobrarExencion" :disabled="BloqueoC==true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="UtilidadEjercicio">
                                    <label class="label-size"> Utilidad del Ejercicio </label>
                                    <vs-input type="text" class="w-full" v-model="Cuentas1.UtilidadEjercicio" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="PerdidaEjercicio">
                                    <label class="label-size"> Pérdida del Ejercicio </label>
                                    <vs-input type="text" class="w-full" v-model="Cuentas1.PerdidaEjercicio" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="UtilidadAcumulada">
                                    <label class="label-size"> Utilidades Acumuladas </label>
                                    <vs-input type="text" class="w-full" v-model="Cuentas1.UtilidadAcumulada" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="DigitosMayor" rules="max:12" v-slot="{ errors }">
                                    <label class="label-size"> No. Dígitos en las Cuentas de el Mayor </label>
                                    <vs-input type="number" class="w-full" v-model="Cuentas1.DigitosMayor" :disabled="BloqueoC==true" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="DigitosBalance" rules="max:12" v-slot="{ errors }">
                                    <label class="label-size"> No. Dígitos en las Cuentas del Balance General </label>
                                    <vs-input type="number" class="w-full" v-model="Cuentas1.DigitosBalance" :disabled="BloqueoC==true" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="RetencionIsr">
                                    <label class="label-size"> Retención ISR </label>
                                    <vs-input type="text" class="w-full" v-model="Cuentas1.RetencionIsr" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="RadHonorarios">
                                    <label class="label-size"> Honorarios RX </label>
                                    <vs-input type="text" class="w-full" v-model="Cuentas1.RadHonorarios" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="ClientesCredito">
                                    <label class="label-size"> Crédito </label>
                                    <vs-input type="text" class="w-full" v-model="Cuentas1.ClientesCredito" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="Inventario">
                                    <label class="label-size"> Inventario </label>
                                    <vs-input type="text" class="w-full" v-model="Cuentas1.Inventario" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="MontoRetIva">
                                    <label class="label-size"> Monto Retención IVA </label>
                                    <vs-input type="number" class="w-full" v-model="Cuentas1.MontoRetIva" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="ProveedoresInt">
                                    <label class="label-size"> Proveedores Integrados </label>
                                    <vs-input type="text" class="w-full" v-model="Cuentas1.ProveedoresInt" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                            <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                                <ValidationProvider name="ProductosDiversos">
                                    <label class="label-size"> Productos Diversos </label>
                                    <vs-input type="text" class="w-full" v-model="Cuentas1.ProductosDiversos" :disabled="BloqueoC==true" />
                                </ValidationProvider>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div style="float:right;padding:15px 30px">
                                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="Validar()" id="btn_confirmacion" :disabled="BloqueoBC"> Guardar</vs-button>
                                <vs-button color="success" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-edit-1" @click="EditarCuentas()" id="btn_confirmacion"> Editar</vs-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- -----------------------HOSPITAL-------------------->
            <div v-show="Opcion==2">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="flex flex-wrap">
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="PorcentajeIva">
                                <label class="label-size"> Porcentaje IVA </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.PorcentajeIva" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="DescuentosExtra">
                                <label class="label-size"> Descuentos Extra </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.DescuentosExtra" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="EmpleadosxCobrar">
                                <label class="label-size"> Empleados por Cobrar </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.EmpleadosxCobrar" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="VenaPaciente">
                                <label class="label-size"> Cuenta Vena Paciente </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.VenaPaciente" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="ClientesxCobrar">
                                <label class="label-size"> Clientes por Cobrar </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.ClientesxCobrar" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="DoctoresxCobrar">
                                <label class="label-size"> Doctores por Cobrar </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.DoctoresxCobrar" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="CuentaAjena">
                                <label class="label-size"> Cuenta Ajena </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.CuentaAjena" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="Anticipos">
                                <label class="label-size"> Anticipos </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.Anticipos" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="ChequesRechazados">
                                <label class="label-size"> Cheques Rechazados </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.ChequesRechazados" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                        <div class="md:w-4/12 lg:w-4/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="LiquidarDepositos">
                                <label class="label-size"> Liquidar Depósitos </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.LiquidarDepositos" :disabled="BloqueoH==true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div style="float:right;padding:15px 30px">
                                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="ValidarC()" id="btn_confirmacion" :disabled="BloqueoBH"> Guardar</vs-button>
                                <vs-button color="success" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-edit-1" @click="EditarHospital()" id="btn_confirmacion"> Editar</vs-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- ------------------------NOMINA--------------------->
            <div v-show="Opcion==3">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="flex flex-wrap">
                        <div class="md:w-6/12 lg:w-6/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="CategoriaNomina">
                                <label class="label-size"> Categoría Facturación Empleado </label>
                                <vs-input type="number" class="w-full" v-model="Cuentas2.CategoriaNomina" :disabled="BloqueoN==true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="md:w-6/12 lg:w-6/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="PolizaNomina">
                                <label class="label-size"> Póliza de Facturación al Empleado </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.PolizaNomina" :disabled="BloqueoN==true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div style="float:right;padding:15px 30px">
                                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="ValidarC()" id="btn_confirmacion" :disabled="BloqueoBN"> Guardar</vs-button>
                                <vs-button color="success" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-edit-1" @click="EditarNomina()" id="btn_confirmacion"> Editar</vs-button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!-- -----------------------AJENOS--------------------->
            <div v-show="Opcion==4">
                <div style="box-shadow: 2px 2px 4px 1px rgba(0, 0, 0, 0.2);padding:10px;border-radius:5px; border:1px solid #ccc;">
                    <div class="flex flex-wrap">
                        <div class="md:w-6/12 lg:w-6/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="CategoriaAjenos">
                                <label class="label-size"> Categoría Facturación Ajenos </label>
                                <vs-input type="number" class="w-full" v-model="Cuentas2.CategoriaAjenos" :disabled="BloqueoA==true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="md:w-6/12 lg:w-6/12 xl:w-4/12" style="float:left;padding:15px 30px">
                            <ValidationProvider name="PolizaAjenos">
                                <label class="label-size"> Póliza de Facturación al Ajeno </label>
                                <vs-input type="text" class="w-full" v-model="Cuentas2.PolizaAjenos" :disabled="BloqueoA==true" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div style="float:right;padding:15px 30px">
                                <vs-button color="primary" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-save" @click="ValidarC()" id="btn_confirmacion" :disabled="BloqueoBA"> Guardar</vs-button>
                                <vs-button color="success" style="float:right;margin: 5px" type="filled" icon-pack="feather" icon="icon-edit-1" @click="EditarAjenos()" id="btn_confirmacion"> Editar</vs-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</vx-card>
</template>

<script>
import bitacora from '@/components/sermesa/funciones/SMBitacora'
export default {
    data() {
        return {
            Opcion: 1,
            BloqueoC: true,
            BloqueoBC: true, //Button
            BloqueoH: true,
            BloqueoBH: true, //Button
            BloqueoN: true,
            BloqueoBN: true, //Button
            BloqueoA: true,
            BloqueoBA: true, //Button
            TarjetasporCobrar: '',
            RadHonorarios: '',
            DatosNoValidos: 0,
            TituloError: '',
            Cuentas1: {
                ClientesCredito: '',
                DigitosBalance: '',
                DigitosMayor: '',
                Empresa: '',
                Inventario: '',
                IvaPorCobrar: '',
                IvaPorPagar: '',
                RadHonorarios: '',
                IvaPorCobrarExencion: '',
                MontoRetiva: '',
                PerdidaEjercicio: '',
                ProveedoresInt: '',
                RetencionIsr: '',
                UtilidadAcumulada: '',
                UtilidadEjercicio: '',
                ProductosDiversos: ''
            },
            Cuentas2: {
                PorcentajeIva: '',
                DescuentosExtra: '',
                EmpleadosxCobrar: '',
                ClientesxCobrar: '',
                DoctoresxCobrar: '',
                CuentaAjena: '',
                Anticipos: '',
                ChequesRechazados: '',
                LiquidarDepositos: '',
                CategoriaNomina: '',
                CategoriaAjenos: '',
                PolizaNomina: '',
                PolizaAjenos: '',
                VenaPaciente: '',
            },
            CuentasNValidas: []
        };
    },
    mounted() {

        this.ConsultaCuentas()
    },
    watch: {
        Opcion(value) {
            if (value == 1) {
                this.ConsultaCuentas()
                this.BloqueoH = true
                this.BloqueoN = true
                this.BloqueoA = true
                this.BloqueoBH = true
                this.BloqueoBN = true
                this.BloqueoBA = true
            }
            if (value == 2) {
                this.ConsultaContabilidad()
                this.BloqueoC = true
                this.BloqueoN = true
                this.BloqueoA = true
                this.BloqueoBC = true
                this.BloqueoBN = true
                this.BloqueoBA = true
            }
            if (value == 3) {
                this.ConsultaContabilidad()
                this.BloqueoC = true
                this.BloqueoH = true
                this.BloqueoA = true
                this.BloqueoBC = true
                this.BloqueoBH = true
                this.BloqueoBA = true
            }
            if (value == 4) {
                this.ConsultaContabilidad()
                this.BloqueoC = true
                this.BloqueoH = true
                this.BloqueoN = true
                this.BloqueoBC = true
                this.BloqueoBH = true
                this.BloqueoBN = true
            }

        }

    },
    methods: {
        EditarCuentas() {
            this.BloqueoC = false
            this.BloqueoBC = false
        },
        EditarHospital() {
            this.BloqueoH = false
            this.BloqueoBH = false
        },
        EditarNomina() {
            this.BloqueoN = false
            this.BloqueoBN = false
        },
        EditarAjenos() {
            this.BloqueoA = false
            this.BloqueoBA = false
        },
        ConsultaCuentas() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCuentasContabilidad', {
                    Opcion: "C",
                    SubOpcion: "1"
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.Cuentas1 = resp.data.json[0]
                        this.Cuentas1.MontoRetIva = parseFloat(this.Cuentas1.MontoRetIva).toFixed(2)

                        setTimeout(() => {
                            bitacora.registrar(this.Cuentas1, {
                                Tipo: 'Modificación'
                            })
                        }, 2000)
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })

                .catch(() => {})
        },
        ConsultaContabilidad() {
            this.axios.post('/app/v1_contabilidad_general/ConsultaCuentasContabilidad', {
                    Opcion: "C",
                    SubOpcion: "2"
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {
                        this.Cuentas2 = resp.data.json[0]
                        setTimeout(() => {
                            bitacora.registrar(this.Cuentas2, {
                                Tipo: 'Modificación'
                            })
                        }, 2000)
                    } else {
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'No se ha podido cargar las cuentas, favor validar.',
                        })
                    }
                })
                .catch(() => {})
        },

        GuardarHospital() {
            if (this.CuentasNValidas.length == 0) {

                let valor2 = bitacora.obtener()
                if (valor2 == null) {
                    this.BloqueoH = true
                    this.BloqueoBC = true
                    this.ConsultaContabilidad()
                } else if (valor2 != null) {
                    this.axios.post('/app/v1_contabilidad_general/ModificacionHospital', {
                            ...this.Cuentas2,
                            Opcion: 'U',
                            SubOpcion: '2'
                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Cuentas',
                                    text: resp.data.mensaje,
                                })
                            } else {
                                this.axios.post('/app/bitacora/registro_bitacora', {
                                    info: valor2,
                                    tabla: 'Contabilidad',
                                    llave: {
                                        Empresa: this.Cuentas2.Empresa
                                    }
                                })
                                this.ConsultaContabilidad()
                                this.BloqueoH = true
                                this.BloqueoBH = true
                            }
                        })
                }

            }
        },
        GuardarNomina() {
            if (this.CuentasNValidas.length == 0) {

                let valor2 = bitacora.obtener()
                if (valor2 == null) {
                    this.BloqueoN = true
                    this.BloqueoBN = true
                    this.ConsultaContabilidad()
                } else if (valor2 != null) {
                    this.axios.post('/app/v1_contabilidad_general/ModificacionNomina', {
                            ...this.Cuentas2,
                            Opcion: 'U',
                            SubOpcion: '3'
                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Cuentas',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.axios.post('/app/bitacora/registro_bitacora', {
                                    info: valor2,
                                    tabla: 'Contabilidad',
                                    llave: {
                                        Empresa: this.Cuentas2.Empresa
                                    }
                                })
                                this.ConsultaContabilidad()
                                this.BloqueoN = true
                                this.BloqueoBN = true
                            }
                        })
                }
            }

        },
        GuardarAjenos() {
            if (this.CuentasNValidas.length == 0) {
                let valor2 = bitacora.obtener()
                if (valor2 == null) {
                    this.BloqueoA = true
                    this.BloqueoBA = true
                    this.ConsultaContabilidad()
                } else if (valor2 != null) {

                    this.axios.post('/app/v1_contabilidad_general/ModificacionAjenos', {
                            ...this.Cuentas2,
                            Opcion: 'U',
                            SubOpcion: '4'
                        })
                        .then(resp => {
                            this.$vs.loading.close();
                            if (resp.data.codigo != 0) {
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Cuentas',
                                    text: resp.data.mensaje,
                                })

                            } else {
                                this.axios.post('/app/bitacora/registro_bitacora', {
                                    info: valor2,
                                    tabla: 'Contabilidad',
                                    llave: {
                                        Empresa: this.Cuentas2.Empresa
                                    }
                                })
                                this.ConsultaContabilidad()
                                this.BloqueoA = true
                                this.BloqueoBA = true
                            }
                        })

                }
            }
        },
        Validar() {
            this.CuentasNValidas = []
            this.axios.post('/app/v1_contabilidad_general/ValidacionCuentas', {
                    ...this.Cuentas1,
                    Opcion: 'C',
                    SubOpcion: '3'
                })
                .then((resp) => {
                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        resp.data.json.map(data => {
                            this.CuentasNValidas.push({
                                ...data
                            })
                            this.CuentasNValidas = this.CuentasNValidas.filter(ab => ab.Cuenta != '');
                        })
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'Validar Cuentas, o no existen o no es correcta.',
                        })

                    } else if (this.CuentasNValidas.length == 0) {
                        if (this.Cuentas1.DigitosMayor > 12 || this.Cuentas1.DigitosBalance > 12) {
                            this.$vs.notify({
                                color: 'danger',
                                title: 'Cuentas',
                                text: 'El No. de Dígitos debe ser menor o igual a 12',
                            })
                            return;
                        } else {
                            let valor = {
                                ...this.Cuentas1
                            }
                            valor = bitacora.obtener()
  

                            if (valor != null) {
                                this.axios.post('/app/v1_contabilidad_general/ModificacionCuentas', {
                                        ...this.Cuentas1,
                                        Opcion: 'U',
                                        SubOpcion: '1'
                                    })
                                    .then(resp => {
                                        if (resp.data.codigo != 0) {
                                            this.$vs.notify({
                                                color: '#B71C1C',
                                                title: 'Cuentas',
                                                text: resp.data.mensaje,
                                            })
                                        } else {
                                            if (valor.indexOf("ProductosDiversos") !== -1) {
                                                this.axios.post('/app/bitacora/registro_bitacora', {
                                                    info: valor,
                                                    tabla: 'CuentasDefault - Contabilidad',
                                                    llave: {
                                                        Empresa: this.Cuentas1.Empresa
                                                    }
                                                })
                                            } else {
                                                this.axios.post('/app/bitacora/registro_bitacora', {
                                                    info: valor,
                                                    tabla: 'CuentasDefault',
                                                    llave: {
                                                        Empresa: this.Cuentas1.Empresa
                                                    }
                                                })
                                            }
                                        }
                                        this.ConsultaCuentas()
                                        this.BloqueoC = true
                                        this.BloqueoBC = true
                                    })
                            } else {
                                this.ConsultaCuentas()
                                this.BloqueoC = true
                                this.BloqueoBC = true
                            }
                        }

                    }
                })
        },

        ValidarC() {
            this.CuentasNValidas = []
            this.axios.post('/app/v1_contabilidad_general/ValidacionCotabilidad', {
                    ...this.Cuentas2,
                    Opcion: 'C',
                    SubOpcion: '4'
                })
                .then((resp) => {

                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        resp.data.json.map(data => {
                            this.CuentasNValidas.push({
                                ...data
                            })
                            this.CuentasNValidas = this.CuentasNValidas.filter(ab => ab.Cuenta != '');
                        })
                        this.$vs.notify({
                            color: '#B71C1C',
                            title: 'Cuentas',
                            text: 'Validar Cuentas, o no existen o no es correcta.',
                        })

                    } else {
                        if (this.Opcion == 2) {
                            this.GuardarHospital()
                        } else if (this.Opcion == 3) {
                            this.GuardarNomina()
                        } else if (this.Opcion == 4) {
                            this.GuardarAjenos()
                        }

                    }
                })

        }

    },
}
</script>   

<style scoped>
.footer {
    height: 50px;
    margin-top: -65px;
    margin-left: 10px;
}

.label-size {
    font-size: 14px;
    font-weight: bold;
}
</style>
