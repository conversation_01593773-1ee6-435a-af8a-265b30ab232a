<template>
<div>
    <vx-card title="Pago de Cuenta Ajena">
        <!-- <ValidationObserver ref="formValidate" v-slot="{ invalid,handleSubmit }" mode="lazy"> -->
        <div class="flex mb-5 ">
            <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                <vs-input label="Fecha de Última Validación" type="datetime" style="padding:0" disabled="false" class="w-full" v-model="info.ultimaFecha" />
            </div>
            <!-- <vs-button color="primary" style="height:37px;margin-top:25px" type="filled" @click.native="Consulta().Consulta_Pagos()">Generar Planilla</vs-button> -->
        </div>

        <div class="flex flex-wrap">

            <div>
                <vs-input label="Fecha Inicial:" type="date" v-model="FechaInicial" name="date1" />
            </div>

            <div>
                <vs-input label="Fecha Final:" type="date" v-model="FechaFinal" name="date1" />
            </div>
            <div class="xs:w-full md:w-full lg:w-full xl:w-4/12 m-5">
                <vs-button color="primary" type="filled" @click.native="Consulta().Consulta_Pagos()">Generar Planilla</vs-button>
            </div>

        </div>
        <vs-divider></vs-divider>
        <!-- {{agrupar}} -->
        <vs-table2 max-items="10" search tooltip pagination :data="agrupar" exportarExcel="Planilla Cuenta Ajena">
            <template slot="thead">
                <th order="NombreSucursal" width="120px">Hospital</th>
                <th order="Admision" width="120px">Admision</th>
                <th order="NombrePaciente">Paciente</th>
                <th order="Medico">Médico</th>
                <th order="Especialidad">Especialidad</th>
                <th order="TipoMedico" width="130px">Tipo Médico</th>
                <th order="Factura" width="130px">Factura</th>
                <th order="Valor" orderType="number" width="150px">Valor Facturación</th>
                <th width="170px">Acción</th>
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data" :class="[(tr.FacturaFecha)?'factura':'']">
                    <vs-td2>
                        {{ tr.NombreSucursal.toUpperCase() }}
                    </vs-td2>

                    <vs-td2>
                        {{tr.SerieAdmision}}{{tr.Admision}}
                    </vs-td2>

                    <vs-td2>
                        {{tr.Cita_Cliente ? tr.NombreCliente : tr.NombrePaciente }}
                    </vs-td2>

                    <vs-td2>
                        ({{tr.Ajeno}}) {{ tr.NombreMedico }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.MedicoEspecialidad }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.TipoMedico }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.FacturaSerie }} {{tr.FacturaNumero}}
                    </vs-td2>

                    <vs-td2>
                        <div style="text-align:right">
                            {{ $formato_moneda(tr.ValorCalculado) }}
                        </div>
                    </vs-td2>

                    <vs-td2 noTooltip noExcel>
                        <vs-button color="primary" size="small" type="filled" style="display:inline" @click.native="Otros().factura(tr)"> <i class="fas fa-plus"></i> Factura</vs-button>
                        <vx-tooltip v-if="tr.DenegarMotivo" style="display:inline;position:relative;top:5px;left:5px" delay=".5s" :text="tr.DenegarMotivo">
                            <i class="fas fa-exclamation-circle" style="color:rgb(230, 126, 34);font-size:22px"></i>
                        </vx-tooltip>
                    </vs-td2>
                </tr>
            </template>
        </vs-table2>
        <vs-divider></vs-divider>
        <div class="flex bottom">
            <vs-spacer></vs-spacer>
            <vs-button color="success" style="float:right" type="filled" @click.native="(pagos.length>0)?Guardar().lote():null" :disabled="!pagos || pagos.filter(f=>f.FacturaFecha).length==0">Validar Planilla</vs-button>
        </div>

        <!-- </ValidationObserver> -->
    </vx-card>
    <div class="facturas" v-if="info.MostrarFactura">
        <Facturas v-model="info" :cancelar="Otros().facturasCancelar" :tabla="info.Facturas" :CuentaAjena="Otros().facturaSeleccion" />
    </div>
</div>
</template>

<script>
import moment from "moment"
export default {
    data() {
        return {
            /**
             * Documentación del módulo
             */
            doc_: [
                'Genera el listado de todos los cargos de cuenta ajena validados por las reglas de pago.',
                'Al seleccionar <b>Generar Planilla</b> se descarga la información de los cargos (98,99) que cumplen las reglas de pago',
                'Los cargos mostrados estan en estado <b>No Pagado</b> y no se encuentran asignados a un lote',
                'Los lotes generados de los cargos seleccionados llevan la siguiente nomenclatura: CA + Tipo de Cuenta (Deposito o Cheque) + Hospital + Año + Correlativo',
                '* Se obtienen los datos del sp <i>sp_Ajenos_Honorarios_PagosHospital</i>.',
                '** Validar bitacora'
            ],

            /**
             * Editar el monto de la tabla
             */
            info: {
                Codigo: null,
                Medico: null,
                NIT: null,
                Tipo: null,
                Especialidad: null,
                SubEspecialidad: null,
                FacturaFecha: null,
                FacturaSerie: null,
                FacturaNumero: null,
                Total: '0.00',
                MostrarFactura: false,
                Facturas: []
            },

            /**
             * Listado de permisos
             */
            permisos: {
                editar: false
            },

            /**
             * Información de los pagos
             */
            pagos: [],
            pagosSeleccion: [],
            FechaInicial: '',
            FechaFinal: ''
        }
    },
    computed: {
        calculcar() {
            const arr = this.pagos.filter(f => f.seleccion)
            return this.$formato_moneda(arr.reduce((acumulador, valor) => acumulador + parseFloat(valor.Valor), 0))
        },
        agrupar() {
            const datos = []
            let id = null
            this.pagos.forEach(item => {
                const id_ = item.Ajeno + '_' + item.SerieAdmision + '_' + item.Admision
                item.idGrupo = id_
                if (id != id_) {
                    item.ValorCalculado = parseFloat(item.Valor)
                    datos.push(item)
                } else {
                    const anterior = datos[datos.length - 1]
                    anterior.ValorCalculado = parseFloat(anterior.ValorCalculado) + parseFloat(item.Valor)
                }
                id = id_
            })
            return datos
        }
    },
    components: {
        Facturas: () => import('./AJE005_Factura.vue')
    },
    mounted() {
        let b = new Date();
        this.FechaInicial = moment(b).format('YYYY-MM-DD')
        this.FechaFinal = moment(b).format('YYYY-MM-DD')

    },
    methods: {

        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function () {
            return {
                Consulta_Pagos: () => {
                    
                    if ((this.FechaFinal == '') || (this.FechaInicial == '')) {
                        this.$vs.dialog({
                            color: 'danger',
                            title: 'Reglas',
                            acceptText: 'Aceptar',
                            cancelText: 'Cancelar',
                            text: `Debe seleccionar fecha de inicio y fecha final.`,
                            buttonCancel: false,
                        })
                        return;
                    }
                    
                    this.pagos = []
                    return this.axios.post('/app/ajenos/Honorarios_Pagos_Consulta', {
                            TipoPlanilla: 'C', // Cuenta Ajena,
                            FechaInicio: this.FechaInicial,
                            FechaFinal: this.FechaFinal
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.pagos = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        cargoFactura: false,
                                        FacturaFecha: null,
                                        FacturaNumero: null,
                                        FacturaSerie: null
                                    }
                                })
                            }
                        })
                },

                Consulta_UltimaActualizacion: (Ajeno) => {
                    return this.axios.post('/app/ajenos/HonorariosConsultaEstados', {
                            Opcion: 'A',
                            Ajeno
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.info.FacturaFecha = resp.data.json[0].Fecha
                            }
                        })
                }
            }
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function () {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function () {
            return {
                lote: () => {
                    // this.pagos.
                    return this.axios.post('/app/ajenos/Honorarios_Lotes_Nuevo', {
                            Tipo: 'D',
                            CuentaAjeCargos: this.pagos.filter(f => f.FacturaFecha).map(m => {
                                return {
                                    Id: m.Id,
                                    FacturaFecha: m.FacturaFecha,
                                    FacturaNumero: m.FacturaNumero,
                                    FacturaSerie: m.FacturaSerie
                                }
                            })
                        })
                        .then(() => {

                            this.Consulta().Consulta_Pagos()
                            this.Consulta().Consulta_UltimaActualizacion()
                        })
                        .catch(() => {

                        })
                }
            }
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function () {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function () {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function () {
            return {
                Limpiar_Datos: () => {
                    Object.entries(this.info).forEach(([key]) => {
                        this.info[key] = null
                    });
                    this.pagos = []
                },
                factura: (item) => {
                    this.info.Facturas = []
                    this.info.Facturas = this.pagos.filter(f => f.idGrupo == item.idGrupo)
                    this.axios.post('/app/ajenos/Consulta_Ajenos', {
                            Codigo: item.Ajeno
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                let datos = resp.data.json[0]
                                // Remplazando el tipo  si se ha seleccionado tipocasacortesia
                                if (datos['TipoCortesiaCasa'] == true) datos['Tipo'] = 'CC'
                                
                                this.info.Tipo = datos.Tipo
                                this.info.Especialidad = datos.EspecialidadPrincipal.trim()
                                this.info.SubEspecialidad = datos.Especialidad.trim()
                            }
                        })
                    this.info.MostrarFactura = true
                    this.info.Codigo = item.Ajeno
                    this.info.Medico = item.NombreMedico
                },
                facturaSeleccion: (item) => {
                    this.Otros().facturasCancelar(false)
                    this.info.Facturas.map(m => {
                        m.FacturaFecha = item.FacturaFecha
                        m.FacturaNumero = item.FacturaNumero
                        m.FacturaSerie = item.FacturaSerie
                    })
                },
                facturasCancelar: (actualizar = false) => {
                    this.info.MostrarFactura = false
                    this.info.Tipo = null
                    this.info.Especialidad = null
                    this.info.SubEspecialidad = null

                },
            }
        }
    }
}
</script>

<style scoped>
.facturas {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    /* overflow: auto; */
    background-color: rgba(0, 0, 0, 0.5);
    padding: 50px;
    z-index: 99999;
}

.factura {
    background-color: rgba(46, 204, 113, 0.4) !important
}
</style>
