<template>
<DxDataGrid :ref="dataGridDetalle" v-bind="DefaultDxGridConfiguration" :width="'auto'" :searchPanel="{visible: false}" :data-source="detalleDataSource" :height="'100%'" @editor-preparing="editorDetalleValue">
    <DxSelection mode="single" />

    <DxToolbar>
        <DxItemTool name="groupPanel" />
        <DxItemTool :location="'before'" template="opcionesDetalle" />
    </DxToolbar>

    <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup">
        <DxPopup v-bind="popupConf" :width="400" :height="450" :title="addComentario?'Agregar comentario':'Agregar detalle'">
            <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                            text: 'Guardar',
                                            type: 'success',
                                            useSubmitBehavior: true,
                                            onClick: submitDetalle
                                        }" location="after">
            </DxToolbarItem>
            <DxToolbarItem widget="dxButton" toolbar="bottom" :options="{
                                            text: 'Cancelar',
                                            type: 'danger',
                                            useSubmitBehavior: true,
                                            onClick: cancelDetalle
                                        }" location="after">
            </DxToolbarItem>
        </DxPopup>

        <DxForm labelMode="floating" :ref="dataAddDetalle">
            <DxItem data-field="FechaHora" editor-type="dxDateBox" :use-mask-behavior="true" :col-span="2" :is-required="true" :editor-options="dateBoxDetalle" :visible="!addComentario">
                <DxLabel :text="'Fecha de aplicación'" />
            </DxItem>
            <DxItem data-field="Observacion" editor-type="dxTextArea" :editor-options="{height:200}" :col-span="2" :is-required="true">
                <DxLabel :text="'Observación de la orden médica'" />
            </DxItem>
        </DxForm>
    </DxEditing>

    <DxColumn :width="50" cell-template="comentario" alignment="center" />

    <DxColumn :width="200" data-field="FechaHora" caption="Fecha aplicación" format="HH:mm dd/MM/yyyy" data-type="date" alignment="center" />
    <DxColumn :width="200" data-field="FechaRegistro" caption="Fecha registro" format="dd/MM/yyyy HH:mm" data-type="date" alignment="center" />
    <DxColumn :width="400" data-field="Observacion" caption="Observación aplicación" cell-template="observacion" alignment="center" />
    <DxColumn :width="200" data-field="Usuario" caption="Usuario" alignment="center" />

    <template #opcionesDetalle>
        <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[]" @add="addRowDetalle" :showItems="['add']" :report-param="$props" />
    </template>
    <template #comentario="{data}">
        <div style="display: flex; flex-wrap: wrap; justify-content: center;">
            <vs-tooltip text="Agregar comentario" position="bottom" style="cursor: pointer;">
                <font-awesome-icon :icon="['far', 'comment-dots']" style="font-size: 20px; color: #00008B;" @click="validarComentario(data)" />
            </vs-tooltip>
        </div>
    </template>
    <template #observacion="{data}">
        <div style="display: flex; white-space: pre-wrap; flex-wrap: wrap; text-align: left;">
            <span>{{data.data.Observacion}}</span>
        </div>
    </template>
</DxDataGrid>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxEditing,
    DxForm,
    DxToolbar,
    DxLabel,
    DxPopup,
    DxSelection,
    DxItem as DxItemTool
} from 'devextreme-vue/data-grid'
import CustomStore from 'devextreme/data/custom_store'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import {
    DxItem
} from 'devextreme-vue/form'
import 'devextreme-vue/lookup'
import {
    DxToolbarItem
} from 'devextreme-vue/popup'
import {
    DefaultDxGridConfiguration,
    popupConf
} from './data'

const dataGridDetalle = 'gridDetalle'
const dataAddDetalle = 'addDetallePop'

export default {
    name: 'ControlOrdenesDetalle',
    props: {
        orden: null,

        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
    },
    components: {
        DxDataGrid,
        DxColumn,
        ExpedienteGridToolBar,
        DxEditing,
        DxForm,
        DxItem,
        DxToolbar,
        DxPopup,
        DxLabel,
        DxToolbarItem,
        DxSelection,
        DxItemTool
    },
    data() {
        return {
            detalle: [],
            detalleDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.detalle;
                },
                insert: (values) => {
                    if (!this.addComentario) {
                        this.guardarDetalle(values);
                    } else {
                        this.agregarComentario(values)
                    }
                },
            }),
            dataGridDetalle,
            dataAddDetalle,
            DefaultDxGridConfiguration,
            dateBoxDetalle: '',
            addComentario: false,
            idDetalle: '',
            comentarioAdd: '',
            popupConf,
        }
    },
    methods: {
        submitDetalle() {
            this.dataGridDetalleOrden.saveEditData()
        },
        cargarDetalle(Orden) {
            this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaOrdenesMedicamentoDetalle', {
                    IdOrden: Orden
                })
                .then(resp => {
                    this.detalle = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            IdOrdenDetalle: x.IdOrdenDetalle,
                            Hora: x.HoraAplicacion,
                            FechaAplicacion: x.FechaAplicacion,
                            FechaHora: x.FechaHoraAplicacion,
                            FechaRegistro: x.FechaRegistro,
                            Observacion: this.$limpiar_saltos_tabulares(x.ObservacionAplicacion),
                            Usuario: x.PersonaAplica
                        }
                    })
                    this.detalleDataSource.load().then(
                        () => {
                            this.refreshDetalleDataGrid()
                        },
                    )
                })
        },
        addRowDetalle() {
            if (this.StatusAdmision != 'A')
                this.$vs.notify({
                    time: 4000,
                    title: 'Antecedentes',
                    text: 'La admisión no está activa, no se puede agregar nuevo antecedente',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
            else
                this.dataGridDetalleOrden.addRow()
        },
        refreshDetalleDataGrid() {
            this.dataGridDetalleOrden.refresh()
                .then(function () {

                })
                .catch(function () {
                });
            this.dataGridDetalleOrden.pageIndex(0);
            this.dataGridDetalleOrden.clearSelection();
            this.dataGridDetalleOrden.clearSorting();
            this.dataGridDetalleOrden.clearFilter();
            this.dataGridDetalleOrden.columnOption('FechaHora', 'sortOrder', 'asc');
            this.idDetalle = ''
            this.addComentario = false
        },
        cancelDetalle() {
            this.dataGridDetalleOrden.cancelEditData()
            this.addComentario = false
        },
        editorDetalleValue(e) {
            if (e.parentType === 'dataRow' && e.dataField === 'FechaHora') {
                e.setValue(new Date());
                e.editorOptions.value = new Date();
            }
        },
        guardarDetalle(values) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                IdOrden: this.orden,
                Comentario: values.Observacion,
                FechaHoraAplicacion: values.FechaHora,
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/InsertarAplicacionMedicamento", {
                ...postData
            }).then(() => {
                this.addComentario = false
                this.cargarDetalle(this.orden)
            })

        },
        validarComentario(e) {
            this.idDetalle = e.data.IdOrdenDetalle
            this.addComentario = true
            this.addRowDetalle()
        },
        agregarComentario(values) {
            let postData = {
                SerieAdmision: this.SerieAdmision,
                Admision: this.CodigoAdmision,
                IdOrdenDetalle: this.idDetalle,
                Comentario: values.Observacion,
            }
            this.axios.post("/app/v1_ExpedienteEvolucion/ComentarAplicacionMedicamento", {
                ...postData
            }).then(() => {
                this.addComentario = false
                this.idDetalle = ''
                this.cargarDetalle(this.orden)
            })
        }
    },
    mounted() {
        this.dateBoxDetalle = {
            max: new Date(),
            value: new Date(),
            type: "datetime"
        }
        this.cargarDetalle(this.orden);
    },
    computed: {

        dataGridDetalleOrden: function () {
            return this.$refs[dataGridDetalle].instance;
        },
    },
}
</script>
