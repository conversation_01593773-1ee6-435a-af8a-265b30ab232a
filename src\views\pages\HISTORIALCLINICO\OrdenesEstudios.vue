<template>
<div class="p-2">
    <vx-card>
        <div class="p-2">
            <vs-button v-if="!Consulta" class="button" color="success" type="filled" style="width: 200px; size: 20px" @click="AbrirModal">
                <font-awesome-icon :icon="['fas', 'clipboard-list']" class="pr-2" style="align-items: center" />
                <span>Ingresar orden de estudio</span>
            </vs-button>
        </div>
        <div>
            <DxDataGrid v-bind="DefaultDxGridConfiguration" :data-source="ordenes" :filter-sync-enabled="true" :headerFilter="{ visible: true , allowSearch: true }" :height="'100%'">
                <DxDataGridSelection mode="single" />

                <DxDataGridColumn :width="50" alignment="center" cell-template="reporte" />

                <DxDataGridColumn :width="100" data-field="IdSolicitud" :allowHeaderFiltering="false" caption="Orden" data-type="string" alignment="center" css-class="classTexto" />
                <DxDataGridColumn :width="120" data-field="FechaOrden" caption="Fecha" format="dd/MM/yyyy" data-type="date" alignment="center" css-class="classTexto" />
                <DxDataGridColumn :width="200" data-field="NombreMedico" :allowHeaderFiltering="false" caption="Médico" data-type="string" alignment="center" css-class="classTexto" />

                <template #reporte="{data: info}">
                    <!-- <vs-tooltip text="Imprimir orden" position="bottom" style="cursor: pointer;">
                        <font-awesome-icon :icon="['fas', 'file-pdf']" style="font-size: 20px; color: #eb5454;" @click="mostrarReporte(info.data.IdSolicitud)" />
                    </vs-tooltip> -->
                    <DxButton width="30px" height="30px" type="danger" styling-mode="contained" class="botonReporte" @click="MostrarReporte(info.data)" hint="Imprimir orden">
                        <font-awesome-icon :icon="['fas', 'file-pdf']" style="font-size: 20px;" />
                    </DxButton>
                </template>
            </DxDataGrid>
        </div>
    </vx-card>

    <!-- Popup de Orden de Estudio -->
    <DxPopup :visible.sync="mostrarModal" :width="'95%'" :height="'95%'" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Orden de Estudios" :showCloseButton="true">
        <DxScrollView :scroll-by-conent="true">
            <div>
                <ModalOrdenes ref="Modal" v-bind="$props" @OrdenCorrecta="OrdenCorrecta" />
            </div>
        </DxScrollView>
    </DxPopup>

    <DxPopup :visible="mostrarMensaje" width="40%" height="auto" :show-title="false">
        <template #content>
            <div class="mensajePopup">
                <span class="mb-4 titulo">¡Solicitud guardada con éxito!</span>
                <span v-if="envioCorrecto" class="mb-4 subtitulo">Esta solicitud de exámenes ha sido enviada al paciente por correo electrónico.</span>
                <span v-else class="mb-4 subtitulo" style="color: red">No se ha podido enviar el correo ya que el usuario no tiene una dirección de correo registrado.</span>
                <span class="mb-2 pregunta">¿Desea imprimir la solicitud de estudios?</span>

                <div class="mt-4" style="display: flex; justify-content: center;">
                    <vs-button class="m-2" color="success" type="filled" style="width: 200px; size: 20px" @click="() => {MostrarReporte(); mostrarMensaje = false}">
                        <!-- <font-awesome-icon :icon="['fas', 'credit-card']" class="pr-2" style="font-size: 16px" /> -->
                        <span>Aceptar</span>
                    </vs-button>

                    <vs-button class="m-2" color="danger" type="filled" style="width: 200px; size: 20px" @click="NoMostrarReporte">
                        <!-- <font-awesome-icon :icon="['fas', 'credit-card']" class="pr-2" style="font-size: 16px" /> -->
                        <span>Cancelar</span>
                    </vs-button>
                </div>
            </div>
        </template>
    </DxPopup>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'

import ModalOrdenes from './ModalOrdenes.vue'

import {
    comandoReceta
} from './data'

export default {
    name: 'Receta',
    components: {
        ModalOrdenes
    },
    data() {
        return {
            DefaultDxGridConfiguration,
            ordenes: [],
            comandoReceta,
            comando: '',
            mostrarModal: false,

            parametrosReporte: [],

            mostrarMensaje: false,

            IdSolicitud: null,

            envioCorrecto: false
        }
    },
    props: {
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdAfiliado: null,
        IdConsulta: null,
        NombreMedico: null,
        CodigoMedico: null,
        NivelPreciosExt: null,
        CodigoDiagnostico: null,
        Telefono: null,
        Correo: null,
        Asegura: null,

        Consulta: null // Variable para saber si se está llamando desde la búsqueda, y determinar si mostrar el botón para ingresar nuevas órdenes 
    },
    methods: {
        CargarOrdenes() {
            this.axios.post('/app/v1_autorizaciones/ConsultaAutorizaciones', {
                    IdCliente: this.IdCliente
                })
                .then(resp => {
                    this.ordenes = resp.data.json
                })
        },
        LimpiarVariables() {
            this.ordenes = []
            this.$refs.Modal.LimpiarVariables()
        },
        AbrirModal() {
            if ((this.Asegura != null && this.Asegura != '') && (this.NivelPreciosExt != null && this.NivelPreciosExt != '')) {
                this.mostrarModal = true
                this.$refs.Modal.CargarProductos()
            } else {
                this.CargarDatos()
            }

        },

        async OrdenCorrecta(e) {
            this.IdSolicitud = e
            this.mostrarModal = false
            this.$refs.Modal.LimpiarVariables()

            const emailRegex = new RegExp(/^[A-Za-z0-9_!#$%&'*+/=?`{|}~^.-]+@[A-Za-z0-9.-]+$/, "gm")

            if (this.Correo !== null && this.Correo !== '' && emailRegex.test(this.Correo)) {
                await this.$genera_reporte_envio({
                    Nombre: 'SolicitudEstudios',
                    Data_source: {
                        IdSolicitud: this.IdSolicitud,
                        IdCita: this.IdCita,
                        IdCliente: this.IdCliente,
                        IdAfiliado: this.IdAfiliado,
                        EnviarA: this.Correo,
                        Asunto: 'Solicitud de Estudios',
                        NombreDelArchivo: 'SolicitudEstudios_' + this.IdSolicitud + '.pdf'
                    },
                    Data_report: this.parametrosReporte
                })

                this.envioCorrecto = true
            } else {
                this.envioCorrecto = false
            }
            this.mostrarMensaje = true

        },
        async MostrarReporte(e) {
            if (e !== undefined && e !== null) {
                await this.$reporte_modal({
                    Nombre: 'SolicitudEstudios',
                    Opciones: {
                        IdSolicitud: e.IdSolicitud,
                        IdCita: e.IdCita,
                        IdCliente: e.IdCliente,
                        IdAfiliado: e.Afiliado
                    }
                })
            } else {
                await this.$reporte_modal({
                    Nombre: 'SolicitudEstudios',
                    Opciones: {
                        IdSolicitud: this.IdSolicitud,
                        IdCita: this.IdCita,
                        IdCliente: this.IdCliente,
                        IdAfiliado: this.IdAfiliado
                    }
                })
            }

            if (e === undefined || e === null) {

                this.CargarOrdenes()
            }
        },
        NoMostrarReporte() {
            this.CargarOrdenes()
            this.mostrarMensaje = false
        },

        async CargarDatos() {
            await this.axios.post('/app/v1_Historial_Clinico/BusquedaInformacionSeguro', {
                    IdCita: this.IdCita
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        let e = resp.data.json[0]
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Paciente Inactivo',
                            acceptText: 'Aceptar',
                            text: 'No es posible ingresar estudios para el paciente ' + e.Nombre + ' - Solicitud de Estudios Requiere Orden Física',  
                            buttonCancel: 'border',
                            clientWidth: 100,
                            accept: () => {},
                        })
                    }
                })
        },
    },
    mounted() {
        this.CargarOrdenes()
    },
    async beforeCreate() {
        this.parametrosReporte = await this.$recupera_parametros_reporte('SolicitudEstudios')
    },
    watch: {
        'IdCliente'(newval) {
            if (newval !== '' && newval !== null && newval !== undefined) {
                this.CargarOrdenes()
            }
        },
    },
}
</script>

<style scoped>
.classTexto {
    vertical-align: middle !important;
}

.mensajePopup {
    display: grid;
    /* place-items: center; */
    justify-content: center;
    align-items: center;
    text-align: center;
    color: black;
}

.titulo {
    font-size: 2.25em;
    font-weight: bold;
    color: #045782
}

.subtitulo {
    font-size: 1.75rem;
}

.pregunta {
    font-size: 1.25rem;
}
</style>
