<template>
<div class="flex flex-wrap">
    <InfoP class="info-expediente-container" ref="componentDatosAdicionales" :visible="Mostrar_Admision" :titulo="'Datos Adicionales de la Admisión'" :Datos="DatosAdicionales" :mostrarDoc="false" :mostrarBitacora="false" tabColor="rgb(201, 32, 178)"> </InfoP>
    <div class="flex-none w-fit pt-6 pl-1 pr-1 info-enc-height" v-if="No_Admision === ''">
        <vs-tooltip text="Buscar Paciente">
            <vs-button color="primary" size="small" icon-pack="feather" icon="icon-search" @click="BuscarPaciente"></vs-button>
        </vs-tooltip>
    </div>
    <vs-popup :active.sync="popupBusquedaPaciente" title="Búsqueda de pacientes">
        <BusquedaPacientes v-bind="$props" ref="busquedaPop" @admision="ingresarAdmisionPopup" />
    </vs-popup>
    <div class="flex-none w-fit p-1">
        <Transition v-on:before-enter="beforeEnter" v-on:enter="enter" v-on:leave="leave" v-bind:css="false">
            <div v-if="Mostrar_Admision">
                <h4 style="font-weight: 700;">{{ Encabezado.Nombre + ' ' + Encabezado.Apellido}} </h4>
            </div>
        </Transition>
        <vx-input-group class="w-auto">
            <vs-tooltip text="Ingrese la serie seguido del número de la admisión, o bien el número de habitacion">
                <vs-input class="w-full" size="small" @keyup="No_Admision=No_Admision.toUpperCase()" v-model="No_Admision" name="Admision" label-placeholder="Número de admisión " @change="CargarAdmision()" :disabled="Mostrar_Admision" />
            </vs-tooltip>
  
            <template slot="append">
                <div class="append-text btn-addon pt-5">
                    <vs-tooltip text="Buscar datos de la admisión" v-if="!Mostrar_Admision">
                        <vs-button clientWidth="42" size="small" color="success" icon-pack="fas" @click="CargarAdmision()" icon="fa-share"></vs-button>
                    </vs-tooltip>

                    <vs-tooltip v-if="Mostrar_Admision" text="Limpiar datos" class="vs-button vs-button-danger vs-button-filled includeIcon includeIconOnly small">
                        <vs-button clientWidth="42" size="small" color="danger" icon-pack="feather" @click="LimpiarAdmision()" icon="icon-x"></vs-button>
                    </vs-tooltip>
                    <vs-tooltip v-if="Mostrar_Admision" text="Ver datos adicionales de la admisión y expediente" class="vs-button vs-button-primary vs-button-filled includeIcon includeIconOnly small">
                        <vs-button clientWidth="42" size="small" color="primary" icon-pack="fas" @click="MostrarDatosAdmision()" icon="fa-info-circle"></vs-button>
                    </vs-tooltip>

                    <vs-tooltip v-if="Mostrar_Admision" text="Recargar datos de la admisión" class="vs-button vs-button-filled includeIcon includeIconOnly small">
                        <vs-button clientWidth="42" size="small" color="warning" icon-pack="feather" @click="CargarAdmision()" icon="icon-refresh-ccw"></vs-button>
                    </vs-tooltip>

                    <vs-tooltip v-if="PermiteCrearExpediente()" text="Crear nuevo expediente para la admisión" class="vs-button vs-button-success vs-button-filled includeIcon includeIconOnly small">
                        <vs-button clientWidth="42" size="small" color="success" icon-pack="fas" icon="fas fa-folder-plus" @click="RegistrarIngreso()"></vs-button>
                    </vs-tooltip>

                </div>
            </template>
        </vx-input-group>
    </div>

    <transition name="fade" mode="in-out">
        <div v-if="Mostrar_Admision" class="w-52 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">Edad:</td>
                    <td>{{ Encabezado.Edad }}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Sexo:</td>
                    <td>{{ Encabezado.DescripcionSexo }}</td>
                </tr>
                <tr>
                    <td colspan="1" class="font-semibold">Nacimiento:</td>
                    <td colspan="1">{{ this.$formato_fecha(Encabezado.Nacimiento, 'ddMMyyyy') }}</td>
                </tr>
            </table>
        </div>
    </transition>
    <transition name="slide-fade">
        <div v-if="Mostrar_Admision" class="w-52 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">Peso:</td>
                    <td> <span style="color: #ed1b24" v-if="Encabezado.Peso==''">0</span> {{ Encabezado.Peso + ' kg'}} </td>

                    <td class="font-semibold">Estatura:</td>
                    <td> <span style="color: #ed1b24" v-if="Encabezado.Estatura==''">0</span> {{ Encabezado.Estatura + ' cm'}} </td>
                </tr>

                <tr>
                    <td colspan="1" class="font-semibold">IMC:</td>
                    <td> {{ this.$formato_decimal(Encabezado.IMC, 2) }} </td>
                </tr>
                <tr>
                    <td colspan="2">
                        {{ Encabezado.DescripcionIMC }}
                    </td>
                </tr>
            </table>
        </div>
    </transition>
    <transition name="bounce">
        <div v-if="Mostrar_Admision" class="w-56 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">Tipo Admisión:</td>
                    <td>{{ Encabezado.TipoAdmision}} </td>
                </tr>
                <tr>
                    <td class="font-semibold">Fecha Ingreso:</td>
                    <td>{{ this.$formato_fecha(Encabezado.Entrada) }}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Fecha Egreso:</td>
                    <td>{{ this.$formato_fecha(Encabezado.FechaEgreso) }}</td>
                </tr>
            </table>
        </div>
    </transition>
    <transition name="custom-classes-transition" enter-active-class="animated tada" leave-active-class="animated bounceOutRight">
        <div v-if="Mostrar_Admision" class="w-52 p-1 info-enc">
            <table>
                <tr>
                    <td class="font-semibold">Habitación:</td>
                    <td>{{ Encabezado.Habitacion}}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Hospital Habitación:</td>
                    <td>{{ Encabezado.HospitalHabitacion}}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Médico Tratante:</td>
                    <td>{{ Encabezado.Tratante }}</td>
                </tr>
            </table>
        </div>
    </transition>
    <transition name="fade">
        <div v-if="Mostrar_Admision" :class="'w-auto p-1 info-enc '+ ColorSeguro">
            <table>
                <tr>
                    <td class="font-semibold">Seguro:</td>
                    <td>{{ Encabezado.NombreSeguro}}</td>
                </tr>
                <tr>
                    <td class="font-semibold">Tipo Seguro:</td>
                    <td>{{ Encabezado.TipoSeguro }} </td>
                </tr>
                <tr>
                    <td class="font-semibold">Visitas previas:</td>
                    <td>{{ Encabezado.VisitasPrevias}}</td>
                </tr>
            </table>
        </div>
    </transition>
</div>
</template>

<script>
import '../../../assets/utils/velocity.min.js'
export default {
    name: 'EncabezadoExpediente',
    data() {
        return {
            No_Admision: '',
            Mostrar_Admision: false,
            Encabezado: this.LimpiarEncabezado(),
            show: false,
            popupBusquedaPaciente: false,
        }
    },
    props: {
        FiltrosBusquedaPaciente: Object
    },
    components: {
        InfoP: () => import("../../../components/sermesa/global/SMInfo.vue"),

        BusquedaPacientes: () => import("./BusquedaPacientes.vue")
    },
    methods: {
        CargarAdmision() {
            if (this.No_Admision)

                this.axios.post("/app/v1_ExpedienteEvolucion/BusquedaEncabezadoExpediente", {
                    hideloading: true,
                    serie: this.No_Admision.substring(0, 1),
                    AdmisionX: this.No_Admision.substring(1, this.No_Admision.length),
                    Admision: 5
                })
                .then(resp => {

                    if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                        this.Mostrar_Admision = true
                        this.Encabezado = resp.data.json[0]
                        //renombrando atributos
                        this.Encabezado.NumeroExpediente = this.Encabezado.NoExpediente
                        this.Encabezado.SerieAdmision = this.Encabezado.Serie
                        this.Encabezado.CodigoAdmision = this.Encabezado.CodAdmision
                        this.Encabezado.CodigoPaciente = this.Encabezado.CodPaciente
                        delete this.Encabezado.NoExpediente
                        delete this.Encabezado.Serie
                        delete this.Encabezado.CodAdmision
                        this.Encabezado.DescripcionSexo = this.Encabezado.Sexo
                        this.Encabezado.Sexo = this.Encabezado.Sexo.substring(0, 1)
                        this.$emit('onLoadedData', this.Encabezado)
                        this.MostrarAleta()
                        this.No_Admision=this.Encabezado.SerieAdmision+this.Encabezado.CodigoAdmision 
                    }
                    else {
                        this.$vs.notify({
                            time: 4000,
                            title: 'Admisión no encontrada',
                            text: 'No se encontró la admisión "'+this.No_Admision+'" o esta no esta activa',
                            iconPack: 'feather',
                            icon: 'icon-alert-circle',
                            color: 'warning',
                            position: 'top-center'
                        })
                    }
                })
        },
        MostrarAleta() {
            if (this.Encabezado.VisitasPrevias != '0')
                this.$vs.notify({
                    time: 6000,
                    title: 'Vistas previas',
                    text: 'Hay ' + this.Encabezado.VisitasPrevias + ' visita(s) previa(s) del Paciente',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'top-center'
                })
        },
        LimpiarEncabezado() {
            return {
                NumeroExpediente: "",
                Motivoconsulta: "",
                HistoriaEnfermedad: "",

                Empresa: "",
                SerieAdmision: "",
                CodigoAdmision: "",
                TipoAdmision: "",
                StatusAdmision: "",
                Entrada: "",
                FechaEgreso: "",

                Habitacion: "",
                HospitalHabitacion: "",

                NombreSeguro: "",
                TipoSeguro: "",
                Tratante: "",
                VisitasPrevias: "",

                CodigoPaciente: "",
                Nombre: "",
                Apellido: "",
                Sexo: "",
                Nacimiento: "",
                Edad: "",
                Peso: "",
                Estatura: "",
                IMC: "",
                DescripcionIMC: "",

                Celular: "",
                Ocupacion: "",

            }
        },
        ingresarAdmisionPopup(e) {
            this.popupBusquedaPaciente = false
            this.No_Admision = e

            this.CargarAdmision()
        },
        LimpiarAdmision() {
            this.Mostrar_Admision = false
            this.No_Admision = ''
            this.Encabezado = this.LimpiarEncabezado()
            this.$emit('onLoadedData', this.Encabezado)
        },
        BuscarPaciente() {
            this.popupBusquedaPaciente = true;
        },
        PermiteCrearExpediente() {
            return this.Encabezado.NumeroExpediente != "" && this.Encabezado.StatusAdmision == 'A' //no se valida Encabezado.NumeroExpediente para permitir siempre crear nuevos datos de ingreso para la misma admisión
        },
        MostrarDatosAdmision() {
            this.$refs.componentDatosAdicionales.mostrar = !this.$refs.componentDatosAdicionales.mostrar
        },
        RegistrarIngreso() {
            if (this.Encabezado.NumeroExpediente != "0")
                this.$vs.dialog({
                    type: 'confirm',
                    color: '#ed8c72',
                    title: 'Existe un expediente de Ingreso para la admisión ' + this.No_Admision,
                    acceptText: 'Crear Nuevo Expediente ',
                    cancelText: 'Trabajar con con el Expediente Existente',
                    text: `¿Desea crear nuevo expediente o trabajar con el existente?`,
                    buttonCancel: 'border',
                    clientWidth: 100,
                    accept: () => {
                        this.$emit('onCrearExpediente', this.Encabezado)
                    },
                    cancel: () => {

                    }
                })
            else
                this.$emit('onCrearExpediente', this.Encabezado)
        },
        ///Transiciones
        /*eslint-disable */
        beforeEnter: function (el) {
            el.style.opacity = 0
            el.style.transformOrigin = 'left'
        },
        enter: function (el, done) {
            Velocity(el, {
                opacity: 1,
                fontSize: '1.4em'
            }, {
                duration: 300
            })
            Velocity(el, {
                fontSize: '1em'
            }, {
                complete: done
            })
        },
        leave: function (el, done) {
            Velocity(el, {
                translateX: '15px',
                rotateZ: '50deg'
            }, {
                duration: 600
            })
            Velocity(el, {
                rotateZ: '100deg'
            }, {
                loop: 2
            })
            Velocity(el, {
                rotateZ: '45deg',
                translateY: '30px',
                translateX: '30px',
                opacity: 0
            }, {
                complete: done
            })
        }

        /*eslint-enable */
    },
    computed: {
        DatosAdicionales() {
            if (this.Encabezado) {
                let result = {
                    'Número de Expediente': this.Encabezado.NumeroExpediente,
                    'Serie y número de la Admisión': this.Encabezado.SerieAdmision + ' ' + this.Encabezado.CodigoAdmision,
                    'Tipo de Admisión': this.Encabezado.TipoAdmision,
                    'Fecha de Ingreso': this.Encabezado.Entrada,
                    'Fecha de Egreso': this.Encabezado.FechaEgreso,
                    'Estado de la Admisión': this.Encabezado.StatusAdmision,
                    'Habitación': this.Encabezado.Habitacion,
                    'Hospital de Habitación': this.Encabezado.HospitalHabitacion,
                    'Seguro': this.Encabezado.NombreSeguro,
                    'Tipo de Seguro': this.Encabezado.TipoSeguro,
                    'Médico Tratante': this.Encabezado.Tratante,
                    'Visitas previas': this.Encabezado.VisitasPrevias,
                    'Código del Paciente': this.Encabezado.CodigoPaciente,
                    'Nombre del Paciente': this.Encabezado.Nombre + ' ' + this.Encabezado.Apellido,
                    'Sexo': this.Encabezado.Sexo,
                    'Fecha de Naciemiento': this.Encabezado.Nacimiento,
                    'Edad': this.Encabezado.Edad,
                    'Peso': this.Encabezado.Peso + 'kg',
                    'Estatura': this.Encabezado.Estatura + 'cm',
                    'Índice de Masa Corporal': this.$formato_decimal(this.Encabezado.IMC, 2) + ' ' + this.Encabezado.DescripcionIMC,
                }
                let motivo = this.Encabezado.Motivoconsulta.replace(/\\n/g, '').replace(/\\\\n/g, '').replace(/\\r/g, '<br>').replace(/\\\\r/g, '<br>')
                let historia = this.Encabezado.HistoriaEnfermedad.replace(/\\n/g, '').replace(/\\\\n/g, '').replace(/\\r/g, '<br>').replace(/\\\\r/g, '<br>')

                return [{
                        label: 'Datos de la Admisión',
                        contenido: result,
                        forma: 'i'
                    },
                    {
                        label: 'Motivo de la Consulta',
                        contenido: motivo,
                        forma: 'p'
                    },
                    {
                        label: 'Historia de la Enfermedad',
                        contenido: historia,
                        forma: 'p'
                    },
                ]
            }
            return []
        },
        ColorSeguro() {
            return this.Encabezado.TipoSeguro.replace(' ', '-').toLowerCase()
        }
    },
}
</script>

<style scoped>
.info-enc {
    border: 1px solid #ccc !important;
    border-radius: 5px;
    margin: 1px;
    background-color: #f8f8f8;
    height: 83px !important;
}

.info-enc-height {
    height: 83px !important;
}

.salud-siempre {
    background-color: #ffa500 !important;
}

.privado {
    background-color: #c0dcc0 !important;
}

.seguro {
    background-color: #ffff00 !important;
}

.vacio {
    background-color: #fffdd0 !important;
}
</style><style>
/*sobrescribiendo los estilos se SM-INFO*/
.info-expediente-container .info-contenido {
    width: 400px !important;
}

.info-expediente-container .info i {
    font-size: 14px;
}

/**transitions */
.fade-enter-active,
.fade-leave-active {
    transition: opacity .9s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active below version 2.1.8 */
    {
    opacity: 0;
}

.slide-fade-enter-active {
    transition: all .3s ease;
}

.slide-fade-leave-active {
    transition: all .8s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter,
.slide-fade-leave-to

/* .slide-fade-leave-active below version 2.1.8 */
    {
    transform: translateX(10px);
    opacity: 0;
}

.bounce-enter-active {
    animation: bounce-in .5s;
}

.bounce-leave-active {
    animation: bounce-in .5s reverse;
}

@keyframes bounce-in {
    0% {
        transform: scale(0);
    }

    50% {
        transform: scale(1.5);
    }

    100% {
        transform: scale(1);
    }
}
</style>
