<template>
<div class="w-full pr-2">
    <DxDataGrid :ref="dataGridRefKey" v-bind="DefaultDxGridConfiguration" :data-source="coexDataSource" :height="'100%'" @cell-click="onCellClick">
        <DxSelection mode="single" />
        <DxToolbar>
            <DxItem name="groupPanel" />
            <DxItem name="searchPanel" />
            <DxItem location="after" template="opcionesTemplate" />
        </DxToolbar>

        <DxGroupPanel :visible="true" />
        <DxGrouping :auto-expand-all="false" />

        <DxEditing :allow-updating="false" :allow-adding="true" :allow-deleting="false" mode="popup">
            <DxPopup v-bind="popupConf" title="Detalle consulta externa" :toolbarItems="[]" />

            <DxForm labelMode="floating">
                <DxItem item-type="group" :col-span="2" :col-count="2" caption="">
                    <DxItem data-field="FechaConsulta" editor-type="dxTextArea" :editor-options="{ readOnly:true }">
                        <DxLabel :text="'Fecha consulta'" />
                    </DxItem>
                    <DxItem data-field="Motivo" editor-type="dxTextArea" :editor-options="{ readOnly:true }">
                        <DxLabel :text="'Motivo consulta'" />
                    </DxItem>
                    <!-- <DxItem data-field="CodCliente" editor-type="dxTextArea" :editor-options="{ readOnly:true }">
                        <DxLabel :text="'Código del cliente'" />
                    </DxItem> -->
                    <DxItem data-field="Especialidad" editor-type="dxTextArea" :editor-options="{ readOnly:true }">
                        <DxLabel :text="'Especialidad médica'" />
                    </DxItem>
                    <DxItem data-field="NombreMedico" editor-type="dxTextArea" :editor-options="{ readOnly:true }">
                        <DxLabel :text="'Nombre del médico'" />
                    </DxItem>
                </DxItem>
                <DxItem item-type="group" :col-span="2" :col-count="2" caption="Detalle">
                    <DxItem data-field="Historia" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:true }" />
                    <DxItem data-field="Impresion" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:true }">
                        <DxLabel :text="'Impresión clínica'" />
                    </DxItem>
                    <DxItem data-field="Diagnostico" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:true }">
                        <DxLabel :text="'Diagnóstico'" />
                    </DxItem>
                    <DxItem data-field="Anotaciones" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:true }" />
                    <DxItem data-field="Tratamiento" editor-type="dxTextArea" :editor-options="{ height: 100, readOnly:true }" />
                </DxItem>
            </DxForm>
        </DxEditing>

        <DxColumn :width="200" data-field="FechaConsulta" caption="Fecha consulta" format="dd/MM/yyyy" data-type="date" sort-order="desc" alignment="center" />
        <DxColumn :width="200" data-field="Motivo" caption="Motivo consulta" alignment="center" />
        <DxColumn :width="300" data-field="Historia" alignment="center" />
        <DxColumn :width="300" data-field="Impresion" caption="Impresión clínica" alignment="center" />
        <DxColumn :width="300" data-field="Diagnostico" caption="Diagnóstico" alignment="center" />
        <DxColumn :width="300" data-field="Anotaciones" alignment="center" />
        <DxColumn :width="300" data-field="Tratamiento" alignment="center" />
        <!-- <DxColumn :width="200" data-field="CodCliente" caption="Código cliente" alignment="center" /> -->
        <DxColumn :width="250" data-field="NombreMedico" caption="Nombre del médico" alignment="center" />
        <DxColumn :width="200" data-field="Especialidad" caption="Especialidad médica" alignment="center" />
        <!-- <DxMasterDetail :enabled="true" template="detalleCoex" /> -->

        <template #opcionesTemplate>
            <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[{text:'Balance', reportName: 'Balance'}]" @add="addRow" @refresh="Cargar(true)" :report-param="$props" :showItems="['refresh']" />
        </template>
        <template #detalleCoex="{data: info}">
            <div>
                <vs-row id="detail">
                    <vs-col vs-w="6" style="border-bottom: none;">
                        <p>
                            <b>Historia: </b>
                        </p>
                        <p>
                            {{ info.data.Historia }}
                        </p>
                    </vs-col>
                    <vs-col vs-w="6" style="border-bottom: none; border-left: none;">
                        <p>
                            <b>Impresión: </b>
                        </p>
                        <p>
                            {{ info.data.Impresion }}
                        </p>
                    </vs-col>
                    <vs-col vs-w="6">
                        <p>
                            <b>Diagnóstico: </b>
                        </p>
                        <p>
                            {{ info.data.Diagnostico }}
                        </p>
                    </vs-col>
                    <vs-col vs-w="6" style="border-left: none;">
                        <p>
                            <b>Anotaciones: </b>
                        </p>
                        <p>
                            {{ info.data.Anotaciones }}
                        </p>
                    </vs-col>
                    <vs-col vs-w="6" style="border-top: none;">
                        <p>
                            <b>Tratamiento: </b>
                        </p>
                        <p>
                            {{ info.data.Tratamiento }}
                        </p>
                    </vs-col>
                </vs-row>
            </div>
        </template>
    </DxDataGrid>
</div>
</template>

<script>
import {
    DxDataGrid,
    DxColumn,
    DxToolbar,
    DxSelection,
    DxEditing,
    DxPopup,
    DxForm,
    DxLabel,
    //DxMasterDetail,
    DxGrouping,
    DxGroupPanel,
} from 'devextreme-vue/data-grid'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import {
    DefaultDxGridConfiguration
} from './data'
import CustomStore from 'devextreme/data/custom_store'
import {
    DxItem
} from 'devextreme-vue/form'
import EXPBase from './EXPBase.vue'

const dataGridRefKey = 'gridCoex'

export default {
    name: 'ConsultaExterna',
    extends: EXPBase,
    components: {
        DxDataGrid,
        DxColumn,
        DxToolbar,
        DxSelection,
        DxItem,
        DxEditing,
        DxPopup,
        DxForm,
        DxLabel,
        ExpedienteGridToolBar,
        //DxMasterDetail,
        DxGrouping,
        DxGroupPanel,
    },
    data() {
        return {
            coex: [],
            coexDataSource: new CustomStore({
                key: "id",
                load: () => {
                    return this.coex;
                },
                insert: () => {},
            }),
            dataGridRefKey,
            DefaultDxGridConfiguration
        }
    },
    methods: {
        Cargar() {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaCoex', {
                    Serie: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    this.coex = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            CodCliente: x.idcliente,
                            FechaConsulta: x.FechaConsulta,
                            Especialidad: x.EspecialidadMedica,
                            NombreMedico: x.NombreMedico,
                            Motivo: x.Motivo,
                            Historia: x.Historia,
                            Impresion: x.ImpresionClinica,
                            Diagnostico: x.Diagnostico,
                            Tratamiento: x.Tratamiento,
                            Anotaciones: x.Anotaciones
                        }
                    })
                    this.coexDataSource.load().then(
                        () => {
                            this.refreshDataGrid()
                        },
                        () => {
                            //console.error('error loading data customstore' + JSON.stringify(error))
                        }
                    )
                })
            else {
                this.coex = []
                this.refreshDataGrid()
            }
        },
        refreshDataGrid() {
            this.dataGrid.refresh()
                .then(function () {

                })
                .catch(function () {
                    //console.error('error refreshing: ' + error)
                });
            this.dataGrid.clearSelection();
            this.dataGrid.clearSorting();
            this.dataGrid.columnOption('FechaConsulta', 'sortOrder', 'desc');
            this.dataGrid.clearFilter();
            this.dataGrid.pageIndex(0);
            this.dataGrid.collapseAll(-1)
            this.dataGrid.clearGrouping()
        },
        addRow() {
            if (this.StatusAdmision != 'A')
                this.$vs.notify({
                    time: 4000,
                    title: 'Antecedentes',
                    text: 'La admisión no está activa, no se puede agregar nuevo antecedente',
                    iconPack: 'feather',
                    icon: 'icon-alert-circle',
                    color: 'warning',
                    position: 'bottom-center'
                })
            else
                this.dataGrid.addRow();
        },
        onCellClick(e) {
            if (e.rowType === "data") {
                if (this.dataGrid.isRowExpanded(e.key)) {
                    this.dataGrid.collapseRow(e.key);
                } else {
                    e.component.collapseAll(-1)
                    this.dataGrid.expandRow(e.key);
                }
            }
        },
    },
    mounted() {
        this.Cargar();
    },
    watch: {
        'NumeroExpediente'(newval) {
            if (newval != undefined)
                this.Cargar()
        },
    },
    props: {
    },
    computed: {
        dataGrid: function () {
            return this.$refs[dataGridRefKey].instance;
        },
    },
}
</script>

<style>
#detail .vs-col {
    padding: 5px;
    border-color: black;
    border-style: solid;
    border-width: 1px;
}

[dir=ltr] .dx-datagrid-group-closed::before,
[dir=ltr] .dx-datagrid-group-opened::before {
    font-size: 40px !important;
    color: #337ab7;
}
</style>
