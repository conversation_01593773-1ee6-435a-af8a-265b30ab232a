<template>
    <vx-card type="2" :title="`Anular Cargos Farmacia`">
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form ref="formAnulacion" method="post" @submit.prevent="handleSubmit(anular_orden())">

                <div class="flex flex-wrap">
                    <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                        <label>Admision:</label>
                        &nbsp;&nbsp;
                        <label style="color:black">{{info.admision}}</label>
                    </div>
                    <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                        <label>Paciente:</label>
                        &nbsp;&nbsp;
                        <label style="color:black">{{info.paciente}}</label>
                    </div>
                    <div class="md:w-full lg:w-1/12 xl:w-5/12"></div>
    
                    <div class="md:w-full lg:w-4/12 xl:w-2/12 m-2">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <SM-Buscar class="w-full" label="*Tipo de Orden" v-model="info.tipoOrden" api="app/v1_JefeCaja/ObtenerTiposOrdenesPorRol" :api_campos="['Codigo','Nombre']" :api_titulos="['Tipo de Orden','Descripción']" :api_filtro="{'Activa':0,'opcion':'C','subOpcion':'3','filtroTipoOrdenHospital':info.filtroTipoOrden.toString(),'filtroTipoOrdenGeneral':info.filtroTipoOrdenGeneral.toString()}" api_campo_respuesta="Codigo" api_campo_respuesta_mostrar="Codigo" :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true" :callback_buscar="cargar().datos_tipo_orden" :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
                    <div class="md:w-full lg:w-6/12 xl:w-4/12 m-2 lg:pr-6">
                        <vs-input label="Descripción:" class="w-full" :value="info.descripcionOrden" disabled />
                    </div>
    
                    <div class="md:w-full lg:w-10/12 xl:w-6/12 m-2">
                        <ValidationProvider rules="required" v-slot="{ errors }">
                            <vs-input label="*No. Orden" type="number" class="w-full" v-model="info.codigoOrden" :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" v-on:change="CargarOrden" />
                        </ValidationProvider>
                    </div>
                    <div class="md:w-full lg:w-10/12 xl:w-6/12 m-2">
                        <ValidationProvider rules="max:40|required" v-slot="{ errors }">
                            <vs-input size="large" label="*Motivo" class="w-full" v-model="info.razon" :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                        </ValidationProvider>
                    </div>
    
                    <div class="w-full m-2">
                        <vs-button color="success" class="md:w-full lg:w-4/12 xl:w-1/12 m-2" :disabled="info.desactivarBotonAnulacion" @click="handleSubmit(anular_orden(invalid))">
                            Anular Cargo
                        </vs-button>
    
                        <vs-button class="md:w-full lg:w-4/12 xl:w-1/12 m-2" color="warning" @click="confirmar_limpia_campos()">
                            Limpiar Campos
                        </vs-button>
                    </div>
                </div>
            </form>
        </ValidationObserver>
    </vx-card>

    </template>
    
    <script>
    export default {
        components: {},
        data() {
            return {
    
                info: {
                    tipoOrden: null,
                    descripcionOrden: null,
                    codigoOrden: null,
                    admision: null,
                    paciente: null,
                    razon: null,
                    desactivarBotonAnulacion: true,
                    filtroTipoOrden: [],
                    filtroTipoOrdenGeneral: []
                },
                permisos: {
                    fa_anula: false,
                    fc_anula: false,
                },
    
            }
    
        },
        watch: {
            'permisos.fa_anula'() {
                if (this.permisos.fa_anula) {
                    this.info.filtroTipoOrden.push('FA')
                }
            },
            'permisos.fc_anula'() {
                if (this.permisos.fa_anula) {
                    this.info.filtroTipoOrden.push('FC')
                }
            }
        },
        computed: {
    
        },
        mounted() {
            this.info.filtroTipoOrden = []
            this.info.filtroTipoOrdenGeneral = []
            this.permisos.fa_anula = this.$validar_privilegio('FA_ANULA').status
            this.permisos.fc_anula = this.$validar_privilegio('FC_ANULA').status
        },
        methods: {
            cargar() {
                return {
                    datos_tipo_orden: (datos) => {
                        this.info.tipoOrden = datos.Codigo
                        this.info.descripcionOrden = datos.Nombre
                        this.cargar_paciente()
                    },
                    datos_orden: () => {
    
                    }
                }
    
            },
            CargarOrden() {
                this.info.desactivarBotonAnulacion = true;
                if (this.info.tipoOrden != '' && this.info.codigoOrden != '') {
    
                    this.axios.post('/app/v1_farmacia/ConsultarCargos', {
                            TipoOrden: this.info.tipoOrden,
                            Id: this.info.codigoOrden
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {

                                if (resp.data.json[0].codigo == 1){
                                    this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Anulación Orden',
                                    text: resp.data.json[0].descripcion
                                })
                                }
                                else{
                                    this.info.paciente = resp.data.json[0].Paciente;
                                this.info.admision = resp.data.json[0].Admision;
                                this.info.desactivarBotonAnulacion = false;
                                }
                                
    
                            } else {
                                this.Paciente = '';
                                this.Admision = '';
    
                                this.$vs.notify({
                                    color: '#B71C1C',
                                    title: 'Anulación Orden',
                                    text: 'No existe Orden seleccionada.',
                                })
                            }
                        })
                        .catch()
                }
            },
    
            anular_orden(invalid) {
                if (invalid) {
                    this.$vs.notify({
                        color: '#B71C1C',
                        title: 'Alerta',
                        text: 'Falta ingresar campos obligatorios',
                    })
                    return
                }
                if (this.info.tipoOrden != '' && this.info.codigoOrden != '' && this.info.admision != '') {
                    this.axios.post('/app/v1_farmacia/AnularCargos', {
                            TipoOrden: this.info.tipoOrden,
                            Id: this.info.codigoOrden,
                            Observaciones: this.info.razon
                        })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.limpiar_campos();
                            }
                        })
                        .catch()
                }
            },
            limpiar_campos() {
                this.info.tipoOrden = null
                this.info.descripcionOrden = null
                this.info.codigoOrden = null
                this.info.admision = null
                this.info.paciente = null
                this.info.razon = null
                this.desactivarBotonAnulacion = true
            },
            confirmar_limpia_campos() {
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    acceptText: 'Limpiar',
                    cancelText: 'Cancelar',
                    text: `¿Desea limpiar los campos?`,
                    accept: () => {
                        this.limpiar_campos()
                    }
                })
            }
    
        }
    }
    </script>
    