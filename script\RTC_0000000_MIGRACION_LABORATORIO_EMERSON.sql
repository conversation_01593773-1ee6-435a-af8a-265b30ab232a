USE [HOSPITAL]
GO

/****** Object:  StoredProcedure [dbo].[SpLisConsultaResLaboratorio]    Script Date: 22/02/2023 13:13:39 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
G

-- ==========================================================================================
-- Creado por:			Emerson Ríos
-- Fecha de creación:	xx/12/2022
-- Producto:			LIS - Resultados Laboratorio
-- Description:			Procedimiento almacenado que obtiene los resultados de laboratotio de una orden,
--						incluye filtros opcionales por estado, tipo de conslta y ocultamiento de información sensible
-- ==========================================================================================


ALTER PROCEDURE [dbo].[SpLisConsultaResLaboratorio]
	---Para identificar la fila a actualizar
	-- Add the parameters for the stored procedure here
	@i_empresa varchar(3) = '',--es la empresa de la cual se va a obtener el reporte
	@i_tipo_orden varchar(3) = '', --indica el tipo del laboratorio ó resultado a lfiltrar
	@i_orden int = null,--es el estado de los registros que se quiere obtener	
	@i_LineaResultado int = null,
	@i_Estado_Examen varchar(10) =null,
	@i_TipoConsulta varchar(1)=null,
	@i_MostrarResultados varchar(1) = 'T'
AS
BEGIN
	SET NOCOUNT ON;
	    
		declare @EstadoExamen varchar(20)
		declare @MinCodExamen int
		declare @MaxCodExamen int
		declare @ListaValoresRes table (listRes varchar(50))
		declare @MostrarResultados table (MostrarResultados int)

		if(@i_MostrarResultados = 'O')
		begin
			insert into @MostrarResultados
			 values (0)			
		end

		if(@i_MostrarResultados = 'T')
		begin
			insert into @MostrarResultados
			 values (0)
			,       (1)
		end

		set @i_LineaResultado = coalesce(@i_LineaResultado,0)
		set @i_TipoConsulta   = coalesce(@i_TipoConsulta,'C')


		if ( @i_TipoConsulta ='')
		begin 
			select @i_TipoConsulta = null
		end


		if @i_Estado_Examen = 'T'
		BEGIN 
			insert into @ListaValoresRes 
			values ('T')
		END

		if @i_Estado_Examen = 'I'
		BEGIN 

			insert into @ListaValoresRes 
			values  ( 'I')
		    ,        ('M')
			,        ('N')	
		
		END

		
		if coalesce (@i_Estado_Examen, 'A') = 'A' or  coalesce (@i_Estado_Examen, ' ') = ' '
		BEGIN 

			insert into @ListaValoresRes 
			values  ('I')
			,       ('T')	
			,       ('P')	
		END
		
		select @MinCodExamen = coalesce (@i_LineaResultado, 0)
		select @MaxCodExamen = case when @i_LineaResultado = 0 then  9999  
							        when @i_LineaResultado > 0 then @i_LineaResultado 
									else 0 end
		select   Empresa	
				, Admision 
				, CodigoPaciente
		        , CodExamen
				, Medico		
				, MedCorreoElectronico
				, Paciente	
				, Sexo	
				, Nacimiento	
				, Edad	
				, EdadMedida
				, Telefonos	
				, CorreoElectronico
				, Hospital	
				, Registro	
				, TipoOrden	
				, Orden	
				, NombreSeccion	
				, CodigoSeccion
				, CodigoExamen	
				, Examen	
				, InformeAnormal	
				, LineaPrueba	
				, Prueba	
				, Resultado	
				, Medida	
				, ValorRefXEquipo	
				, usuariofirma	
				, FirmaBitmap
				, Referencia	
				, Texto	
				, FechaValidacion
				, bloqueocorreo
				, Status
				, EstadoOrden
		from (							
		
			select 
				  lr.CodExamen
				, lr.Empresa 
				, RTRIM( lr.CodigoExamen) CodigoExamen
				, concat(rtrim(a.nombre),' ',rtrim(a.Apellido)) Medico
				, a.CorreoElectronico MedCorreoElectronico
				, CONCAT(o.SerieAdmision, o.Admision)Admision
				, p.Codigo CodigoPaciente
				, concat(rtrim(p.Nombre),' ',rtrim(p.Apellido),' ',rtrim(p.ApellidoCasada)) Paciente
				, case p.Sexo when 'M' then 'MASCULINO' ELSE 'FEMENINO' END Sexo
				, p.Nacimiento 
				, ad.Edad
				, case upper(ad.EdadMedida) when 'A' then 'Años' when 'M' then 'Meses' when 'D' then 'Días' else '' end as EdadMedida
				, p.Telefonos
				, p.Email CorreoElectronico
				, eh.Nombre  Hospital
				, lr.Registro 
				, lr.TipoOrden
				, lr.Orden
				, d.Nombre NombreSeccion
				, d.Codigo CodigoSeccion
				, lr.Examen
				, ISNULL(lr.InformeAnormal,0) InformeAnormal
				, lr.LineaPrueba
				, lr.Prueba
				, lr.Resultado
				, lr.Medida
				, lr.ValorRefXEquipo
				, TRY_CONVERT (int,lr.usuariofirma)usuariofirma
				, case when isnumeric(TRY_CONVERT (int,lr.usuariofirma)) = 1 then corp.firmabitmap else  null end as FirmaBitmap					
				, lr.Referencia
				, lt.Texto
				, lr.FechaValidacion  
				, lr.bloqueocorreo
				, lr.LineaOrden 				
				, lr.Status
				, o.Status EstadoOrden
				, o.SerieAdmision
			from Ordenes o with (nolock) 
			left join Cargos  c with (nolock) 
				on  o.Empresa		= c.Empresa	
				and o.Tipo			= c.TipoOrden	
				and o.Codigo		= c.Orden		
			left join Admisiones ad  with (nolock) on  
				    o.Empresa		 = ad.Empresa 
				and o.SerieAdmision  = ad.Serie 
				and o.Admision		 = ad.Codigo 
			left join Pacientes p   with (nolock) 
			on  o.Empresa			 = p.Empresa 
				and  ad.Paciente	 = p.Codigo
			left join
			(
					select
					 DENSE_RANK () over (order by  case @i_TipoConsulta 
													    when 'C'  then le.CodigoExamen 
													    when 'S' then le.Departamento end ) CodExamen 
					, le.Empresa 
					, RTRIM( le.CodigoExamen) CodigoExamen
					, le.Registro 
					, le.TipoOrden
					, le.Orden
					, le.Examen
					, le.LineaOrden
					, le.Status					
					, ISNULL(le.InformeAnormal,0) InformeAnormal		
					, coalesce (TRY_CONVERT(int, le.usuariofirma),0)  usuariofirma 					
					, le.ValidadoFecha FechaValidacion
					, lr.LineaPrueba
					, lr.Prueba
					, lr.Resultado
					, lr.Medida
					, lr.ValorRefXEquipo
					, pr.bloqueocorreo
					, pr.Departamento
					, dbo.F_EliminarEnter(lr.Referencia)  Referencia
					from   LaboratorioResultados le  with (nolock) 				
					left join LaboratorioResultadosLineas lr  with (nolock)
					on le.Empresa			= lr. Empresa 
						and le.TipoOrden	= lr.TipoOrden 
						and le.Orden		= lr.Orden 
						and le.LineaOrden	= lr.LineaOrden			
					left join inventario.dbo.productos  pr  with (nolock) 
					on  le.Empresa			= pr.Empresa 
						and le.CodigoExamen = pr.codigo
					where le.Empresa		= @i_empresa
						and le.TipoOrden	= @i_tipo_orden
						and le.Orden		= @i_orden
						and le.Status in (select  listRes from  @ListaValoresRes )
						and pr.bloqueocorreo in (select MostrarResultados from @MostrarResultados)
			) lr  
			on  o.Empresa			= lr.Empresa
				and o.Tipo   		= lr.TipoOrden
				and o.Codigo		= lr.Orden 
			left join inventario.dbo.Departamentos d  with (nolock) 
			on  o.Empresa		     = d.Empresa  
				and lr.Departamento  = d.Codigo 
			left join LaboratorioPruebas lp  with (nolock) 
			on  o.Empresa			 = lr.Empresa 
				and RTRIM(lp.Examen) = RTRIM(lr.CodigoExamen)
				and lp.Linea		 = lr.LineaPrueba 
			left join LaboratorioTextos lt  with (nolock)  
			on	o.Empresa			= lr.Empresa 
				and lt.Examen		= lr.CodigoExamen 			
			left join Ajenos a  with (nolock)  
			on	o.Empresa			= a.Empresa 
				and o.Medico		= a.Codigo
			left join OrdenesTipos ot  with (nolock)  
			on	o.Empresa			= ot.Empresa 
				and ot.Codigo		= lr.TipoOrden 
			left join NOMINADB.dbo.EmpHospital eh  with (nolock) 
			on o.Empresa			= eh.Empresa 
				and eh.Codigo		= ot.Hospital 													
			left join CONTADB.DBO.CORPORATIVOS corp with (nolock) 
			on	corp.corporativo= lr.usuariofirma
			where
				o.Empresa		 = @i_empresa
				and o.Tipo      = @i_tipo_orden
				and o.Codigo     = @i_orden				
		)t
		where COALESCE(CodExamen,0) >=@MinCodExamen and COALESCE(CodExamen,998) <=@MaxCodExamen
		order by  CodExamen 		

END
GO      


