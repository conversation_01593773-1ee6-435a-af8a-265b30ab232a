<template>
<div id="salud-siempre-container">
    <vs-alert v-if="!permisos.status" color="warning">
        <span> Usuario no posee permisos de accesos a este módulo </span>
        <font-awesome-icon :icon="['fas', 'ban']" />
        <div>

        </div>
    </vs-alert>
    <div v-if="numeroFormulario !== ''" id="detail" style="display: grid; place-items: center; font-size: 20px">
        <p>
            <b>Formulario</b><br>
        </p>
        <p>
            {{ formulario.Formulario }}
        </p>
    </div>
    <vs-row vs-justify="flex-end" vs-align="center" class="pb-2">
        <ExpedienteGridToolBar v-bind="$props" :visible="true" :pdfExportItems="[{text:'Formulario Hospitalización', reportName: 'Formulario Hospitalización'}]" :report-param="$props" :showItems="buttons">
            <DxButton :visible="editarSolicitud !== 1 && !formularioFinalizado" id="botonBarra" type="default" styling-mode="outlined" hint="Finalizar documento" @click="finalizarDocumento">
                <font-awesome-icon :icon="['fas', 'file-circle-check']" style="font-size: 18px; color: #5db95c" />
            </DxButton>
            <DxButton :visible="editarSolicitud !== 1 && formularioFinalizado" id="botonBarra" type="default" styling-mode="outlined" hint="Habilitar edición" @click="finalizarDocumento">
                <font-awesome-icon :icon="['fas', 'file-pen']" style="font-size: 18px; color: #ffc107" />
            </DxButton>
        </ExpedienteGridToolBar>
    </vs-row>
    <vs-collapse v-if="permisos.status" accordion type="margin">
        <vs-collapse-item v-for="(item, index) in tabsAntecedentes" v-bind:key="index" icon-arrow="label">
            <div slot="header" style="display: flex; flex-wrap: wrap; justify-content: start; align-items: center;">
                <font-awesome-icon :icon="['fas', item.icon]" class="i-size pr-2" /> {{item.name}}
            </div>
            <div>

                <!-- ENCABEZADO -->
                <div v-if="item.value === 1" style="cursor: auto;">
                    <form @submit="handleSubmit">
                        <DxForm :form-data.sync="formulario" labelMode="floating" :ref="dataEncabezadoRefKey" :read-only="blockItems">
                            <DxItem item-type="group" :col-span="2" :col-count="2">
                                <DxSimpleItem v-for="(item, index) in info" v-bind:key="index" data-field="Copago">
                                    <template #default>
                                        <div id="detail">
                                            <b>{{ item.name }}</b>
                                            {{ formulario[item.value] }}
                                        </div>
                                    </template>
                                </DxSimpleItem>
                                <DxItem data-field="Cobertura" editor-type="dxNumberBox" :is-required="editarSolicitud == 2 ? false: true" :editor-options="{ min: 0, max: 99999999.99, format: '#0.00', step: 0 }" />
                            </DxItem>
                            <DxGroupItem item-type="group" :col-span="2" :col-count="2" caption=" ">
                                <DxItem data-field="Descripcion" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                                    <DxLabel :text="'Descripción copago/coaseguro'" />
                                </DxItem>
                                <DxItem data-field="Extraordinarios" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }" />
                                <DxItem data-field="Diagnosticos" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }">
                                    <DxLabel :text="'Diagnósticos'" />
                                </DxItem>
                                <DxItem data-field="Antecedentes" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 1000 }" />
                                <DxItem data-field="Clausulas" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 5000, disabled: blockItems }" />
                                <DxItem item-type="group" :col-span="1">
                                    <DxItem data-field="Actualizado" editor-type="dxTextBox" :editor-options="{ maxLength: 100 }">
                                        <DxLabel :text="'Actualizado por'" />
                                    </DxItem>
                                    <DxItem data-field="Egreso" editor-type="dxTextBox" :editor-options="{ maxLength: 100 }">
                                        <DxLabel :text="'Egresado por'" />
                                    </DxItem>
                                </DxItem>
                            </DxGroupItem>
                            <DxButtonItem :col-span="3" :visible="!formularioFinalizado" :button-options="submitButtonOptions" horizontal-alignment="center" verical-alignment="center" />
                        </DxForm>
                    </form>
                </div>

                <!-- DETALLE -->
                <div v-if="item.value === 2">
                    <SaludDetalle v-bind="$props" :Formulario="getFormulario" :EstadoFormulario="formularioFinalizado" />
                </div>
            </div>
        </vs-collapse-item>
    </vs-collapse>
</div>
</template>

<script>
import 'devextreme-vue/text-area'
import 'devextreme-vue/popup';
import {
    DefaultDxGridConfiguration
} from './data'
import {
    DxForm,
    DxItem,
    DxLabel,
    DxGroupItem,
    DxSimpleItem,
    DxButtonItem,
} from 'devextreme-vue/form'
import DxButton from 'devextreme-vue/button'
import ExpedienteGridToolBar from './ExpedienteGridToolBar.vue'
import SaludDetalle from './EXP021_SaludSiempreDetalle.vue'
const dataEncabezadoRefKey = 'encabezado-form'

export default {
    name: 'SaludSiempre',
    components: {
        DxForm,
        DxItem,
        DxLabel,
        DxGroupItem,
        DxSimpleItem,
        DxButton,
        DxButtonItem,
        ExpedienteGridToolBar,
        SaludDetalle,
    },
    data() {
        return {
            tabsAntecedentes: [{
                    name: 'Encabezado',
                    icon: 'user-shield',
                    value: 1,
                    data: 'AntMedicamentos'
                },
                {
                    name: 'Detalle',
                    icon: 'table',
                    value: 2,
                    data: 'AntMedicos'
                },
            ],
            info: [{
                    name: 'Fecha formulario:',
                    value: 'FechaFormulario'
                },
                {
                    name: 'Fecha actualización:',
                    value: 'FechaActualizacion'
                },
                {
                    name: 'Meses plan:',
                    value: 'Antiguedad'
                },
                {
                    name: 'Consumo anual:',
                    value: 'ConsumoAnual'
                },
                {
                    name: 'Copago:',
                    value: 'Copago'
                },
                {
                    name: 'Coaseguro:',
                    value: 'Coaseguro'
                },
                {
                    name: 'Cobertura disponible:',
                    value: 'CoberturaDisponible'
                },
            ],
            DefaultDxGridConfiguration,
            dataEncabezadoRefKey,
            numeroFormulario: '',
            cobertura: '',
            formulario: [],
            submitButtonOptions: {},
            editarSolicitud: 1,
            blockItems: true,
            formularioFinalizado: true,
            seleccionFormulario: false,
            formularioFinalizaMounted: false, //Variable para determinar si cuando se cargó el componenete el formulario ya estaba finalizado
            permisos: {
                status: true,
                data: []
            } //ver prototipo $validar_funcionalidad solo por mantener la estructura que devuele ;)
        }
    },
    props: {
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,

    },
    methods: {
        Cargar(mounted) {
            if (this.SerieAdmision && this.CodigoAdmision)
                this.axios.post('/app/v1_ExpedienteEvolucion/BusquedaEncFormHosp', {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision
                })
                .then(resp => {
                    this.formulario = resp.data.json.map((x, index) => {
                        return {
                            id: index,
                            CodigoPaciente: x.CodigoPaciente,
                            Seguro: x.Seguro,
                            Hospital: x.Hospital,
                            ConsumoAnual: x.ConsumoAnual,
                            Coaseguro: x.Coaseguro,
                            Copago: x.Copago,
                            Antiguedad: x.Antiguedad,
                            CoberturaDisponible: x.CoberturaDisponible,
                            IdPlan: x.IdPlan,
                            IdContrato: x.IdContrato,
                            SerieAdmision: x.SerieAdmision,
                            Admision: x.Admision,
                            Formulario: x.NoFormulario,
                            Extraordinarios: x.DescExtraordinarios,
                            Diagnosticos: x.Diagnosticos,
                            FormularioFinalizado: x.FormularioFinalizado,
                            Antecedentes: x.Antecedentes,
                            Descripcion: x.DescCopagoCoaseguro,
                            FechaFormulario: this.formatFecha(x.FechaRegistro),
                            FechaActualizacion: this.formatFecha(x.FechaActualizacion),
                            Usuario: x.Usuario,
                            UsuarioActualiza: x.UsuarioActualiza,
                            Actualizado: x.ActualizadoPor,
                            Egreso: x.EgresadoPor,
                            Clausulas: x.Clausulas,
                            Cobertura: x.MontoCobertura
                        }
                    })
                    this.formulario = {
                        ...this.formulario[0]
                    }
                    this.numeroFormulario = this.formulario.Formulario
                    this.formularioFinalizado = this.formulario.FormularioFinalizado

                    if (this.formulario.FormularioFinalizado && mounted) {
                        this.formularioFinalizaMounted = true
                    }

                    if (this.formularioFinalizaMounted) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Formulario finalizado',
                            acceptText: 'Aceptar',
                            text: `El formulario ya se ha finalizado`,
                            buttonCancel: 'border',
                            clientWidth: 100,
                            accept: () => {},
                        })
                    }

                    if (this.numeroFormulario !== '' && this.numeroFormulario !== null && !this.seleccionFormulario && !this.formularioFinalizaMounted) {
                        this.$vs.dialog({
                            type: 'confirm',
                            color: '#ed8c72',
                            title: 'Formulario de hospitalización encontrado',
                            acceptText: 'Trabajar con el formulario encontrado',
                            cancelText: 'Crear un nuevo formulario',
                            text: `¿Desea trabajar con el formulario existente o desea crear uno nuevo?`,
                            buttonCancel: 'border',
                            clientWidth: 100,
                            accept: () => {
                                this.seleccionFormulario = true
                            },
                            cancel: () => {
                                this.seleccionFormulario = true
                                this.formulario = {
                                    CodigoPaciente: '',
                                    Seguro: '',
                                    Hospital: '',
                                    ConsumoAnual: '',
                                    Coaseguro: '',
                                    Copago: '',
                                    Antiguedad: '',
                                    CoberturaDisponible: '',
                                    IdPlan: '',
                                    IdContrato: '',
                                    SerieAdmision: '',
                                    Admision: '',
                                    Formulario: '',
                                    Extraordinarios: '',
                                    Diagnosticos: '',
                                    FormularioFinalizado: false,
                                    Antecedentes: '',
                                    Descripcion: '',
                                    FechaFormulario: '',
                                    FechaActualizacion: '',
                                    Usuario: '',
                                    UsuarioActualiza: '',
                                    Actualizado: '',
                                    Egreso: '',
                                    Clausulas: '',
                                    Cobertura: ''
                                }
                                this.numeroFormulario = ''
                                this.formularioFinalizado = false
                            }
                        })
                    }
                })
            else {
                this.formulario = []
            }
        },
        formatFecha(date) {
            const year = new Date(date).toLocaleString('default', {
                year: 'numeric'
            });
            const month = new Date(date).toLocaleString('default', {
                month: '2-digit'
            });
            const day = new Date(date).toLocaleString('default', {
                day: '2-digit'
            });

            return [day, month, year].join('/');
        },
        submit() {
            this.dataGrid.saveEditData()
        },
        addRow() {
            this.dataGrid.addRow();
        },
        handleSubmit(e) {
            e.preventDefault()
            if (this.SerieAdmision && this.CodigoAdmision) {
                this.grabarEncabezado()
            }
        },
        grabarEncabezado() {
            if (this.editarSolicitud === 1) {
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    DescCopagoCoaseguro: this.formulario.Descripcion,
                    DescExtraordinarios: this.formulario.Extraordinarios,
                    Diagnosticos: this.formulario.Diagnosticos,
                    Antecedentes: this.formulario.Antecedentes,
                    Clausulas: this.formulario.Clausulas,
                    MontoCobertura: this.formulario.Cobertura,
                    ActualizadoPor: this.formulario.Actualizado,
                    EgresadoPor: this.formulario.Egreso
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarEncFormHosp", {
                    ...postData
                }).then(() => {
                    this.Cargar(false)

                    this.editarSolicitud++
                    this.blockItems = true
                })
            } else if (this.editarSolicitud === 2) {
                this.editarSolicitud++
                this.blockItems = false
            } else if (this.editarSolicitud === 3) {
                let postData = {
                    SerieAdmision: this.SerieAdmision,
                    Admision: this.CodigoAdmision,
                    NoFormulario: this.numeroFormulario,
                    DescCopagoCoaseguro: this.formulario.Descripcion,
                    DescExtraordinarios: this.formulario.Extraordinarios,
                    Diagnosticos: this.formulario.Diagnosticos,
                    Antecedentes: this.formulario.Antecedentes,
                    Clausulas: this.formulario.Clausulas,
                    MontoCobertura: this.formulario.Cobertura,
                    ActualizadoPor: this.formulario.Actualizado,
                    EgresadoPor: this.formulario.Egreso
                }
                this.axios.post("/app/v1_ExpedienteEvolucion/InsertarEncFormHosp", {
                    ...postData
                }).then(() => {
                    this.Cargar(false)

                    this.editarSolicitud--
                    this.blockItems = true
                })
            }
        },
        finalizarDocumento() {
            this.$validar_funcionalidad('/EXPEDIENTE/EXP021', this.formularioFinalizado ? 'ACTIVAR' : 'FINALIZAR', (d) => {
                if (d.status) {
                    let postData = {
                        SerieAdmision: this.SerieAdmision,
                        Admision: this.CodigoAdmision,
                        NoFormulario: this.numeroFormulario,
                        Finalizado: !this.formularioFinalizado
                    }
                    this.axios.post("/app/v1_ExpedienteEvolucion/FinalizaActivaEncFormHosp", {
                        ...postData
                    }).then(() => {
                        this.Cargar(false)
                        this.formularioFinalizado = !this.formularioFinalizado
                    })
                } else {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Permiso denegado',
                        acceptText: 'Aceptar',
                        text: 'Actualmente no cuenta con permiso para ' + (this.formularioFinalizado ? 'ACTIVAR' : 'FINALIZAR') + ' el formulario',
                        buttonCancel: 'border',
                        clientWidth: 100,
                        accept: () => {},
                    })
                }
            })

        }
    },
    mounted() {
        let permiso = this.$validar_funcionalidad("/EXPEDIENTE/EXP021", null, null)
        if (permiso) {
            this.permisos = permiso
            this.Cargar(true)
        } else
            this.permisos.status = false
    },
    watch: {
        "numeroFormulario"(newval) {
            if (newval !== '') {
                this.editarSolicitud = 2;
            } else {
                this.editarSolicitud = 1;
                this.blockItems = false;
            }
        },
        "editarSolicitud"(newval) {
            if (newval !== '') {
                if (newval === 1) {
                    this.submitButtonOptions = {
                        text: 'Guardar solicitud',
                        type: 'default',
                        icon: 'save',
                        useSubmitBehavior: true,
                        disabled: false
                    }
                } else if (newval === 2) {
                    this.submitButtonOptions = {
                        text: 'Editar solicitud',
                        type: 'warning',
                        icon: 'edit',
                        useSubmitBehavior: true,
                        disabled: false
                    }
                } else if (newval === 3) {
                    this.submitButtonOptions = {
                        text: 'Actualizar solicitud',
                        type: 'success',
                        icon: 'upload',
                        useSubmitBehavior: true,
                        disabled: false
                    }
                }
            }
        },
    },
    computed: {
        getFormulario: {
            get() {
                return this.numeroFormulario;
            }
        },
        buttons: function () {
            if (this.numeroFormulario !== '') {
                return ['exportPdf']
            } else {
                return [];
            }
        },
    },
}
</script>

<style>
#detail {
    padding: 5px;
    border: none;
    color: black;
}

#salud-siempre-container .vs-alert {
    font-size: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

#salud-siempre-container .vs-alert .fa-ban {
    font-size: 6rem;
    margin: 2rem;
}
</style>
