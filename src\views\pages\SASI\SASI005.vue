<template>
  <div>
    <!--=========================================================================================================================
        POPUP SINIESTRALIDAD 
        ==========================================================================================================================-->
    <vs-popup title="Siniestralidad" :active.sync="popupActivoSinestrilidad">
      <vs-divider position="left"> Informacion de Plan </vs-divider>
      <vs-table2
        style="height: 150px !important"
        :data="planSiniestro"
        max-items="1"
        pagination
        class="mt-0 mb-0"
      >
        <template slot="thead">
          <th>Poliza</th>
          <th>Contrato</th>
          <th>Plan</th>
          <th>No.Adhesion</th>
          <th>Id Afiliado</th>
          <th>Id Cliente</th>
          <th>Id Paciente</th>
          <th>Status</th>
          <th>Fecha Cobertura</th>
          <th>Meses Activo</th>
        </template>
        <template slot-scope="{ data }">
          <tr :key="indextr" v-for="(tr, indextr) in data">
            <vs-td2
              ><label>{{ tr.idPoliza }}</label></vs-td2
            >
            <vs-td2
              ><label>{{ tr.contrato_nom }}</label></vs-td2
            >
            <vs-td2
              ><label>{{ tr.plan_nom }}</label></vs-td2
            >
            <vs-td2
              ><label>{{ tr.NumeroAdhesion }}</label></vs-td2
            >
            <vs-td2
              ><label>{{ tr.afiliado }}</label></vs-td2
            >
            <vs-td2
              ><label>{{ tr.IdCliente }}</label></vs-td2
            >
            <vs-td2
              ><label>{{ tr.idPaciente }}</label></vs-td2
            >
            <vs-td2
              ><label>{{ tr.status }}</label></vs-td2
            >
            <vs-td2
              ><label>{{ tr.fecha_inicio }}</label></vs-td2
            >
            <vs-td2
              ><label>{{ tr.meses_vigencia }}</label></vs-td2
            >
          </tr>
        </template>
      </vs-table2>
      <br />
      <div style="display: flex">
        <vs-input
          class="inputx"
          disabled
          label="Monto Vitalicio"
          v-model="info.MaximoAnual"
        />
        &nbsp;
        <vs-input
          class="inputx"
          disabled
          label="Monto Anual"
          v-model="info.MaximoEvento"
        />
        &nbsp;
        <vs-input
          class="inputx"
          disabled
          label="Monto Evento"
          v-model="info.MaximoVitalicio"
        />
        &nbsp;
      </div>
      <br />
      <vs-tabs>
        <!--Vitalicio-->
        <vs-tab label="Vitalicio">
          <div>
            <vs-table2
              max-items="1"
              pagination
              :data="vitalicio"
              class="mt-0 mb-0"
            >
              <template slot="thead">
                <th>Maximo Vitalicio</th>
                <th>Total Acumulado</th>
                <th>Siniestro Acumulado</th>
                <th>Participacion</th>
                <th>Total Disponible</th>
              </template>
              <template slot-scope="{ data }">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                  <vs-td2
                    ><label>{{ tr.MaximoVitalicio }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.TotalAcumulado }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.SiniestroAcumulado }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.Participacion }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.TotalDisponible }}</label></vs-td2
                  >
                </tr>
              </template>
            </vs-table2>
          </div>
        </vs-tab>
        <!--Anual-->
        <vs-tab label="Anual">
          <div>
            <vs-table2 max-items="5" :data="Anual" pagination class="mt-0 mb-0">
              <template slot="thead">
                <th>Año</th>
                <th>Maximo Anual</th>
                <th>Participacion Anual</th>
                <th>Total Acumulado</th>
                <th>Total Disponible</th>
              </template>
              <template slot-scope="{ data }">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                  <vs-td2
                    ><label>{{ tr.Anio }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.MaximoAnual }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.participacion }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.TotalAcumulado }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.TotalDisponible }}</label></vs-td2
                  >
                </tr>
              </template>
            </vs-table2>
          </div>
        </vs-tab>
        <!--Evento-->
        <vs-tab label="Evento">
          <div>
            <vs-table2
              max-items="5"
              :data="Evento"
              pagination
              class="mt-0 mb-0"
            >
              <template slot="thead">
                <th>Serie</th>
                <th>Admision</th>
                <th>Maximo Evento</th>
                <th>Total Evento</th>
                <th>Total Disponible</th>
              </template>
              <template slot-scope="{ data }">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                  <vs-td2
                    ><label>{{ tr.SerieAdmision }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.Admision }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.MaximoXEvento }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.TotalEvento }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.TotalDisponible }}</label></vs-td2
                  >
                </tr>
              </template>
            </vs-table2>
          </div>
        </vs-tab>
        <!--Sin Cierre-->
        <vs-tab label="Sin Cierre">
          <div>
            <vs-table2
              max-items="5"
              :data="Cierre"
              pagination
              class="mt-0 mb-0"
            >
              <template slot="thead">
                <th>Año</th>
                <th>Total Acumulado</th>
                <th>Habitacion Acumulado</th>
              </template>
              <template slot-scope="{ data }">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                  <vs-td2
                    ><label>{{ tr.Annio }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.HabitacionAcumulado }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.TotalAcumulado }}</label></vs-td2
                  >
                </tr>
              </template>
            </vs-table2>
          </div>
        </vs-tab>
        <!--Historico-->
        <vs-tab label="Historico">
          <div>
            <vs-table2
              max-items="5"
              :data="Historico"
              pagination
              class="mt-0 mb-0"
            >
              <template slot="thead">
                <th>Año</th>
                <th>Mes</th>
                <th>Serie</th>
                <th>Admision</th>
                <th>Categoria</th>
                <th>Valor</th>
                <th>Participacion</th>
                <th>Cantidad</th>
              </template>
              <template slot-scope="{ data }">
                <tr :key="indextr" v-for="(tr, indextr) in data">
                  <vs-td2
                    ><label>{{ tr.Anio }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.Mes }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.Serie }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.Admision }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.nombre }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.Valor }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.Participacion }}</label></vs-td2
                  >
                  <vs-td2
                    ><label>{{ tr.CuantasOrdenes }}</label></vs-td2
                  >
                </tr>
              </template>
            </vs-table2>
          </div>
        </vs-tab>
      </vs-tabs>
    </vs-popup>
  </div>
</template>

<script>
export default {
  data() {
    return {
      info: {
         //Sinientro
         MaximoVitalicio: null,
         MaximoAnual: null,
         MaximoEvento: null,
      },

      planSiniestro: [],
      vitalicio: [],
      Anual: [],
      Evento: [],
      Cierre: [],
      Historico: [],
    };
  },

  methods: {
    // =========================================================================================================================
    //  SIMIESTROS
    // =========================================================================================================================

    hasConsulta() {
      (this.popupActivoSinestrilidad = true),
        this.consultaPlanSiniestro().PlanSiniestro();
    },
    // ==========================================================
    //  MOTIVO PLAN SINIESTRO
    // ==========================================================
    consultaPlanSiniestro: function () {
      return {
        PlanSiniestro: () => {
          this.axios
            .post("/app/v1_autorizaciones/PlanSiniestro", {
              Operacion: "SINIESTROPLAN",
              Afiliado: this.info.idAdiliado,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.planSiniestro = resp.data.json;
              }
              this.consultaMontoVitalicio().MotivoVitalicio();
            });
        },
      };
    },
    // ==========================================================
    //  MOTIVO VITALICIO
    // ==========================================================
    consultaMontoVitalicio: function () {
      return {
        MotivoVitalicio: () => {
          this.axios
            .post("/app/v1_autorizaciones/MontoVitalicio", {
              Operacion: "MONTOVITALICIO",
              Afiliado: this.info.idAdiliado,
            })
            .then((resp) => {
              if (resp.data.json) {
                this.$map({
                  objeto: this.info,
                  respuestaAxios: resp,
                });
              }
              this.ConsultaVitalicio().Vitalicio();
            });
        },
      };
    },
    // ==========================================================
    // CONSULTA VITALICIO
    // ==========================================================
    ConsultaVitalicio: function () {
      return {
        Vitalicio: () => {
          this.axios
            .post("/app/v1_autorizaciones/Vitalicio", {
              Operacion: "VITALICIO",
              Afiliado: this.info.idAdiliado,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.vitalicio = resp.data.json;
              }
              this.ConsultaAnual().Anual();
            });
        },
      };
    },
    // ==========================================================
    //  CONSULTA ANUAL
    // ==========================================================
    ConsultaAnual: function () {
      return {
        Anual: () => {
          this.axios
            .post("/app/v1_autorizaciones/Anual", {
              Operacion: "ANUAL",
              Afiliado: this.info.idAdiliado,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Anual = resp.data.json;
              }
              this.ConsultaEvento().Evento();
            });
        },
      };
    },
    // ==========================================================
    //  CONSULTA CIERRE
    // ==========================================================
    ConsultaCierre: function () {
      return {
        Cierre: () => {
          this.axios
            .post("/app/v1_autorizaciones/Cierre", {
              Operacion: "CIERRE",
              Afiliado: this.info.idAdiliado,
              NumeroAdhesion: this.info.NumeroAdhesion,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Cierre = resp.data.json;
              }
              this.ConsultaCierre().Cierre();
            });
        },
      };
    },
    // ==========================================================
    //  CONSULTA EVENTO
    // ==========================================================
    ConsultaEvento: function () {
      return {
        Evento: () => {
          this.axios
            .post("/app/v1_autorizaciones/Evento", {
              Operacion: "EVENTO",
              Afiliado: this.info.idAdiliado,
              NumeroAdhesion: this.info.NumeroAdhesion,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Evento = resp.data.json;
              }
              this.ConsultaHistorico().Historico();
            });
        },
      };
    },
    // ==========================================================
    //  CONSULTA HISTORICO
    // ==========================================================
    ConsultaHistorico: function () {
      return {
        Historico: () => {
          this.axios
            .post("/app/v1_autorizaciones/Historico", {
              Operacion: "HISTORICO",
              Afiliado: this.info.idAdiliado,
              NumeroAdhesion: this.info.NumeroAdhesion,
            })
            .then((resp) => {
              if (resp.data.codigo == "0") {
                this.Historico = resp.data.json;
              }
            });
        },
      };
    },
  },
};
</script>
<style></style>
