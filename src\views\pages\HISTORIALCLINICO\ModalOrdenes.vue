<template>
<div class="p-2 historial-container">
    <vs-row class="pt-2">
        <vs-col vs-w="6" vs-justify="center" vs-align="center" class="pr-2">
            <vx-card style="height: 690px;">
                <DxDataGrid :ref="gridCargaProductos" v-bind="DefaultDxGridConfiguration" :data-source="productosOrden" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" :height="'500px'">
                    <DxDataGridToolbar>
                        <DxDataGridItem location="before" template="botonGuardar" />
                        <DxDataGridItem name="searchPanel" />
                    </DxDataGridToolbar>
                    <DxDataGridSelection mode="single" />

                    <DxDataGridEditing :allow-updating="true" :allow-adding="false" :allow-deleting="true" mode="cell" :use-icons="true" :confirmDelete="false" />

                    <DxDataGridColumn :width="50" type="buttons" alignment="center">
                        <DxButton name="delete" />
                    </DxDataGridColumn>
                    <DxDataGridColumn :width="'auto'" data-field="Codigo" caption="Código" alignment="center" :allow-editing="false" />
                    <DxDataGridColumn :width="'auto'" data-field="Nombre" caption="Producto" alignment="center" :allow-editing="false" />
                    <!-- <DxColumn :width="'auto'" data-field="UnidadMedida" caption="U. Medida" alignment="center" :allow-editing="false" /> -->
                    <!-- <DxColumn :width="'auto'" data-field="Precio" caption="Precio" data-type="number" alignment="center" :format="{ type: 'fixedPoint' , precision: 2 }" :allow-editing="false" /> -->
                    <!-- <DxColumn :width="'auto'" data-field="Cantidad" alignment="center" :editor-options="{ editorType: 'dxNumberBox', min: 1 }" :validation-rules="[{ type: 'required', message: 'Campo obligatorio' }]" /> -->

                    <template #botonGuardar>
                        <div class="w-full div-button">
                            <vs-button class="button" color="success" type="filled" :disabled="productosOrden.length == 0" @click="GuardarOrden">
                                <font-awesome-icon :icon="['fas', 'save']" class="pr-2" style="font-size: 16px" />
                                <span> Guardar </span>
                            </vs-button>
                        </div>
                    </template>
                </DxDataGrid>
                <DxForm labelMode="floating" :form-data.sync="observaciones" :ref='formObsevaciones'>
                    <DxFormGroupItem>
                        <DxFormItem data-field="Observaciones" editor-type="dxTextArea" :editor-options="{ height: 100, maxLength: 255 }" />
                    </DxFormGroupItem>
                </DxForm>
            </vx-card>
        </vs-col>
        <vs-col vs-justify="center" vs-align="center" vs-w="6" class="pr-2">
            <vx-card style="height: 690px;">
                <DxDataGrid :ref="gridProductos" v-bind="DefaultDxGridConfiguration" :data-source="productos" :headerFilter="{visible:true, allowSearch:true}" :width="'100%'" :height="'600px'" @row-dbl-click="AgregarProducto">
                    <DxDataGridSelection mode="single" />
                    <DxDataGridColumn :width="'auto'" data-field="Codigo" caption="Código" alignment="center" />
                    <DxDataGridColumn :width="'80%'" data-field="Nombre" caption="Producto" alignment="center" />
                    <!-- <DxColumn :width="'auto'" data-field="Precio" caption="Precio" data-type="number" alignment="center" :format="{ type: 'fixedPoint' , precision: 2 }" /> -->
                </DxDataGrid>
            </vx-card>
            <!-- </div> -->
        </vs-col>
    </vs-row>
</div>
</template>

<script>
import {
    DefaultDxGridConfiguration
} from './data'

const gridCargaProductos = 'gridCargaProductos'
const gridProductos = 'gridProductos'
const formObsevaciones = 'formObsevaciones'

const formularioDatos = 'formDatos'
const formularioBeneficios = 'formBeneficios'

export default {
    name: 'Prescripcion',
    components: {},
    data() {
        return {
            formDatos: null,
            formBeneficios: null,
            gridCargaProductos,
            gridProductos,
            DefaultDxGridConfiguration,
            formObsevaciones,

            formularioDatos,
            formularioBeneficios,

            infoProducto: {},

            productos: [], //Variable que guarda el catálogo de productos
            productosOrden: [], //Variable que guarda los productos agregados a la orden
            observaciones: null, //Variable para guardar las observaciones de la orden
        }
    },
    props: {
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdAfiliado: null,
        IdConsulta: null,
        NombreMedico: null,
        CodigoMedico: null,
        NivelPreciosExt: null,
        CodigoDiagnostico: null,
        Telefono: null,
        Asegura: null
    },
    methods: {
        submit() {
            this.dataGrid.saveEditData()
        },

        CargarProductos() {
            this.axios.post('/app/v1_afiliados/BusquedaServiciosEstudios', {
                    CodigoAseguradora: this.Asegura,
                    NivelPrecio: this.NivelPreciosExt,
                })
                .then(resp => {
                    this.productos = resp.data.json
                })
        },

        LimpiarVariables() {
            this.productos = []
            this.productosOrden = []
            this.observaciones.Observaciones = ''
            this.formularioObservaciones.repaint();

            this.dataGridProductos.refresh();
            this.dataGridProductos.clearFilter();
            this.dataGridProductos.clearSelection();
            this.dataGridProductos.clearSorting();

            this.dataGridCargaProductos.refresh();
            this.dataGridCargaProductos.clearFilter();
            this.dataGridCargaProductos.clearSelection();
            this.dataGridCargaProductos.clearSorting();
        },

        AgregarProducto(e) {

            e.data.Precio = parseFloat(e.data.Precio).toFixed(2)
            this.productosOrden.push(e.data)

            this.dataGridProductos.clearSelection()
            this.infoProducto = {}
        },
        SeleccionarProducto(e) {
            this.infoProducto = {
                ...e.data
            }
        },
        GuardarOrden() {
            var arrayProductos = []

            for (const i of this.productosOrden) {
                arrayProductos.push(i.Codigo.trim())
            }

            this.axios.post('/app/v1_afiliados/RegistroSolucitudProcedimiento', {
                    IdAfiliado: this.IdAfiliado,
                    IdCita: this.IdCita,
                    IdConsulta: this.IdConsulta,
                    IdCliente: this.IdCliente,
                    NivelPrecios: this.NivelPreciosExt,
                    CodigoMedico: this.CodigoMedico,
                    Observacion: this.observaciones.Observaciones ? this.observaciones.Observaciones : '',
                    Productos: arrayProductos,
                    Diagnosticos: [this.CodigoDiagnostico],
                    SMSNotificacion: this.Telefono != '' ? [this.Telefono] : null
                })
                .then(resp => {
                    if (resp.data.codigo === 0) {
                        this.LimpiarVariables()
                        this.$emit('OrdenCorrecta', resp.data.IdSolicitud)
                    }
                })
        },
    },
    mounted() {},
    watch: {},
    computed: {

        dataGridCargaProductos: function () {
            return this.$refs[gridCargaProductos].instance;
        },

        dataGridProductos: function () {
            return this.$refs[gridProductos].instance;
        },

        formularioObservaciones: function () {
            return this.$refs[formObsevaciones].instance;
        },

        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },
        colCountByScreenDatos() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 3,
                    lg: 3
                };
        },
    },
}
</script>

<style scoped>
.div-button {
    display: flex;
    justify-content: center;
}
</style>
