<template>
<div id="containter">
    <buscador ref="buscar_privilegios" buscador_titulo="Buscador / Permisos" :api="'app/administracion/buscar_privilegios'" :api_filtro="{IdRol:rol_select.IdRol}" campo_llave="IdPrivilegio" :campos="['Nombre_Aplicativo','Codigo','Nombre_Funcionalidad', 'Nombre_Privilegio','Descripcion_Privilegio']" :titulos="['Aplicativo','Codigo','Funcionalidad','Nombre','Descripción']" :multiselect="true" :all_datos="true" />
    <buscador ref="buscar_corporativos" buscador_titulo="Buscador / Corporativos" :api="'app/administracion/buscar_corporativos'" campo_llave="IdCorporativo" :campos="['Corporativo', 'Apellidos','Nombres']" :titulos="['Corporativo', 'Apellidos','Nombres']" :multiselect="false" />
    <buscador ref="buscar_empresas" buscador_titulo="Buscador / Empresas" :api="'app/administracion/buscar_empresas'" campo_llave="IdCorporativo" :campos="['Codigo','Nombre']" :titulos="['Codigo','Nombre']" :multiselect="false" />

    <!-- NUEVO ROL -->
    <!-- ****************************************************************************************************************************************************************** -->
    <vs-popup classContent="popup-example" title="Nuevo Rol" :active.sync="rol_editar">
        <div class="flex flex-wrap">
            <div class="w-full" style="margin-bottom:15px">
                <label>Nombre</label>
                <vs-input v-model="rol_nuevo" style="width:100%" />
            </div>
        </div>
        <vs-button style="float:right;margin-bottom:12px" v-on:click="guardar_rol()">Guardar</vs-button>
    </vs-popup>

    <!-- EDITAR PRIVILEGIOS -->
    <!-- ****************************************************************************************************************************************************************** -->
    <vs-popup classContent="popup-example" :title="rol_select.Nombre" :active.sync="usuario_editar">
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
            <div class="flex flex-wrap">
                <div class="w-full sm:w-1/3" style="margin-bottom:15px">
                    <label>Corporativo</label>
                    <h5>{{usuario_select.IdCorporativo}}</h5>
                </div>
                <div class="w-full sm:w-1/3" style="margin-bottom:15px">
                    <label>Nombre</label>
                    <h5>{{usuario_select.Nombres}}</h5>
                </div>
                <div class="w-full sm:w-1/3" style="margin-bottom:15px">
                    <label>Apellido</label>
                    <h5>{{usuario_select.Apellidos}}</h5>
                </div>
            </div>
            <vs-alert color="danger" title="Precaución" active="true">
                Eliminar todas las empresas de un colaborador implica eliminar al colaborador del rol
            </vs-alert>
        </div>

        <vs-tabs position="left" color="success">
            <vs-tab :label="'Empresa ('+((usuario_select._level)?usuario_select._level.length:0)+')'">
                <h5 style="float:left">Empresas</h5><br>
                <!-- <form v-on:submit.prevent=""> -->
                <vs-button color="success" type="filled" v-on:click="buscar_empresas()" icon-pack="feather" icon="icon-plus">Agregar Empresa</vs-button>
                <!-- <vx-input-group class="">
                        <vs-input v-model="usuario_select.empresa_nueva" v-mask="'XXX'" />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                            </div>
                        </template>
                    </vx-input-group> -->
                <!-- </form> -->
                <br>
                <hr>

                <vs-table v-if="usuario_select._level && usuario_select._level.length>0 " :data="usuario_select._level">
                    <template slot="thead">
                        <vs-th>Código</vs-th>
                        <vs-th>Empresa</vs-th>
                        <vs-th></vs-th>
                    </template>

                    <template slot-scope="{data}">
                        <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td>
                                ({{ data[indextr].Empresa }})
                            </vs-td>

                            <vs-td>
                                {{data[indextr].Nombre}}
                            </vs-td>

                            <vs-td width="50px">
                                <vs-button color="danger" icon-pack="far" icon="fa-trash-alt" style="display:inline-block" v-on:click="eliminar_usuario_empresa(data[indextr].Empresa)"></vs-button>
                            </vs-td>
                        </vs-tr>
                    </template>
                </vs-table>
            </vs-tab>
            <!--  -->
            <vs-tab :label="'IP ('+((usuario_select.ips)?usuario_select.ips.length:0)+')'">
                <h5 style="float:left">IP</h5><br>
                <vx-input-group class="">
                    <vs-input v-model="usuario_select.ip_nueva" />
                    <template slot="append">
                        <div class="append-text btn-addon">
                            <button type="submit" v-show="false" name="button"></button>
                            <vs-button id="button-with-loading" @click="guardar_usuario_ip()"></vs-button>
                        </div>
                    </template>
                </vx-input-group>
                <vs-table v-if="usuario_select.ips && usuario_select.ips.length>0 " :data="usuario_select.ips">
                    <template slot="thead">
                        <vs-th>IP</vs-th>
                        <vs-th></vs-th>
                    </template>

                    <template slot-scope="{data}">
                        <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td>
                                {{ data[indextr].Ip }}
                            </vs-td>

                            <vs-td width="50px">
                                <vs-button color="danger" icon-pack="far" icon="fa-trash-alt" style="display:inline-block" v-on:click="eliminar_usuario_ip(data[indextr].Ip)"></vs-button>
                            </vs-td>
                        </vs-tr>
                    </template>
                </vs-table>
            </vs-tab>
        </vs-tabs>

        <vs-table v-if="aplicativos_funcionalidades._level && aplicativos_funcionalidades._level.length>0 && aplicativos_privilegios" max-items="5" pagination :data="aplicativos_funcionalidades._level">
            <template slot="thead">
                <vs-th>Nombre</vs-th>
                <vs-th>Descripción</vs-th>
                <vs-th>Activo</vs-th>
            </template>

            <template slot-scope="{data}">
                <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="data[indextr].Nombre_Privilegio">
                        {{ data[indextr].Nombre_Privilegio }}
                    </vs-td>

                    <vs-td :data="data[indextr].Descripcion_Privilegio">
                        {{ data[indextr].Descripcion_Privilegio }}
                    </vs-td>

                    <vs-td :data="data[indextr].Activo_Privilegio">
                        <vs-switch v-model="data[indextr].Activo_Privilegio" v-on:click="guardar_privilegios_activo(data[indextr])" />
                    </vs-td>
                </vs-tr>
            </template>
        </vs-table>
    </vs-popup>

    <!-- INFORMACIÓN DEL PRIVILEGIO -->
    <!-- ****************************************************************************************************************************************************************** -->
    <vs-popup classContent="popup-example" :title="rol_select.Nombre" :active.sync="rol_mostrar" fullscreen>
        <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
            <div class="flex flex-wrap">
                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Nombre</label>
                    <h5>{{rol_select.Nombre}}</h5>
                </div>

                <div class="w-full sm:w-1/2" style="margin-bottom:15px">
                    <label>Activo</label>
                    <h5><span style="font-size:20px" :class="rol_select.Activo==true?'far fa-check-square':'far fa-square'"></span></h5>
                </div>

            </div>
        </div>

        <!-- <vs-tab label="Left"> -->
        <!-- left -->
        <vs-tabs color="primary">
            <vs-tab label="Usuarios">
                <h5 style="float:left">Usuarios</h5>
                <vs-divider />
                <form v-on:submit.prevent="guardar_rol_usuario()">
                    <vx-input-group>

                        <vs-input v-model="rol_select.nuevo_usuario" v-mask="'##########'" @change="buscar_nombre_corporativo" />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button color="success" type="filled" v-on:click="buscar_corporativos()" icon-pack="feather" icon="icon-search"></vs-button>
                                <vs-button color="primary" type="filled" v-on:click="guardar_rol_usuario()">Asignar Usuario</vs-button>
                            </div>
                        </template>

                    </vx-input-group>
                    {{usuario}}
                </form>

                <vs-divider />

                <vs-table v-if="rol_usuario && rol_usuario.length>0" :data="rol_usuario">
                    <template slot="thead">
                        <vs-th>Corporativo</vs-th>
                        <vs-th>Nombre</vs-th>
                        <vs-th>Empresa</vs-th>
                        <vs-th>Acciones</vs-th>
                    </template>

                    <template slot-scope="{data}">
                        <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td :data="data[indextr].IdCorporativo">
                                {{ data[indextr].IdCorporativo }}
                            </vs-td>

                            <vs-td :data="data[indextr].Apellidos">
                                {{ data[indextr].Apellidos }}, {{ data[indextr].Nombres }}
                            </vs-td>

                            <vs-td width="100px">
                                {{ (data[indextr]._level.length==0)?'--':'' }}
                                <ul>
                                    <li v-for="(data,index) in data[indextr]._level" :key="index">
                                        * {{data.Empresa}}
                                    </li>
                                </ul>
                            </vs-td>

                            <vs-td>
                                <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" v-on:click="editarUsuario(data[indextr])"></vs-button>
                                <vs-button color="danger" icon-pack="far" icon="fa-trash-alt" style="display:inline-block;margin-right:2px" v-on:click="eliminarUsuario(rol_select.Nombre,data[indextr])"></vs-button>
                            </vs-td>

                        </vs-tr>
                    </template>
                </vs-table>
            </vs-tab>
            <!--  -->
            <vs-tab label="Permisos">
                <h5 style="float:left">Permisos</h5>
                <vs-divider />
                <div style="padding-bottom:14px">
                    <div style="float:right;">Seleccionados: {{privilegio_seleccion.length}}</div>
                    <vs-button color="primary" type="filled" v-on:click="Buscar_Privilegios()">Permisos</vs-button>
                </div>

                <vs-divider position="left">Datos Filtrados</vs-divider>

                <div class="flex flex-wrap">
                    <div class="w-full sm:w-1/3" style="margin-bottom: 15px;">
                        <vx-input-group>
                            <vs-input id="search-encabezado" v-model="filtro" @input="datosFiltrados_Busqueda()" class="w-full" placeholder="Ingrese término" />
                            <template slot="append">
                                <div class="append-text btn-addon">
                                    <button type="submit" v-show="false" name="button"></button>
                                    <vs-button id="button-with-loading" color="primary" icon-pack="feather" @click="datosFiltrados_Busqueda()" icon="icon-search"></vs-button>
                                </div>
                            </template>
                        </vx-input-group>
                    </div>
                </div>

                <!-- filtros de er-->
                <div v-for="(rol, key) in privilegios_filtrados" @click="rol.activo = !rol.activo" class="permiso" :key="key">
                    <div>
                        {{ rol.Nombre_Aplicativo }}
                    </div>
                    <div v-if="rol_privilegio.length > 0 && rol.activo" class="permiso-sub">
                        <div v-for="(item, index) in rol._level" :key="index">
                            <div v-for="(tr, indextr) in item._level" :key="indextr">
                                <vs-divider />
                                <div class="flex flex-wrap">
                                    <div class="w-full sm:w-1/6 p-1">
                                        <h6>
                                            {{ item.Nombre_Funcionalidad }}
                                        </h6>
                                    </div>
                                    <div class="w-full sm:w-1/6 ">
                                        <h6>
                                            {{ item.Codigo }}
                                        </h6>
                                    </div>
                                    <div class="w-full sm:w-1/6 ">
                                        <h6>
                                            {{ tr.Nombre_Privilegio }}
                                        </h6>
                                    </div>
                                    <div class="w-full sm:w-1/4 ">
                                        <h6>
                                            {{ tr.Descripcion_Privilegio }}
                                        </h6>
                                    </div>
                                    <div class="w-full sm:w-1/4 ">
                                        <div class="flex flex-wrap">
                                            <div class="w-full sm:w-1/2">
                                                <span style="font-size:20px" :class="tr.Activo_Privilegio == true ? 'far fa-check-square' : 'far fa-square'"></span>
                                            </div>
                                            <br>
                                            <div class="w-full sm:w-1/2">
                                                <vs-button color="danger" icon-pack="far" icon="fa-trash-alt" style="display:inline-block;margin-right:2px" v-on:click="eliminar_rol_privilegio(rol_select.Nombre, item.Nombre_Funcionalidad, data[indextr])"></vs-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </vs-tab>
        </vs-tabs>

        <vs-table v-if="rol_select && rol_select._level && rol_select._level.length>0" max-items="5" pagination :data="rol_select._level">
            <template slot="thead">
                <vs-th>Codigo</vs-th>
                <vs-th>Nombre</vs-th>
                <vs-th>Descripción</vs-th>
                <vs-th>Url</vs-th>
                <vs-th>Activo</vs-th>
                <vs-th>Oculto</vs-th>
                <vs-th>Acciones</vs-th>
            </template>

            <template slot-scope="{data}">
                <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="data[indextr].Codigo">
                        {{ data[indextr].Codigo }}
                    </vs-td>

                    <vs-td :data="data[indextr].Nombre_Funcionalidad">
                        {{ data[indextr].Nombre_Funcionalidad }}
                    </vs-td>

                    <vs-td :data="data[indextr].Descripcion_Funcionalidad">
                        {{ data[indextr].Descripcion_Funcionalidad }}
                    </vs-td>

                    <vs-td :data="data[indextr].Url">
                        {{ data[indextr].Url }}
                    </vs-td>

                    <vs-td :data="data[indextr].Activo_Funcionalidad">
                        <span style="font-size:20px" :class="data[indextr].Activo_Funcionalidad==true?'far fa-check-square':'far fa-square'"></span>
                    </vs-td>

                    <vs-td :data="data[indextr].Oculto_Funcionalidad">
                        <span style="font-size:20px" :class="data[indextr].Oculto_Funcionalidad==true?'far fa-check-square':'far fa-square'"></span>
                    </vs-td>

                    <vs-td>
                        <vs-button color="primary" icon-pack="feather" icon="icon-edit-1" style="display:inline-block;margin-right:2px" v-on:click="editar_funcionalidad(rol_select,data[indextr])"></vs-button>
                        <vs-button color="primary" icon-pack="feather" icon="icon-lock" style="display:inline-block" v-on:click="cargar_privilegios(data[indextr])"></vs-button>
                    </vs-td>

                </vs-tr>
            </template>
        </vs-table>
    </vs-popup>

    <!-- ****************************************************************************************************************************************************************** -->
    <vx-card id="" title="Roles y Permisos">
        <div class="content content-pagex">
            <!-- {{aplicativos}} -->
            <div class="flex flex-wrap">
                <div class="w-full sm:w-1/3" style="margin-bottom:15px">
                    <label class="vs-input--label">Buscar</label>
                    <vx-input-group>
                        <vs-input v-model="filtro_rol" />
                        <template slot="append">
                            <div class="append-text btn-addon">
                                <button type="submit" v-show="false" name="button"></button>
                                <vs-button id="button-with-loading" color="primary" icon-pack="feather" icon="icon-search"></vs-button>
                            </div>
                        </template>
                    </vx-input-group>
                </div>
                <div class="w-full sm:w-1/3">
                </div>
                <div class="w-full sm:w-1/3">
                    <div style="float:right;padding-bottom:14px">
                        <br>
                        <vs-button color="primary" type="filled" v-on:click="rol_editar=true;rol_nuevo=''">Nuevo Rol</vs-button>
                    </div>
                </div>
            </div>

            <vs-table v-if="rol_filtro.length>0" max-items="15" pagination :data="rol_filtro" @selected="handleSelected">
                <template slot="thead">
                    <!-- <vs-th>Id</vs-th> -->
                    <vs-th>Nombre</vs-th>
                    <!-- <vs-th>Descripción</vs-th> -->
                    <vs-th width="10%">Activo</vs-th>
                </template>

                <template slot-scope="{data}">
                    <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">

                        <vs-td :data="data[indextr].Nombre">
                            {{ data[indextr].Nombre }}
                        </vs-td>

                        <vs-td :data="data[indextr].Activo">
                            <span style="font-size:20px" :class="data[indextr].Activo==true?'far fa-check-square':'far fa-square'"></span>
                        </vs-td>

                    </vs-tr>
                </template>
            </vs-table>
        </div>
    </vx-card>
</div>
</template>

<script>
// import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"

export default {
    data() {
        return {
            roles: [],
            rol_editar: false,
            rol_select: {}, //rol seleccionado
            rol_usuario: [], //listado de usuarios por rol
            rol_privilegio: [], //listado de privilegios por rol
            rol_mostrar: false, //popup con la información de los usuarios y privilegios del rol
            rol_nuevo: '', //nombre del nuevo rol

            privilegios: [],
            privilegio_seleccion: [],
            privilegios_filtrados: [],
            usuario_editar: false,
            usuario_select: {},

            aplicativos_funcionalidades: {},
            // filtro
            filtro_rol: '',
            usuario: null,
            filtro: '',
        }
    },
    computed: {
        rol_filtro() {
            if (this.filtro_rol != '') {
                return this.roles.filter(data => data.Nombre.toUpperCase().indexOf(this.filtro_rol.toUpperCase()) >= 0)
            } else {
                return this.roles
            }
        }
    },
    components: {
        // Multiselect
    },
    methods: {

        datosFiltrados_Busqueda() {

            let rolesPermisosFiltrado = this.rol_privilegio.filter(rol =>
                rol.Nombre_Aplicativo.toLowerCase().includes(this.filtro.toLowerCase()) ||
                rol._level.filter(item =>
                    item.Nombre_Funcionalidad.toLowerCase().includes(this.filtro.toLowerCase()) ||
                    item.Codigo.toLowerCase().includes(this.filtro.toLowerCase())

                ).length > 0
            );

            let paginasFiltradas =
                rolesPermisosFiltrado.map(menu => {
                    return {
                        ...menu,
                        _level: menu._level.filter(pagina => pagina.Codigo.toLowerCase().includes(this.filtro.toLowerCase()) ||
                            pagina.Nombre_Funcionalidad.toLowerCase().includes(this.filtro.toLowerCase())
                        )
                    }
                })
            this.privilegios_filtrados = paginasFiltradas
        },

        cargar_roles() {
            this.axios.post('/app/administracion/obtener_rol', {})
                .then(resp => {
                    if (resp.data.codigo == 0) this.roles = resp.data.json
                })
        },

        cargar_privilegios() {
            this.axios.post('/app/administracion/obtener_privilegios', {})
                .then(resp => {
                    if (resp.data.codigo == 0) this.privilegios = resp.data.json
                })
        },

        cargar_rol_usuario(IdRol) {
            this.axios.post('/app/administracion/obtener_rol_usuario', {
                    idRol: IdRol
                })
                .then(resp => {
                    if (resp.data.codigo == 0) this.rol_usuario = resp.data.json
                })
        },

        cargar_rol_usuario_empresa(IdRol, IdCorporativo) {
            return new Promise(resolve => {
                this.axios.post('/app/administracion/obtener_rol_usuario_empresa', {
                        IdRol: IdRol,
                        IdCorporativo: IdCorporativo
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) this.usuario_select._level = resp.data.json
                        resolve(true)
                    })
            })

        },

        cargar_rol_usuario_ip(IdCorporativo) {
            this.axios.post('/app/administracion/obtener_rol_usuario_ip', {
                    IdCorporativo: IdCorporativo
                })
                .then(resp => {
                    if (resp.data.codigo == 0) this.usuario_select.ips = resp.data.json
                })
        },

        cargar_rol_privilegio(IdRol) {
            this.axios.post('/app/administracion/obtener_rol_privilegio', {
                    idRol: IdRol
                })
                .then(resp => {
                    if (resp.data.codigo == 0) {

                        this.rol_privilegio = resp.data.json.map(m => {
                            return {
                                ...m,
                                activo: false
                            }
                        })
                        this.privilegios_filtrados = this.rol_privilegio
                    }
                })
        },

        // ********************************************************* BUSCADOR ***********************************************************************
        // ******************************************************************************************************************************************
        Buscar_Privilegios() {
            this.$refs.buscar_privilegios.iniciar((data) => {
                if (data != null) {
                    this.privilegio_seleccion = data
                    let privilegios = ""
                    this.privilegio_seleccion.map(data => {
                        if (privilegios != "") privilegios += ","
                        privilegios += data.IdPrivilegio
                    })
                    this.guardar_rol_privilegio(privilegios)
                }
            })
        },

        buscar_corporativos() {
            this.$refs.buscar_corporativos.iniciar((data) => {
                if (data != null) {
                    this.usuario = data.Apellidos + ',' + data.Nombres
                    this.rol_select.nuevo_usuario = data.Corporativo
                } else {
                    this.usuario = null
                    this.rol_select.nuevo_usuario = null
                }
            })
        },

        buscar_nombre_corporativo() {
            if (this.rol_select.nuevo_usuario > 0)
                this.$refs.buscar_corporativos.validar({
                    Corporativo: this.rol_select.nuevo_usuario
                }, (data) => {
                    if (data != null && data.length > 0) {
                        this.usuario = data[0].Apellidos + ',' + data[0].Nombres
                    } else {
                        this.rol_select.nuevo_usuario = null
                        this.usuario = null
                    }
                })
        },
        buscar_empresas() {
            this.$refs.buscar_empresas.iniciar((data) => {
                if (data != null) {
                    this.usuario_select.empresa_nueva = data.Codigo
                    this.guardar_usuario_empresa()

                }
            })
        },

        Obtener_Privilegios() {
            this.privilegio_seleccion = []
            this.$refs.buscar_privilegios.obtenerSeleccion({
                IdRol: this.rol_select.IdRol
            }, data => {
                if (data != null) {
                    this.privilegio_seleccion = data
                }
            })
        },

        // ********************************************************* GUARDAR ************************************************************************
        // ******************************************************************************************************************************************
        guardar_rol() {
            this.axios.post('/app/administracion/guardar_rol', {
                    nombre: this.rol_nuevo,
                })
                .then(() => {
                    this.rol_editar = false
                    this.cargar_roles()
                })
        },

        guardar_rol_usuario() {
            this.axios.post('/app/administracion/guardar_usuario', {
                    idRol: this.rol_select.IdRol,
                    idCorporativo: this.rol_select.nuevo_usuario,
                })
                .then(() => {
                    this.cargar_rol_usuario(this.rol_select.IdRol)
                    this.usuario = null
                    this.rol_select.nuevo_usuario = null
                })
        },

        guardar_usuario_empresa() {
            this.axios.post('/app/administracion/guardar_usuario_empresa', {
                    idRol: this.usuario_select.IdRol,
                    idCorporativo: this.usuario_select.IdCorporativo,
                    empresa: this.usuario_select.empresa_nueva,
                })
                .then(() => {
                    this.usuario_select.empresa_nueva = null
                    this.cargar_rol_usuario(this.usuario_select.IdRol)
                    this.cargar_rol_usuario_empresa(this.usuario_select.IdRol, this.usuario_select.IdCorporativo)
                })
        },

        guardar_usuario_ip() {
            this.axios.post('/app/administracion/guardar_usuario_ip', {
                    idRol: this.usuario_select.IdRol,
                    idCorporativo: this.usuario_select.IdCorporativo,
                    ip: this.usuario_select.ip_nueva,
                })
                .then(() => {
                    this.usuario_select.ip_nueva = ''
                    this.cargar_rol_usuario(this.usuario_select.IdRol)
                    this.cargar_rol_usuario_ip(this.usuario_select.IdCorporativo)
                })
        },

        guardar_rol_privilegio(privilegios) {
            this.axios.post('/app/administracion/guardar_rol_privilegios', {
                    idRol: this.rol_select.IdRol,
                    privilegios: privilegios
                })
                .then(() => {
                    this.cargar_rol_privilegio(this.rol_select.IdRol)
                })
        },

        // ******************************************************** EDITAR **************************************************************************
        // ******************************************************************************************************************************************

        editar_privilegios(data) {
            this.aplicativos_privilegios = true
            this.aplicativos_funcionalidades = data
            this.aplicativos_funcionalidades = {
                ...this.aplicativos_funcionalidades,
                nombre: '',
                descripcion: '',
                activo: true
            }
        },

        editarUsuario(select) {
            this.usuario_editar = true
            this.usuario_select = {
                ...select,
                ips: [],
                empresa_nueva: '',
                ip_nueva: ''
            }
            this.cargar_rol_usuario_empresa(this.usuario_select.IdRol, this.usuario_select.IdCorporativo)
            this.cargar_rol_usuario_ip(this.usuario_select.IdCorporativo)

        },

        // ***************************************************** ELIMINAR ***************************************************************************
        // ******************************************************************************************************************************************

        eliminarUsuario(rol, select) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Desea eliminar la asociación del usuario ' + select.Apellidos + ', ' + select.Nombres + ' del rol ' + rol + ' ?',
                accept: () => {
                    this.usuario_select = {
                        ...select,
                        ips: [],
                        empresa_nueva: '',
                        ip_nueva: ''
                    }
                    this.cargar_rol_usuario_empresa(this.usuario_select.IdRol, this.usuario_select.IdCorporativo)
                        .then(() => {
                            this.usuario_select._level.map(data => {
                                this.axios.post('/app/administracion/eliminar_usuario_empresa', {
                                        idRol: this.usuario_select.IdRol,
                                        idCorporativo: this.usuario_select.IdCorporativo,
                                        empresa: data.Empresa,
                                    })
                                    .then(() => {
                                        this.cargar_rol_usuario(this.usuario_select.IdRol)
                                    })
                            })
                        })
                }
            })
        },

        eliminar_usuario_empresa(empresa) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Desea eliminar la empresa ' + empresa + ' asociada al usuarios ' + this.usuario_select.IdCorporativo + ' [' + this.usuario_select.Apellidos + ', ' + this.usuario_select.Nombres + '] ?',
                accept: () => {
                    this.axios.post('/app/administracion/eliminar_usuario_empresa', {
                            idRol: this.usuario_select.IdRol,
                            idCorporativo: this.usuario_select.IdCorporativo,
                            empresa: empresa,
                        })
                        .then(() => {
                            this.cargar_rol_usuario(this.usuario_select.IdRol)
                            this.cargar_rol_usuario_empresa(this.usuario_select.IdRol, this.usuario_select.IdCorporativo)
                        })
                }
            })

        },

        eliminar_usuario_ip(ip) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Desea eliminar la restricción de ip?',
                accept: () => {
                    this.axios.post('/app/administracion/eliminar_usuario_ip', {
                            idCorporativo: this.usuario_select.IdCorporativo,
                            ip: ip,
                        })
                        .then(() => {
                            this.cargar_rol_usuario(this.usuario_select.IdRol)
                            this.cargar_rol_usuario_ip(this.usuario_select.IdCorporativo)
                        })
                }
            })
        },

        eliminar_rol_privilegio(rol, funcionalida, resp) {
            this.$vs.dialog({
                type: 'confirm',
                color: 'danger',
                title: 'Confirmación',
                text: '¿Desea eliminar el privilegio ' + resp.Nombre_Privilegio + ' [' + funcionalida + '] asociada al rol ' + rol + '?',
                accept: () => {
                    this.axios.post('/app/administracion/eliminar_rol_privilegio', {
                            idRol: this.rol_select.IdRol,
                            idPrivilegio: resp.IdPrivilegio
                        })
                        .then(() => {
                            this.cargar_rol_privilegio(this.rol_select.IdRol)
                        })
                }
            })
        },

        // ******************************************************************************************************************************************
        // ******************************************************************************************************************************************
        // ******************************************************************************************************************************************

        handleSelected(tr) {

            this.rol_mostrar = true
            this.rol_select = {
                ...tr,
                nuevo_usuario: null,
                nuevo_privilegio: null
            }

            this.cargar_rol_usuario(tr.IdRol)
            this.cargar_rol_privilegio(tr.IdRol)

            this.Obtener_Privilegios()

        },

    },
    created() {
        this.cargar_roles()
        // this.cargar_privilegios()
    }
}
</script>

<style scoped>
.header-sidebar {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
}

.header-sidebar h4 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

}

.header-sidebar h4>button {
    margin-left: 10px;
}

.sidebarx {
    z-index: 99999;
    position: absolute;
}

.vs-sidebar {
    max-width: 350px;
}

.footer-sidebar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.footer-sidebar>button {
    border: 0px solid rgba(0, 0, 0, 0) !important;
    border-left: 1px solid rgba(0, 0, 0, 0.07) !important;
    border-radius: 0px !important;
}

.permiso {
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    margin-bottom: 5px;
    cursor: pointer;
    font-size: 15px;

}

.permiso-sub {
    padding: 15px;
}

.permiso-sub table {
    border-spacing: 0px;
}

.permiso-sub table tr td {
    padding: 2px
}

.permiso-sub table tr:hover {
    background-color: #ccc;

}
</style>
