<template>
<div class="p-2">
    <vx-card>
        <DxForm :form-data.sync="signosVitales" labelMode="floating" :read-only="Modulo === 2 ? true : false">
            <DxFormItem item-type="group" :col-span="1" :col-count="2">
                <DxFormItem item-type="group" caption="Peso y estatura" :col-span="1" :col-count="3">
                    <DxFormItem data-field="Libras" editor-type="dxNumberBox" :editor-options="{min: 0, max: 500, format: '#0', step: 1, onValueChanged: calcularKilos }" />
                    <DxFormItem data-field="Onzas" editor-type="dxNumberBox" :editor-options="{min: 0, max: 15, format: '#0', step: 1, onValueChanged: calcularKilos }" />
                    <DxFormItem data-field="Estatura" editor-type="dxNumberBox" :editor-options="{min: 0, max: 300, format: '#0.0#', step: 0.1, onValueChanged: calcularIMC }">
                        <DxFormLabel :text="'Estatura (cm)'" />
                    </DxFormItem>
                    <DxFormItem data-field="Kilos" editor-type="dxNumberBox" :editor-options="{min: 0, max: 100, format: '#0.0##', readOnly: true, onValueChanged: calcularIMC, tabIndex:-1, value: signosVitales.Kilos }" />
                    <DxFormItem data-field="IMC" editor-type="dxNumberBox" :editor-options="{min: 0, max: 1000, format: '#0.0#', readOnly: true, tabIndex:-1, value: signosVitales.IMC }">
                        <DxFormLabel :text="'Índice masa corporal'" />
                    </DxFormItem>
                    <DxFormItem data-field="DescripcionIMC" editor-type="dxTextBox" :editor-options="{ readOnly: true, tabIndex:-1, value: signosVitales.DescripcionIMC }">
                        <DxFormLabel :text="'Descripción IMC'" />
                    </DxFormItem>
                </DxFormItem>
                <DxFormItem item-type="group" caption="Temperatura y presión" :col-span="1" :col-count="2">
                    <DxFormItem data-field="Temperatura" editor-type="dxNumberBox" :editor-options="{min: 0, max: 43, format: '#0.0#', step: 0.5}" />
                    <DxFormItem data-field="Pulso" editor-type="dxNumberBox" :editor-options="{min: 0, max: 400, format: '#0', step: 1 }" />
                    <DxFormItem data-field="Sistolica" editor-type="dxNumberBox" :editor-options="{min: 0, max: 400, format: '#0' }">
                        <DxFormLabel :text="'Sistólica'" />
                    </DxFormItem>
                    <DxFormItem data-field="Diastolica" editor-type="dxNumberBox" :editor-options="{min: 0, max: 150, format: '#0', step: 1 }">
                        <DxFormLabel :text="'Diastólica'" />
                    </DxFormItem>
                    <DxFormItem data-field="FrecuenciaRespiratoria" editor-type="dxNumberBox" :editor-options="{min: 0, max: 100, step: 1, format: '#0' }">
                        <DxFormLabel :text="'Frecuencia respiratoria'" />
                    </DxFormItem>
                    <DxFormItem data-field="CircunferenciaCefalica" editor-type="dxNumberBox" :editor-options="{min: 30, max: 55, format: '#0.0#', step: 0.01}">
                        <DxFormLabel :text="'Circunferencia cefálica'" />
                    </DxFormItem>
                </DxFormItem>
            </DxFormItem>
            <DxFormItem item-type="group" caption="Nutrición" :col-span="1" :col-count="4">
                <DxFormItem data-field="Grasa" editor-type="dxNumberBox" :editor-options="{min: 5, max: 60, format: '#0.0', step: 0.1}">
                    <DxFormLabel :text="'% grasa'" />
                </DxFormItem>
                <DxFormItem data-field="Agua" editor-type="dxNumberBox" :editor-options="{min: 20, max: 70, format: '#0.0', step: 0.1 }">
                    <DxFormLabel :text="'% agua'" />
                </DxFormItem>
                <DxFormItem data-field="GrasaVisceral" editor-type="dxNumberBox" :editor-options="{min: 1, max: 100, format: '#0.0', step: 0.1 }">
                    <DxFormLabel :text="'% grasa visceral'" />
                </DxFormItem>
                <DxFormItem data-field="Musculo" editor-type="dxNumberBox" :editor-options="{min: 0, format: '#0.0', step: 0.1 }">
                    <DxFormLabel :text="'Peso músculo (kg)'" />
                </DxFormItem>
                <DxFormItem data-field="Metabolismo" editor-type="dxNumberBox" :editor-options="{min: 1000, max: 10000, format: '#0', step: 1 }">
                    <DxFormLabel :text="'Metabolismo basal'" />
                </DxFormItem>
                <DxFormItem data-field="ComplexionFisica" editor-type="dxNumberBox" :editor-options="{min: 1, max: 10, format: '#0', step: 1 }">
                    <DxFormLabel :text="'Complexión física'" />
                </DxFormItem>
                <DxFormItem data-field="EdadMetabolica" editor-type="dxNumberBox" :editor-options="{min: 0, max: 100, format: '#0', step: 1 }">
                    <DxFormLabel :text="'Edad metabólica'" />
                </DxFormItem>
                <DxFormItem data-field="MasaOsea" editor-type="dxNumberBox" :editor-options="{min: 1.95, max: 4, format: '0.0#', step: 0.01 }">
                    <DxFormLabel :text="'Masa ósea'" />
                </DxFormItem>
            </DxFormItem>
        </DxForm>
    </vx-card>
</div>
</template>

<script>
import 'devextreme-vue/lookup'

export default {
    name: 'Signos',
    components: {},
    data() {
        return {
            indiceTab: 0,
            buttons: [{
                name: 'Evoluciones',
                icon: 'arrow-up-right-dots'
            }, {
                name: 'Laboratorios',
                icon: 'vial-circle-check'
            }, {
                name: 'Exámenes',
                icon: 'radiation'
            }, {
                name: 'Encamamiento',
                icon: 'bed'
            }, ],
            tabs: [{
                name: 'Antecedentes personales',
                value: 1,
                icon: "address-card",
            }, {
                name: 'Exámen físico',
                value: 2,
                icon: "person-falling-burst",
            }, {
                name: 'Signos vitales',
                value: 3,
                icon: "heart-pulse",
            }, {
                name: 'Impresión clínica / Diagnóstico',
                value: 4,
                icon: "laptop-medical",
            }, {
                name: 'Referencia',
                value: 5,
                icon: "person-arrow-up-from-line",
            }, {
                name: 'Tratamiento',
                value: 6,
                icon: "pills",
            }, {
                name: 'Lab. ext.',
                value: 7,
                icon: "vial",
            }, {
                name: 'Receta',
                value: 8,
                icon: "file-prescription",
            }, ],

            signosVitales: {
                Libras: 0,
                Onzas: 0,
                Kilos: 0,
                Estatura: 1.0,
                IMC: '',
                DescripcionIMC: '',
                Temperatura: 0,
                Pulso: 0,
                Sistolica: 0,
                Diastolica: 0,
                FrecuenciaRespiratoria: 0,
                CircunferenciaCefalica: 30,
                Grasa: 5,
                Agua: 20,
                GrasaVisceral: 1,
                Musculo: 0.1,
                Metabolismo: 1000,
                ComplexionFisica: 1,
                EdadMetabolica: 9,
                MasaOsea: 1.95
            },
        }
    },
    props: {
        Sexo: null,
        IdCita: null,
        IdCliente: null,
        IdPaciente: null,
        IdMedico: null,
        IdConsulta: null,

        Modulo: null, //Variable para saber desde donde está siendo invocado el componente 1 = Historial | 2 = Evoluciones
    },
    methods: {
        BusquedaSignosVitales() {
            this.axios.post('/app/v1_Historial_Clinico/BusquedaSignosVitales', {
                    IdConsulta: this.IdConsulta,
                })
                .then(resp => {
                    this.signosVitales = resp.data.json[0]
                    //Se convierten los valores a números
                    if (this.signosVitales !== undefined) {
                        this.signosVitales.Metabolismo = parseInt(this.signosVitales.Metabolismo)
                        this.signosVitales.Sistolica = parseInt(this.signosVitales.Sistolica)
                        this.signosVitales.Diastolica = parseInt(this.signosVitales.Diastolica)
                        this.signosVitales.CircunferenciaCefalica = parseInt(this.signosVitales.CircunferenciaCefalica)
                        this.signosVitales.ComplexionFisica = parseInt(this.signosVitales.ComplexionFisica)
                        this.signosVitales.FrecuenciaRespiratoria = parseInt(this.signosVitales.FrecuenciaRespiratoria)
                        this.signosVitales.EdadMetabolica = parseInt(this.signosVitales.EdadMetabolica)
                        this.signosVitales.Pulso = parseInt(this.signosVitales.Pulso)

                        this.signosVitales.Peso = parseFloat(this.signosVitales.Peso)
                        this.signosVitales.Temperatura = parseFloat(this.signosVitales.Temperatura)
                        this.signosVitales.Estatura = parseFloat(this.signosVitales.Estatura)
                        this.signosVitales.MasaOsea = parseFloat(this.signosVitales.MasaOsea)
                        this.signosVitales.Musculo = parseFloat(this.signosVitales.Musculo)
                        this.signosVitales.Grasa = parseFloat(this.signosVitales.Grasa)
                        this.signosVitales.Agua = parseFloat(this.signosVitales.Agua)
                        this.signosVitales.GrasaVisceral = parseFloat(this.signosVitales.GrasaVisceral)

                        this.signosVitales = {
                            ...this.signosVitales,
                            Libras: Math.trunc(this.signosVitales.Peso),
                            Onzas: this.calcularOnzas(this.signosVitales.Peso),
                            Kilos: null,
                            IMC: null,
                            DescripcionIMC: null
                        }

                        this.calcularKilos()
                        this.calcularIMC()
                    } else {
                        this.LimpiarVariables()
                    }

                })
        },
        calcularKilos() {
            let kilos = parseInt(this.signosVitales.Libras ? this.signosVitales.Libras : 0) + parseFloat(this.signosVitales.Onzas ? this.signosVitales.Onzas * 0.0625 : 0)

            this.signosVitales.Peso = kilos
            this.signosVitales.Kilos = kilos * 0.45359237
        },
        calcularIMC() {
            if (this.signosVitales.Kilos !== 0 && this.signosVitales.Estatura !== 0) {
                let IMC = this.signosVitales.Kilos / Math.pow(parseFloat(this.signosVitales.Estatura) / 100, 2)

                if (IMC < 16.1) {
                    this.signosVitales.DescripcionIMC = "Desnutrición en 3er grado"
                } else if (IMC >= 16.1 && IMC < 17) {
                    this.signosVitales.DescripcionIMC = "Desnutrición en 2do grado"
                } else if (IMC >= 17 && IMC < 18.5) {
                    this.signosVitales.DescripcionIMC = "Desnutrición en 1er grado"
                } else if (IMC >= 18.5 && IMC < 20) {
                    this.signosVitales.DescripcionIMC = "Peso insuficiente"
                } else if (IMC >= 20 && IMC < 25) {
                    this.signosVitales.DescripcionIMC = "Normal"
                } else if (IMC >= 25 && IMC < 30) {
                    this.signosVitales.DescripcionIMC = "Sobrepeso"
                } else if (IMC >= 30 && IMC < 35) {
                    this.signosVitales.DescripcionIMC = "Obesidad en 1er grado"
                } else if (IMC >= 35 && IMC < 40) {
                    this.signosVitales.DescripcionIMC = "Obesidad en 2do grado"
                } else if (IMC >= 40) {
                    this.signosVitales.DescripcionIMC = "Obesidad en 3er grado"
                }

                this.signosVitales.IMC = IMC
            }
        },
        calcularOnzas(x) {
            let onzas = parseFloat(x - Math.trunc(x)) / 0.0625
            return onzas
        },
        LimpiarVariables() {
            this.signosVitales = {
                Libras: 0,
                Onzas: 0,
                Kilos: 0,
                Estatura: 1.0,
                IMC: '',
                DescripcionIMC: '',
                Temperatura: 0,
                Pulso: 0,
                Sistolica: 0,
                Diastolica: 0,
                FrecuenciaRespiratoria: 0,
                CircunferenciaCefalica: 30,
                Grasa: 5,
                Agua: 20,
                GrasaVisceral: 1,
                Musculo: 0.1,
                Metabolismo: 1000,
                ComplexionFisica: 1,
                EdadMetabolica: 9,
                MasaOsea: 1.95
            }
        },
        GuadarSignosVitales() {
            this.axios.post('/app/v1_Historial_Clinico/ActualizarSignosVitales', {
                    IdHistorialClinico: this.IdConsulta,
                    Peso: this.signosVitales.Peso,
                    Estatura: this.signosVitales.Estatura,
                    Pulso: this.signosVitales.Pulso,
                    Temperatura: this.signosVitales.Temperatura,
                    Sistolica: this.signosVitales.Sistolica,
                    Diastolica: this.signosVitales.Diastolica,
                    FrecuenciaRespiratoria: this.signosVitales.FrecuenciaRespiratoria,
                    CircunferenciaCefalica: this.signosVitales.CircunferenciaCefalica,
                    Grasa: this.signosVitales.Grasa,
                    GrasaVisceral: this.signosVitales.GrasaVisceral,
                    Agua: this.signosVitales.Agua,
                    Musculo: this.signosVitales.Musculo,
                    Metabolismo: this.signosVitales.Metabolismo,
                    ComplexionFisica: this.signosVitales.ComplexionFisica,
                    EdadMetabolica: this.signosVitales.EdadMetabolica,
                    MasaOsea: this.signosVitales.MasaOsea,
                    IdGeneral: this.signosVitales.IdGeneral
                })
                .then(() => {
                    this.BusquedaSignosVitales()
                })
        }
    },
    mounted() {
        if (this.IdConsulta !== undefined && this.IdConsulta !== '' && this.IdConsulta !== null) {
            this.BusquedaSignosVitales()
        }
    },
    watch: {
        'IdConsulta'(newval) {
            if (newval !== '' && newval !== null && newval !== undefined) {
                this.BusquedaSignosVitales()
            }
        },
    },
    computed: {
        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 3,
                    lg: 3
                };
        },
    },
}
</script>
