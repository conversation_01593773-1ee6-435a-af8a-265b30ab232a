
<template>
    <vx-card title="Cosulta de Pacientes Admisiones RN">
        <div class="container_div p-4">
            <div class="container">
        <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
            <form @submit.prevent="handleSubmit(submitForm())">
                <div class="flex flex-wrap">
                    <vs-row>

                        <vs-col vs-type="flex" vs-w="6">
                            <div class="w-4/5  p-2">                            
                                    <vs-input v-model="consultaPaciente.NombrePaciente" label="Nombre Paciente"
                                        class="w-full" />                                
                            </div>
                            <div class="w-4/5  p-2">                                
                                    <vs-input v-model="consultaPaciente.ApellidoPaciente" label="Apellido Paciente"
                                        class="w-full" />                                
                            </div>
                        </vs-col>

                    </vs-row>

                    <vs-row class="justify-end pr-10">
                        <vs-button @click="handleSubmit(ConsultarPaciente().Consultar())" :disabled="invalid">
                            Consultar
                        </vs-button>
                    </vs-row>
                </div>
            </form>
        </ValidationObserver>
        <vs-table2 max-items="10" search tooltip pagination :data="listaPacientes">
            <template slot="thead">
                <th width="50px" filtro="Serie">Serie</th>
                <th width="50px" filtro="Admision">Admision</th>
                <th width="50px" filtro="Paciente">Paciente</th>
                <th width="50px" filtro="Cliente">Cliente</th>
                <th width="330px" filtro="NombrePaciente">NombrePaciente</th>
                <!-- <th width="20px" filtro="PorcentajeCoincidencia">PorcentajeCoincidencia</th> -->
            </template>

            <template slot-scope="{data}">
                <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" v-on:click="submit(tr)"
                           :class="{ 'activo': tr.activo }">

                    <vs-td2>
                        {{ tr.Serie }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Admision }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.Codigo }}
                    </vs-td2>

                    <vs-td2>
                        {{ tr.IdCliente }}
                    </vs-td2>
    
                    <vs-td2>
                        {{ tr.NombrePaciente }}
                    </vs-td2>
                </tr>
            </template>

        </vs-table2>
            </div>
        </div>
    </vx-card>
</template>


<script>

export default {
    name: "ConsultaPacienteAdmision",
    data() {
        return {
            listaPacientes: [],
            consultaPaciente: {
                Empresa: '',
                NombrePaciente: '',
                NumeroDocumento: ''
            }
        }

    },
    methods: {
        ConsultarPaciente() {
            return {

                Consultar: async () => {

                    const resp = await this.axios.post('/app/v1_admision/ConsultaPacienteAdmision', this.consultaPaciente)

                    this.listaPacientes = resp.data.json.map(m => {
                        return {
                            ...m,

                        }
                    })
                },

            }
        },
        submit(item) {

            this.$store.dispatch('admision/setSerieRaiz', item.Serie)
            this.$store.dispatch('admision/setAdmisionRaiz', item.Admision)
            delete item.Serie;
            delete item.Admision;
            delete item.Nacimiento

            this.$emit("ready", true);

            if(item)
                this.$store.dispatch('paciente/setPaciente', item)
            

        }
    }

};
</script>
<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}
.container_div {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 100%;
  
}
.container_div>div {
    border: 1px solid #888;
    padding-top: 10px;
    padding-bottom: 10px;
}
.container_b {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 10%;
}
.container {
    max-width: 100%;
}
.radio-group.radio-group--inline div[data-radio-button] {
    width: auto;
}
.radio-group.radio-group--inline div[data-radio-button]:not(:first-of-type) {
    padding-left: var(--space-base);
}
</style>