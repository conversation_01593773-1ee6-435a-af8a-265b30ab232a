<template>
    <div v-if="PacienteData.Codigo" class="container">
        <vx-card>

            <div class="container_con ">

                <div  class="container pl-4 mx-auto">

                    <vs-row>

                        <vs-col vs-type="flex" vs-w="10">
                            <div v-if=(PacienteData.Codigo) class="w-full">
                                <strong>
                                    {{
                                        PacienteData.PrimerNombre
                                        + ' '
                                        + PacienteData.SegundoNombre
                                        + ' '
                                        + PacienteData.PrimerApellido
                                        + ' '
                                        + PacienteData.SegundoApellido
                                    }}
                                </strong>
                            </div>
                        </vs-col>
                    </vs-row>
                    <vs-row>
                        <vs-col vs-type="flex" vs-w="4">
                            <div class="w-1/2  ">
                                <strong>{{ PacienteData.DPI }}</strong>
                            </div>
                        </vs-col>

                    </vs-row>
                </div>

            </div>
        </vx-card>

    </div>
</template>
  
<script>

import { mapState } from 'vuex'

export default {
    name: "ConsultaDatosPaciente",
    data: () => ({
    }),
    computed: {
        ...mapState("paciente", ["PacienteData"]),
    },
};


</script>
  
  
<style scoped>
* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

.container_con {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 100px;
    overflow: hidden;
}

.container_con>div {
    border: 1px solid #888;
    padding-top: 2px;
    padding-bottom: 2px;
}

.container_b {
    display: grid;
    grid-gap: 10px;
    width: 100%;
    height: 10%;
}

.vs-input {
    padding-top: 4px;
}

strong {
    font-weight: bold;
    font-size: x-large;
}
</style>
  