<template>
    <vx-card title="Semaforo de Diagnóstico">
    
        <vs-popup ref="rechazar" :title="`Rechazar`" :active.sync="rechazo.mostrar" style="z-index:52000" id="div-with-loading" class="vs-con-loading__container" width="200">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <ValidationProvider name="Comentario" rules="required" class="required">
                    <vs-textarea class="w-full" label="Comentario" counter="200" v-model="rechazo.comentario" />
                </ValidationProvider>
                <div class="flex">
                    <vs-spacer></vs-spacer>
                    <vs-button @click="handleSubmit(Guardar(rechazo.item).Rechazar())" color="danger" :disabled="invalid">Rechazar</vs-button>
                </div>
            </ValidationObserver>
        </vs-popup>
    
        <SM-Tabs height="auto">
            <div class="tab p-4" label="Ordenes Pendientes">
                <h4>Ordenes Pendientes</h4>
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <div class="flex flex-wrap">
                        <div class="w-full">
                            <div class="flex flex-wrap">
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                    <ValidationProvider name="Hospital" rules="required" class="required">
                                        <!--                                    <label class="vs-input--label">Hospital *</label>
                                        <multiselect v-model="info.Empreas" :options="Empresas" :disabled="false" :allow-empty="false" placeholder="Seleccione el Hospital" track-by="Nombre" label="Nombre"></multiselect>--->
                                        <label class="typo__label">Hospital *</label>
                                        <multiselect v-model="cb_hospital" :options="Empresas" :close-on-select="true" :show-labels="false" :custom-label="Hospital_seleccionado" placeholder="Seleccionar Hospital" @input="onChangeHospital">
                                            <span slot="noOptions">Seleccione el Hospital</span>
                                        </multiselect>
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                    <ValidationProvider name="Tipo" rules="required" class="required">
                                        <!--                                    <label class="vs-input--label">Tipo *</label>
                                        <multiselect v-model="info.Tipo" :options="Tipos" :disabled="false" :allow-empty="false" placeholder="Selección de Tipo" track-by="Nombre" label="Nombre"></multiselect>-->
                                        <label class="typo__label">Tipo *</label>
                                        <multiselect v-model="cb_tipo" :options="Tipos" :close-on-select="true" :show-labels="false" :custom-label="Tipo_seleccionado" placeholder="Seleccionar Tipo" @input="onChangeTipo">
                                            <span slot="noOptions">Seleccione el Hospital</span>
                                        </multiselect>
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                    <ValidationProvider name="Fecha Inicial" rules="required" class="required">
                                        <vs-input label="Fecha Inicial" class="w-full" type="date" v-model="info.Inicio" />
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                    <ValidationProvider name="Fecha Final" rules="required" class="required">
                                        <vs-input label="Fecha Final" class="w-full" type="date" v-model="info.Final" />
                                    </ValidationProvider>
                                </div>
    
                                <vs-spacer></vs-spacer>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                    <vs-button @click="handleSubmit(Consulta().actualizar())" :disabled="invalid">
                                        Actualización
                                    </vs-button>
                                </div>
    
                                <!---CHECK SELECCIONAR TODOS -->
    
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                    <vs-checkbox v-model="ActualizacionAutomatica" @change="Consulta().Actualizacion_Automatica()">Actualizar automáticamente cada 5 minutos.</vs-checkbox>
                                </div>
    
                            </div>
                        </div>
                    </div>
                    <br>
                    <hr>
                    <br>
                </ValidationObserver>
    
                <div style="border-radius:5px;padding:5px;font-size:15px;background-color:#A5D6A7">
                    Estimado Usuario si la orden no aparece, por favor presionar el botón "Actualización" o F5 y si no aparece el estudio ya fue informado por el Radiólogo, informar a su jefatura para su productividad.
                </div>
                <vs-table2 max-items="10" search tooltip filter pagination :data="tabla" exportarExcel="validación depositos" noSelectText>
                    <!--<vs-table2 max-items="10" search tooltip filter pagination :data="tabla" noSelectText> -->
                    <template slot="thead">
                        <th width="50px" filtro="tipoorden,orden">Tipo</th>
                        <th width="10px" filtro="Admision">Admisión</th>
                        <th width="300px" filtro="Paciente">Paciente</th>
                        <th width="300px" filtro="nombre">Examen</th>
                        <th width="50px" filtro="Area">Sub área</th>
                        <th width="50px" filtro="status"> Estado Status</th>
                        <th width="50px" filtro="Tipo_Admision">Tipo Admisión</th>
                        <!--<th width="130px">Requerida Fecha</th>
                        <th width="120px">Requerida Hora</th>
    
                        <th width="140px">Estado Retraso</th>
                        <th width="170px">Observaciones</th>
                        <th width="170px">Observaciones Extra</th>
    
                        <th width="170px">Habitación</th>
                        <th width="170px">Ubicación</th>
                        <th width="170px">Fecha Registro</th>
                        <th width="170px">Usuario</th>-->
                    </template>
    
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`" @contextmenu="Menu(tr)" @dblclick="Menu(tr)" @click="Menu(null)" :class="{ 'activo': tr.activo }">
    
                            <vs-td2>
                                {{ tr.tipoorden }} {{ tr.orden }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.Admision }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.Paciente }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.nombre }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Area }}
                            </vs-td2>
                            <vs-td2>
                                <i v-if="tr.status=='Recibido'" style="color:#2ECC71" class="far fa-check-circle"></i>
                                <i v-if="tr.status=='Rechazado'" style="color:#E74C3C" class="far fa-times-circle"></i>
                                <i v-if="tr.status=='Pendiente'" style="color:#95A5A6" class="fas fa-dot-circle"></i>
                                {{ tr.status }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Tipo_Admision }}
                            </vs-td2>
                            <!--  <vs-td2>
                                {{ tr.tipoorden }} {{ tr.orden }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.Area }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.fecha_a_realizar }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.hora.split(' ')[1] }}
                            </vs-td2>
                            <vs-td2>
                                <i v-if="tr.status=='Recibido'" style="color:#2ECC71" class="far fa-check-circle"></i>
                                <i v-if="tr.status=='Rechazado'" style="color:#E74C3C" class="far fa-times-circle"></i>
                                <i v-if="tr.status=='Pendiente'" style="color:#95A5A6" class="fas fa-dot-circle"></i>
                                {{ tr.status }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Horas_retraso }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.observaciones }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.observacionesExtra }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Tipo_Admision }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Habitacion }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Ubicacion }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.fecharegistro }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.usuario }}
                            </vs-td2>-->
    
                            <div class="menu" v-if="tr.activo">
                                <template v-if="tr.status=='Pendiente'">
                                    <div @click="Guardar(tr).Realizar()">
                                        Recibir Estudio
                                    </div>
                                    <!--- <div @click="rechazo.comentario = ''; rechazo.item = tr; rechazo.mostrar = true">--->
                                    <div @click="ValidacionPre_Rechazo(tr)">
                                        Rechazar Estudio
                                    </div>
                                </template>
                                <div v-else>
                                    El estudio ya fue validado
                                </div>
                            </div>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <div class="tab p-4" label="Estado de Ordenes">
                <h4>Estado de Ordenes</h4>
                <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                    <div class="flex flex-wrap">
                        <div class="w-full ">
                            <div class="flex flex-wrap">
                                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                                    <ValidationProvider name="Hospital" rules="required" class="required">
                                        <label class="vs-input--label">Hospital *</label>
                                        <multiselect v-model="infoEstado.Empresa" :options="Empresas" :disabled="false" :allow-empty="false" placeholder="Seleccione el Hospital" track-by="Nombre" label="Nombre"></multiselect>
                                        <!--<label class="typo__label">Hospital *</label>
                                        <multiselect v-model="cb_hospital" :options="Empresas" :close-on-select="true" :show-labels="false" :custom-label="Hospital_seleccionado" placeholder="Seleccionar Hospital" @input="onChangeHospital">
                                            <span slot="noOptions">Seleccione el Hospital</span>
                                        </multiselect>-->
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                                    <ValidationProvider name="Tipo" rules="required" class="required">
                                        <label class="vs-input--label">Tipo *</label>
                                        <multiselect v-model="infoEstado.Tipo" :options="Tipos" :disabled="false" :allow-empty="false" placeholder="Selección de Tipo" track-by="Nombre" label="Nombre"></multiselect>
                                        <!--<label class="typo__label">Tipo *</label>
                                        <multiselect v-model="cb_tipo" :options="Tipos" :close-on-select="true" :show-labels="false" :custom-label="Tipo_seleccionado" placeholder="Seleccionar Tipo" @input="onChangeTipo">
                                            <span slot="noOptions">Seleccione el Hospital</span>
                                        </multiselect>-->
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-2">
                                    <ValidationProvider name="Tipo" rules="required" class="required">
                                        <label class="vs-input--label">Opción *</label>
                                        <multiselect v-model="infoEstado.Opcion" :options="Opciones" :disabled="false" :allow-empty="false" placeholder="Seleccione la Opción" track-by="Nombre" label="Nombre"></multiselect>
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                    <ValidationProvider name="Fecha Inicial" rules="required" class="required">
                                        <vs-input label="Fecha Inicial" class="w-full" type="date" v-model="infoEstado.Inicio" />
                                    </ValidationProvider>
                                </div>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                    <ValidationProvider name="Fecha Final" rules="required" class="required">
                                        <vs-input label="Fecha Final" class="w-full" type="date" v-model="infoEstado.Final" />
                                    </ValidationProvider>
                                </div>
                                <vs-spacer></vs-spacer>
                                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-2">
                                    <vs-button @click="handleSubmit(Consulta().actualizarEstado())" :disabled="invalid">
                                        Actualización
                                    </vs-button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br>
                    <hr>
                    <br>
                </ValidationObserver>
                <vs-table2 max-items="10" search tooltip filter pagination :data="tablaEstado" exportarExcel="Ordenes Pendientes" noSelectText>
                    <template slot="thead">
                        <th width="130px">Tipo</th>
                        <th width="130px">Admisión</th>
                        <th width="300px">Paciente</th>
                        <th width="110px">Orden</th>
                        <th width="300px">Examen</th>
                        <th width="110px">Sub área</th>
                        <th width="130px">Requerida Fecha</th>
                        <th width="120px">Requerida Hora</th>
                        <th width="160px">Estado Status</th>
                        <th width="140px">Estado Retraso</th>
                        <th width="170px">Observaciones</th>
                        <th width="170px">Observaciones Extra</th>
                        <th width="170px">Tipo Admisión</th>
                        <th width="170px">Habitación</th>
                        <th width="170px">Ubicación</th>
                        <th width="170px">Fecha Registro</th>
                        <th width="170px">Usuario</th>
                    </template>
    
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data" :ref="`list_${indextr}`">
    
                            <vs-td2>
                                {{ tr.tipoorden }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.Admision }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.Paciente }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.tipoorden }} {{ tr.orden }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.nombre }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.Area }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.fecha_a_realizar }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.hora.split(' ')[1] }}
                            </vs-td2>
    
                            <vs-td2>
                                {{ tr.status }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Horas_retraso }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.observaciones }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.observacionesExtra }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Tipo_Admision }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Habitacion }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Ubicacion }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.fecharegistro }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.usuario }}
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </SM-Tabs>
    
        <div>
            <!--{{ width: 200px !important;height: auto;}}-->
    
            <vs-popup classContent="popup-example" title="Validar credencial" :active.sync="Band_ValidarCredencial">
    
                <div style="padding:15px; border-radius:5px; box-shadow:0 5px 5px rgba(0,0,0,0.5);margin-bottom:20px">
    
                    <form>
    
                        <div style="margin: 15px" class="flex flex-wrap">
                            <div class="w-full md:w-1/2 lg:w1/2 xl:w-1/2">
                                <ValidationProvider name="corporativo" rules="required|max:40" v-slot="{ errors }" class="required">
                                    <vs-input v-on:blur="BusquedaPuesto" label="Corporativo" class="w-full" count="40" v-model="Vl_Corporativo" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
    
                            </div>
                            <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2">
                                <ValidationProvider name="Contraseña" rules="required|max:40" v-slot="{ errors }" class="required">
                                    <vs-input type="password" ref="password" class="w-full mb-4" label="Contraseña" count="12" v-model="Vl_Clave" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" />
                                </ValidationProvider>
                            </div>
                        </div>
    
                        <div v-if="ExistePuesto==false" style="border-radius:5px;padding:5px;font-size:15px;background-color:#E0884D;color:white">
                            Corporativo no contiene Puesto / Pago productividad configurado.
                        </div>
    
                        <vs-button color="primary" style="float:right" type="filled" icon-pack="feather" icon="icon-save" @click="ConsultarCorporativo()"> Aceptar</vs-button>
                        <vs-button color="danger" style="float:right" type="border" icon-pack="feather" icon="icon-x" @click="Band_ValidarCredencial=false;ExistePuesto = true;ItemSeleccionado = '';"> Cancelar</vs-button>
                        <vs-divider></vs-divider>
    
                    </form>
                </div>
            </vs-popup>
        </div>
    </vx-card>
    </template>
    
    <script>
    // import VueApexCharts from 'vue-apexcharts'
    import moment from 'moment';
    import Multiselect from "vue-multiselect";
    import "vue-multiselect/dist/vue-multiselect.min.css";
    export default {
        data() {
            return {
    
                ActualizacionAutomatica: true,
                Band_ValidarCredencial: false,
                Vl_Corporativo: '',
                Vl_Clave: '',
                ExistePuesto: true,
                NombreCorporativo: '',
                ItemSeleccionado: '',
                estudios: [{
                    valor: 0,
                    color: '#E74C3C',
                    title: 'Emergencia / Intensivo'
                }, {
                    valor: 0,
                    color: '#F39C12',
                    title: 'Hospitalización'
                }, {
                    valor: 0,
                    color: '#27AE60',
                    title: 'Consulta Externa'
                }],
                estudiosFinalizados: 0,
    
                temp: null,
    
                Tipos: [{
                        Codigo: 'L',
                        Nombre: 'LABORATORIO'
                    },
                    {
                        Codigo: 'D',
                        Nombre: 'DIAGNÓSTICO'
                    }
                ],
                Opciones: [{
                        Codigo: '1',
                        Nombre: 'INFORMADO'
                    },
                    {
                        Codigo: '0',
                        Nombre: 'NO INFORMADO'
                    }
                ],
                Empresas: [],
    
                info: {
                    Empreas: null,
                    Tipo: null,
                    Inicio: null,
                    Final: null
                },
                infoEstado: {
                    Empreas: null,
                    Tipo: null,
                    Opcion: null,
                    Inicio: null,
                    Final: null
                },
    
                rechazo: {
                    mostrar: false,
                    item: null,
                    comentario: null
                },
                tabla: [],
                tablaEstado: [],
                id_hospital_selec: null,
                cb_hospital: '',
    
                cb_tipo: '',
                id_Tipo_selec: null,
    
                //info.Empresa.Codigo
    
            }
        },
        components: {
            Multiselect,
        },
        computed: {
            sesion() {
                return this.$store.state.sesion
            }
        },
        methods: {
            fecha_hora_local() {
                var today = new Date()
                var now_date = (today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate());
                var now_time = (today.getHours() + ":" + today.getMinutes() + ":" + today.getSeconds()).toString()
                return now_date + ' ' + now_time;
    
            },
            ClickEstado() {
                
                this.ActualizacionAutomatica = false;
            },
            Hospital_seleccionado({
                Nombre
            }) {
                return ` ${Nombre} `;
            },
            onChangeHospital(value) {
                if (value !== null && value.length !== 0) {
                    this.id_hospital_selec = value.Codigo;
    
                } else {
                    this.id_hospital_selec = null;
                }
            },
    
            Tipo_seleccionado({
                Nombre
            }) {
                return ` ${Nombre} `;
            },
            onChangeTipo(value) {
                if (value !== null && value.length !== 0) {
                    this.id_Tipo_selec = value.Codigo;
    
                } else {
                    this.id_Tipo_selec = null;
                }
            },
            ObtenerFecha() {
                let b = new Date(),
                    c = b.getHours(),
                    d = b.getMinutes(),
                    e = b.getSeconds(),
                    h = b.getDay(),
                    i = 12 <= c ? 'PM' : 'AM',
                    j = b.getMonth(),
                    k = b.getDate(),
                    l = b.getFullYear();
                
                this.info.Inicio = moment(b).format('YYYY-MM-DD')
                this.info.Final = moment(b).format('YYYY-MM-DD')
    
                this.infoEstado.Inicio = moment(b).format('YYYY-MM-DD')
                this.infoEstado.Final = moment(b).format('YYYY-MM-DD')

            },
            Consulta() {
                return {
                    consultaEmpresa: () => {
                        this.axios.post('/app/administracion/Busqueda_Empresa', {})
                            .then(resp => {
                                this.Empresas = resp.data.json
    
                            })
                    },
                    Actualizacion_Automatica: () => {
    
                        if (this.ActualizacionAutomatica == true && this.id_hospital_selec != null && this.id_Tipo_selec) {
    
                            this.Consulta().listado();
                            setTimeout(() => {
                                this.Consulta().Actualizacion_Automatica();
                            //}, 5000)
                            }, 300000)
                        }
    
                    },
                    actualizar: () => {
                        // const intervalo = 1000 * 60 * 5 // 5 minutos
                        // this.Consulta().contadores()
                        this.Consulta().listado()
                    },
                    actualizarEstado: () => {
                        this.ActualizacionAutomatica = false;
                        this.Consulta().listadoEstado()
                    },
                    listado: () => {
                        return this.axios.post('/app/admision/BusquedaAdmisiones', {
                                /*                            "Hospital": this.info.Empreas.Codigo,
                                    "Area": this.info.Tipo.Codigo, */
                                "Hospital": this.id_hospital_selec,
                                "Area": this.id_Tipo_selec,
                                "FechaInicio": this.info.Inicio,
                                "FechaFinal": this.info.Final
                            })
                            .then(resp => {
                                this.tabla = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        activo: false
                                    }
                                })
                            })
                    },
                    listadoEstado: () => {
                        return this.axios.post('/app/admision/BusquedaAdmisionesEstado', {
                                //"Hospital": this.id_hospital_selec,
                                "Hospital": this.infoEstado.Empresa.Codigo,
                                "Area": this.infoEstado.Tipo.Codigo,
                                "Informado": this.infoEstado.Opcion.Codigo,
                                "FechaInicio": this.infoEstado.Inicio,
                                "FechaFinal": this.infoEstado.Final
                            })
                            .then(resp => {
                                this.tablaEstado = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        activo: false
                                    }
                                })
                            })
                    },
                    contadores: () => {
                        this.axios.post('/app/admision/Obtenercontadores', {
                                "Hospital": this.info.Empresa.Codigo
                            })
                            .then(resp => {
                                const r = resp.data.json[0]
                                this.estudiosFinalizados = r.Finalizados
                                this.estudios[0].valor = r.Emergencias
                                this.estudios[1].valor = r.Hospitalizacion
                                this.estudios[2].valor = r.COEX
                                // this.tabla = resp.data.json
                            })
                    }
                }
            },
            Guardar(item) {
                return {
                    Realizar: () => {
                        const sesion = this.$store.state.sesion;
    
                        this.axios.post('/app/admision/validarEstado', {
                                "TipoOrden": item.tipoorden,
                                "Orden": item.orden,
                                "Producto": item.codigo,
                                "Linea": item.Linea
                            })
                            .then(resp => {
                                if (resp.data.codigo == -1) {
                                    this.Consulta().listado()
                                    return;
                                } else {
    
                                    if (item.ValorTablaProducto.trim() != '' && item.ValorTablaProducto != null) {
    
                                        this.Band_ValidarCredencial = true;
                                        this.ExistePuesto = true;
                                        this.ItemSeleccionado = item;
                                        this.Vl_Corporativo = '';
                                        this.Vl_Clave = '';
                                    } else {
    
                                        this.axios.post('/app/bitacora/registro_bitacora', {
                                            "info": "{\"info\":{\"TecnicoRealizo\" :" + "0" + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + item.tipoorden + "\", \" Orden\" :\"" + item.orden + "\" ,\" Producto\" :\"" + item.codigo + "\" ,\" Linea\" :\"" + item.Linea + "\"  \" Observaciones\" :\"" + "**INICIO REGISTRO SIN CORPORATIVO** " + "\", \"Aplicativo\" :\"WEB\"  }}"
    
                                        })
                                        var observacionesRecibido = "Recibido por " + sesion.corporativo + ' Fecha: '
                                        observacionesRecibido = observacionesRecibido + this.fecha_hora_local();
    
                                        this.axios.post('/app/admision/ActualizarOrdenDetalle', {
                                                "Estado": "R",
                                                "TipoOrden": item.tipoorden,
                                                "Orden": item.orden,
                                                "Producto": item.codigo,
                                                "Linea": item.Linea,
                                                "Observaciones": observacionesRecibido
    
                                            })
                                            .then(() => {
    
                                                this.axios.post('/app/bitacora/registro_bitacora', {
                                                        "info": "{\"info\":{\"TecnicoRealizo\" :" + "0" + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + item.tipoorden + "\", \" Orden\" :\"" + item.orden + "\" ,\" Producto\" :\"" + item.codigo + "\" ,\" Linea\" :\"" + item.Linea + "\"  \" Observaciones\" :\"" + "**MENSAJE AUTOMATICO: NO VALIDADO POR CORPORATIVO** " + "\", \"Aplicativo\" :\"WEB\"  }}"
    
                                                    })
                                                    .then(() => {
                                                        this.Consulta().actualizar()
                                                    })
                                            })
                                    }
                                }
                            }).catch(() => {
                                this.Consulta().listado()
                            })
    
                    },
                    Rechazar: () => {
    
                        this.axios.post('/app/admision/validarEstado', {
                                "TipoOrden": item.tipoorden,
                                "Orden": item.orden,
                                "Producto": item.codigo,
                                "Linea": item.Linea
                            })
                            .then(resp => {
                                if (resp.data.codigo == -1) {
                                    this.Consulta().listado()
                                    return;
                                } else {
    
                                    const sesion = this.$store.state.sesion;
                                    this.axios.post('/app/bitacora/registro_bitacora', {
                                        "info": "{\"info\":{\"TecnicoRechazo\" :" + sesion.corporativo + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + item.tipoorden + "\", \" Orden\" :\"" + item.orden + "\" ,\" Producto\" :\"" + item.codigo + "\" ,\" Linea\" :\"" + item.Linea + "\"  \" Observaciones\" :\"" + "**INICIO  REGISTRO RECHAZO** " + "\", \"Aplicativo\" :\"WEB\"  }}"
    
                                    })
    
                                    this.axios.post('/app/admision/ActualizarOrdenDetalle', {
                                            "Estado": "A",
                                            "TipoOrden": item.tipoorden,
                                            "Orden": item.orden,
                                            "Producto": item.codigo,
                                            "Linea": item.Linea,
                                            "Observaciones": this.rechazo.comentario
                                        })
                                        .then(() => {
    
                                            this.axios.post('/app/bitacora/registro_bitacora', {
                                                    "info": "{\"info\":{\"TecnicoRechazo\" :" + sesion.corporativo + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + item.tipoorden + "\", \" Orden\" :\"" + item.orden + "\" ,\" Producto\" :\"" + item.codigo + "\" ,\" Linea\" :\"" + item.Linea + "\"  \" Observaciones\" :\"" + this.rechazo.comentario + "\", \"Aplicativo\" :\"WEB\"  }}"
    
                                                })
                                                .then(() => {
                                                    this.Consulta().actualizar()
                                                    this.rechazo.mostrar = false
                                                    this.rechazo.comentario = ''
                                                })
                                        })
                                }
                            }).catch(() => {
                                this.Consulta().listado()
                            })
                    }
                }
            },
            Touch() {
                return {
                    start: (item) => {
                        this.temp = item
                    },
                    end: () => {
                        this.temp = 2
                    }
                }
            },
            Menu(item) {
                // console.log(item)
                this.tabla.map(m => m.activo = false)
                if (item) item.activo = true
            },
            ConsultarCorporativo() {
                if ((this.Vl_Corporativo == null || this.Vl_Corporativo == '') || (this.Vl_Clave == null || this.Vl_Clave == '')) {
                    this.$vs.notify({
                        color: '#FFAB00',
                        title: 'Pago Productividad',
                        text: "Ingresar credenciales.",
                    });
                    return;
                }
                this.axios.post('/app/usuario/verificar', {
                        corporativo: this.Vl_Corporativo,
                        password: this.Vl_Clave,
                        show: true
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0) {
                            
                            this.GuardarOrden();
                        }
                    })
    
            },
            BusquedaPuesto() {
    
                if ((this.Vl_Corporativo == null || this.Vl_Corporativo == '')) {
                    return;
                }
                this.axios.post('/app/admision/validarPuesto', {
                        corporativo: this.Vl_Corporativo
                    })
                    .then(resp => {
    
                        if (resp.data.codigo == 0) {
                            resp.data.json.map(data => {
                                if (data.Existe == 1) {
                                    this.ExistePuesto = true;
                                } else if (data.Existe == 0) {
                                    this.ExistePuesto = false;
                                }
                            })
                        }
                    })
            },
            GuardarOrden() {
                var corporativoRecibe = 0;
                var MensajeAumatico = '';
                const sesion = this.$store.state.sesion;
                if (this.ExistePuesto) {
                    corporativoRecibe = this.Vl_Corporativo;
                    MensajeAumatico = '**MENSAJE AUTOMATICO** INICIO REGISTRO';
                } else {
                    MensajeAumatico = '**MENSAJE AUTOMATICO** EL TECNICO REALIZO:' + this.Vl_Corporativo + ' NO CONTIENE PUESTO / PAGO PRODUCTIVIDAD CONFIGURADO';
                }
                var observacionesRecibido = "Recibido por " + sesion.corporativo + ' Fecha: '
                observacionesRecibido = observacionesRecibido + this.fecha_hora_local();
    
                this.axios.post('/app/bitacora/registro_bitacora', {
                        "info": "{\"info\":{\"TecnicoRealizo\" :" + corporativoRecibe + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + this.ItemSeleccionado.tipoorden + "\", \" Orden\" :\"" + this.ItemSeleccionado.orden + "\" ,\" Producto\" :\"" + this.ItemSeleccionado.codigo + "\" ,\" Linea\" :\"" + this.ItemSeleccionado.Linea + "\"  \" Observaciones\" :\"" + MensajeAumatico + "\", \"Aplicativo\" :\"WEB\"  }}"
                    })
                    .then(() => {
    
                        return this.axios.post('/app/admision/ActualizarOrdenDetalle', {
                                "Estado": "R",
                                "TipoOrden": this.ItemSeleccionado.tipoorden,
                                "Orden": this.ItemSeleccionado.orden,
                                "Producto": this.ItemSeleccionado.codigo,
                                "Linea": this.ItemSeleccionado.Linea,
                                "RevisionTecnico": corporativoRecibe,
                                "Observaciones": observacionesRecibido
                            })
                            .then(() => {
    
                                this.axios.post('/app/bitacora/registro_bitacora', {
                                        "info": "{\"info\":{\"TecnicoRealizo\" :" + corporativoRecibe + ",\" UsuarioSesion\" :" + sesion.corporativo + " ,\"TipoOrden\" :\"" + this.ItemSeleccionado.tipoorden + "\", \" Orden\" :\"" + this.ItemSeleccionado.orden + "\" ,\" Producto\" :\"" + this.ItemSeleccionado.codigo + "\" ,\" Linea\" :\"" + this.ItemSeleccionado.Linea + "\"  \" Observaciones\" :\"" + "**MENSAJE AUTOMATICO: FINALIZA REGISTRO  ** " + "\", \"Aplicativo\" :\"WEB\"  }}"
    
                                    })
                                    .then(() => {
                                        this.Consulta().actualizar()
                                        this.Band_ValidarCredencial = false;
                                        this.ExistePuesto = true;
                                        this.ItemSeleccionado = '';
    
                                    })
    
                            })
                    })
    
            },
            ValidacionPre_Rechazo(item) {
                this.axios.post('/app/admision/validarEstado', {
                        "TipoOrden": item.tipoorden,
                        "Orden": item.orden,
                        "Producto": item.codigo,
                        "Linea": item.Linea
                    })
                    .then(resp => {
                        if (resp.data.codigo == 0) {
                            this.rechazo.comentario = '';
                            this.rechazo.item = item;
                            this.rechazo.mostrar = true
                        } else {
                            this.Consulta().listado()
                            return;
                        }
                    }).catch(() => {
                        this.Consulta().listado()
                    })
            }
    
        },
        mounted() {
    
            this.Consulta().consultaEmpresa()
            this.ObtenerFecha();
    
            //this.Consulta().Actualizacion_Automatica();
        },
        
        watch: {
            'ActualizacionAutomatica'(value) {
    
                this.Consulta().Actualizacion_Automatica();
    
            },
            'id_hospital_selec'(value) {
    
                this.Consulta().Actualizacion_Automatica();
    
            },
            'id_Tipo_selec'(value) {
                this.Consulta().Actualizacion_Automatica();
    
            },
    
        }
    
    }
    </script>
    
    <style scoped>
    .semaforo {
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 2px
            /* margin-left: 5px; */
    }
    
    .semaforo .valor {
        padding: 10px;
        font-size: 50px;
        text-align: center;
        color: white;
        border-radius: 5px;
    }
    
    .semaforo .titulo {
        font-weight: bold;
        color: black;
        text-align: center;
        padding: 5px;
        font-size: 15px;
        flex-wrap: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        /* margin-top: 5px */
    }
    
    tr.activo {
        border: 5px solid #27AE60
    }
    
    .menu {
        background-color: white;
        border: 1px solid #ccc;
        padding: 10px;
        position: absolute;
        left: 50px;
        z-index: 1;
        left: 50%;
        border-radius: 10px;
        box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.7);
    
    }
    
    .menu div {
        padding: 10px 45px;
        cursor: pointer
    }
    
    .menu div:hover {
        background-color: #ccc;
    }
    </style>
    