<template>
<div class="ContenidoBancos">
    <form @submit="handleSubmit">
        <DxForm :ref="formAnticipo" :form-data.sync="formulario" label-mode="floating" @field-data-changed="HabilitarBoton">
            <DxFormGroupItem :col-count-by-screen="colCountByScreen">
                <DxFormItem data-field="Cuenta" editor-type="dxSelectBox" :editor-options="{ width: 'auto', searchEnabled: true, items: cuentas,  displayExpr: 'Valor', valueExpr: 'Codigo', onValueChanged: AsignarSiguienteCheque }" :validationRules="[{ type: 'required' }]" />
                <DxFormItem data-field="SiguienteCheque" editor-type="dxNumberBox" :editor-options="{ min: 0, format: '#0', step: 0 }">
                    <DxFormLabel text="Número" />
                </DxFormItem>
                <DxFormItem data-field="Funcion" editor-type="dxRadioGroup" :editor-options="{ items: funciones, displayExpr:'text', valueExpr: 'id', layout:'horizontal' }">
                    <DxFormLabel text="Función" />
                </DxFormItem>
                <DxFormItem data-field="TipoAnticipo" editor-type="dxSelectBox" :editor-options="{ items: tiposAnticipo, displayExpr:'Descripcion', valueExpr: 'Codigo', onItemClick: AsignarAnticipo  }">
                    <DxFormLabel text="Tipo de anticipo" />
                </DxFormItem>
            </DxFormGroupItem>
            <DxFormGroupItem :col-count-by-screen="colCountByScreen" caption="Información orden">
                <DxFormGroupItem>
                    <DxFormGroupItem :col-count="2" css-class="buttonsOrdenes">
                        <DxFormItem data-field="NumeroOrden" editor-type="dxNumberBox" :editor-options="{ readOnly: notaDebito }">
                            <DxFormLabel text="Número de orden" />
                        </DxFormItem>
                        <!-- <DxButtonItem :button-options="buttonOrdenes" name="Buscar" horizontal-alignment="center" verical-alignment="center" /> -->
                        <DxFormItem :button-options="buttonOrdenes" name="Buscar" :visible="false/*!notaDebito*/" :editor-options="{ disabled: notaDebito }" item-type="button" horizontal-alignment="center" verical-alignment="center" />
                    </DxFormGroupItem>
                    <DxFormGroupItem :col-count=2>
                        <DxFormItem data-field="AutorizadoPor" editor-type="dxTextBox" :editor-options="{ readOnly: true }">
                            <DxFormLabel text="Autorizado por" />
                        </DxFormItem>
                        <DxFormItem data-field="FechaAutorizacion" editor-type="dxDateBox" :editor-options="{ readOnly: true, dataType: 'date', displayFormat: 'dd/MM/yyyy' }">
                            <DxFormLabel text="Fecha" />
                        </DxFormItem>
                    </DxFormGroupItem>
                </DxFormGroupItem>
                <DxFormGroupItem>
                    <DxFormItem data-field="Proveedor" editor-type="dxSelectBox" :editor-options="{ readOnly: this.formulario.Funcion === 'CH' ? true : false, dataSource: proveedoresDataSource,  showCancelButton: false, displayExpr: 'Display', valueExpr: 'Codigo', searchEnabled: true, showClearButton: true, deferRendering: true, onItemClick: SeleccionarProveedor }" />
                    <DxFormItem data-field="CuentaBi" editor-type="dxNumberBox" :visible="formulario.Funcion == 'NA'" :editor-options="{ readOnly: true }">
                        <DxFormLabel text="Cuenta bancaria del Bi" />
                    </DxFormItem>
                </DxFormGroupItem>
            </DxFormGroupItem>

            <DxFormGroupItem :col-count="2">
                <DxFormGroupItem css-class="buttonsOrdenes">
                    <DxFormItem data-field="PagarNombre" editor-type="dxTextBox" :editor-options="{ readOnly: !((Boolean(this.formulario.NumeroOrden) && this.formulario.Funcion == 'CH') || ((this.formulario.Funcion == 'ND' || this.formulario.Funcion == 'NA') && this.formulario.Proveedor !== null && this.formulario.Proveedor !== '')) }">
                        <DxFormLabel text="Pagar a la orden de" />
                    </DxFormItem>
                    <DxFormGroupItem :col-count="2">
                        <DxFormItem data-field="Monto" editor-type="dxNumberBox" :editor-options="{ format: '#0.00', step: 0.01, readOnly: !((Boolean(this.formulario.NumeroOrden) && this.formulario.Funcion == 'CH') || ((this.formulario.Funcion == 'ND' || this.formulario.Funcion == 'NA') && this.formulario.Proveedor !== null && this.formulario.Proveedor !== '')) }" />
                        <DxFormItem data-field="AplicarRetencion" editor-type="dxCheckBox" :editor-options="{ disabled: !((Boolean(this.formulario.NumeroOrden) && this.formulario.Funcion == 'CH') || ((this.formulario.Funcion == 'ND' || this.formulario.Funcion == 'NA') && this.formulario.Proveedor !== null && this.formulario.Proveedor !== '')) }">
                            <DxFormLabel text="Retención" />
                        </DxFormItem>
                    </DxFormGroupItem>
                </DxFormGroupItem>
                <DxFormGroupItem>
                    <DxFormItem data-field="Observaciones" editor-type="dxTextArea" :editor-options="{ height: '100%', readOnly: !((Boolean(this.formulario.NumeroOrden) && this.formulario.Funcion == 'CH') || ((this.formulario.Funcion == 'ND' || this.formulario.Funcion == 'NA') && this.formulario.Proveedor !== null && this.formulario.Proveedor !== '')) }" />
                </DxFormGroupItem>
            </DxFormGroupItem>
            <DxFormButtonItem :button-options="buttonGrabar" name="Grabar" horizontal-alignment="center" verical-alignment="center" />
        </DxForm>
    </form>

    <DxDataGrid :ref="gridDetalle" v-bind="DefaultDxGridConfiguration" :data-source="valoresTabla" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" height="auto" @row-dbl-click="SeleccionarOrden">
        <DxDataGridSelection mode="single" />

        <DxDataGridColumn width="10%" data-field="Documento" alignment="center" data-type="number" />
        <DxDataGridColumn width="30%" data-field="Detalle" alignment="center" data-type="string" />
        <DxDataGridColumn width="10%" data-field="Cuenta" alignment="center" data-type="string" />
        <DxDataGridColumn width="30%" data-field="Nombre" caption="Nombre de la cuenta" alignment="center" data-type="string" />
        <DxDataGridColumn width="10%" data-field="Debe" alignment="center" data-type="number" :customize-text="customizeTextValores" />
        <DxDataGridColumn width="10%" data-field="Haber" alignment="center" data-type="number" :customize-text="customizeTextValores" />
        <DxDataGridSummary>
            <DxDataGridTotalItem column="Debe" summary-type="sum" :customize-text="customizeTextValores" :value-format="{ type: 'fixedPoint', precision: 2 }" />
            <DxDataGridTotalItem column="Haber" summary-type="sum" :customize-text="customizeTextValores" />
        </DxDataGridSummary>
    </DxDataGrid>

    <!-- Módal de órdenes -->
    <DxPopup :wrapper-attr="popupAttributes" :visible.sync="mostrarModalOrdenes" :width="'80%'" height="auto" :show-title="true" :full-screen="false" :hide-on-outside-click="false" title="Ordenes" :showCloseButton="true">
        <form @submit="handleSubmit" class="mb-4">
            <DxForm :ref="formOrdenBuscar" :form-data.sync="ordenesBusqueda" label-mode="hidden">
                <DxFormGroupItem>
                    <DxFormItem data-field="TipoBusqueda" editor-type="dxRadioGroup" :editor-options="{ items: busquedaOrdenes, displayExpr:'text', valueExpr: 'id', layout:'horizontal' }">
                        <DxFormLabel text="" />
                    </DxFormItem>
                </DxFormGroupItem>
                <DxFormGroupItem :col-count="2" css-class="buttonsOrdenes">
                    <DxFormItem data-field="ValorBusqueda" editor-type="dxTextBox" />
                    <DxFormButtonItem :button-options="buttonBuscarOrden" horizontal-alignment="center" verical-alignment="center" />
                </DxFormGroupItem>
            </DxForm>
        </form>

        <DxDataGrid :ref="gridOrdenes" v-bind="DefaultDxGridConfiguration" :data-source="ordenes" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" height="auto" @row-dbl-click="SeleccionarOrden">
            <DxDataGridSelection mode="single" />

            <DxDataGridColumn width="auto" data-field="Codigo" caption="Código orden" alignment="center" data-type="number" />
            <DxDataGridColumn width="auto" data-field="Proveedor" caption="Código proveedor" alignment="center" data-type="string" />
            <DxDataGridColumn width="auto" data-field="Nit" caption="Nit proveedor" alignment="center" data-type="string" />
            <DxDataGridColumn width="auto" data-field="Nombre" alignment="center" data-type="string" />
        </DxDataGrid>
    </DxPopup>

    <!-- Módal de devoluciones -->
    <DxPopup :wrapper-attr="popupAttributes" :visible.sync="mostrarModalDevoluciones" :width="'80%'" height="auto" :show-title="true" :full-screen="false" :hide-on-outside-click="false" :title="tipoDevolucion == 9 ? 'Detalle de facturas' : tipoDevolucion == 10 ? 'Detalle de recibos' : ''" :showCloseButton="true">
        <form @submit="handleSubmit" class="mb-4">
            <DxForm :ref="formDevoluciones" :form-data.sync="devolucionesBusqueda" label-mode="floating">
                <DxFormGroupItem :col-count="4" css-class="buttonsOrdenes">
                    <DxFormItem data-field="SerieDevolucion" name="Serie" editor-type="dxTextBox">
                        <DxFormLabel text="Serie" />
                    </DxFormItem>
                    <DxFormItem data-field="CodigoDevolucion" editor-type="dxNumberBox" :editor-options="{ format: '#0', step: 0 }">
                        <DxFormLabel text="Documento" />
                    </DxFormItem>
                    <DxFormItem data-field="Valor" editor-type="dxNumberBox" :editor-options="{ format: '#0.00'}" />

                    <DxFormButtonItem :button-options="buttonBuscarDevolucion" horizontal-alignment="center" verical-alignment="center" :visible="formDevoluciones.SerieDevolucion && formDevoluciones.CodigoDevolucion && formDevoluciones.Valor" />
                </DxFormGroupItem>
            </DxForm>
        </form>

        <DxDataGrid :ref="gridDevoluciones" v-bind="DefaultDxGridConfiguration" :data-source="devoluciones" :searchPanel="{visible: false}" :headerFilter="{visible:false, allowSearch:false}" :width="'100%'" height="auto" @editor-preparing="editorDatagrid">
            <DxDataGridSelection mode="single" />

            <DxDataGridEditing :allow-updating="true" :allow-adding="false" :allow-deleting="true" mode="cell" :use-icons="true" />

            <DxDataGridColumn width="10%" data-field="Serie" alignment="center" data-type="string" />
            <DxDataGridColumn width="10%" data-field="Documento" alignment="center" data-type="number" />
            <DxDataGridColumn width="10%" data-field="Valor" alignment="center" data-type="number" />
            <DxDataGridColumn width="10%" data-field="TipoDoc" caption="Tipo documento" alignment="center" data-type="string" />
            <DxDataGridColumn width="40%" data-field="Nombre" alignment="center" data-type="string" />
            <DxDataGridColumn width="10%" data-field="SerieAdmision" caption="Serie admisión" alignment="center" data-type="string" />
            <DxDataGridColumn width="10%" data-field="Admision" caption="Admisión" alignment="center" data-type="string" />
        </DxDataGrid>
    </DxPopup>

</div>
</template>

<script>
import {
    DefaultDxGridConfiguration,
    customizeTextValores
} from './data'
import 'devextreme-vue/lookup'

import 'devextreme-vue/text-area'

const gridOrdenes = 'gridOrdenes'
const gridDetalle = 'gridDetalle'
const formOrdenBuscar = 'formOrdenBuscar'
const formDevoluciones = 'formDevoluciones'
const formAnticipo = 'formAnticipo'
const gridDevoluciones = 'gridDevoluciones'

export default {
    name: 'Anticipos',
    components: {},
    data() {
        return {
            DefaultDxGridConfiguration,
            gridOrdenes,
            gridDetalle,
            formOrdenBuscar,
            formDevoluciones,
            formAnticipo,
            gridDevoluciones,

            formulario: {
                Cuenta: null, // Codigo de la cuenta de banco de 2 dígitos
                NumeroCuenta: null, // Número de la cuenta bancaria
                BancoContabilidad: null, // Cuenta contable de la cuenta bancaria seleccionada
                SiguienteCheque: null, // Siguiente cheque de la cuenta bancaria
                NombreCuentaBanco: null, // Nombre de la cuenta contable de la cuenta bancaria 
                Funcion: 'CH', // CH = Cheque / ND = Nota de débido / NA = Acreditamiento
                TipoAnticipo: null, // Codigo del tipo de anticipo seleccionado
                AnticipoContabilidad: null, // Cuenta contable del la cuenta seleccionada para 
                NombreAnticipo: null, // Nombre del tipo de anticipo seleccioando
                NumeroOrden: null, // Número de orden ingresado
                AutorizadoPor: null, // Nombre de la persona que autorizó la orden
                FechaAutorizacion: null, // Fecha en que se autorizó la orden
                NombreProveedor: null, // Nombre del proveedor
                Proveedor: null, // Código del proveedor
                CuentaRetencion: null, // Cuenta de retención del proveedor
                CuentaBancoProveedor: null, // Cuenta bancaria que tiene registrada el proveedor
                ContaAnticipoProveedor: null, // Cuenta para asignar el anticipo al proveedor
                PagarNombre: null, // Beneficiario del cheque     
                Monto: 0, // Monto de la orden, nota de crédito o acreditamiento
                MontoMaximo: null, // Monto de la orden, sirve para validar que no se ingrese un montó mayor al monto de la orden
                AplicarRetencion: false, // Indica si el proveedor tiene retención
                TipoRetencion: null, // Tipo de retención que tiene el proveedor
                Retencion: 0, // Porcentaje de retención
                Observaciones: null, // Observaciones del cheque, nota de crédito o acreditamiento
                TipoValidacion: null, // Tipo validación
                NombreNotaDebito: null, // Guardar el nombre de la cuenta para las notas de débito
                SiguienteNota: null, // Valor de la siguiente nota de acreditamiento
                CuentaBi: null // Numero de cuenta del Bi, para acreditamiento
            },
            cuentas: [],

            dropDownOptions: {
                showTitle: false,
                hideOnOutsideClick: true,
            },

            popupAttributes: {
                class: 'ContenidoBancos'
            },

            funciones: [{
                id: 'CH',
                text: 'Cheque'
            }, {
                id: 'ND',
                text: 'Nota de débito'
            }, ],

            tiposAnticipo: [],

            buttonOrdenes: {
                width: 'auto',
                icon: 'fas fa-magnifying-glass',
                text: 'Buscar orden',
                type: 'default',
                onClick: () => {
                    if (this.formulario.Funcion == 'CH') {
                        this.mostrarModalOrdenes = true
                    }
                },
                useSubmitBehavior: false,
            },

            buttonGrabar: {
                width: 'auto',
                icon: 'fas fa-save',
                text: 'Grabar',
                type: 'success',
                onClick: () => {
                    this.Grabar()
                },
                useSubmitBehavior: false,
            },

            buttonBuscarOrden: {
                width: 'auto',
                icon: 'fas fa-magnifying-glass',
                text: 'Buscar orden',
                type: 'success',
                useSubmitBehavior: false,
                onClick: () => {
                    this.BuscarOrdenes()
                }
            },

            buttonBuscarDevolucion: {
                width: 'auto',
                icon: 'fas fa-magnifying-glass',
                text: 'Buscar',
                type: 'success',
                useSubmitBehavior: false,
                onClick: () => {
                    if (this.tipoDevolucion == 9) {
                        this.ValidaFactura()
                    } else if (this.tipoDevolucion == 10) {
                        this.BuscarDevolucion(this.tipoDevolucion, this.devolucionesBusqueda.SerieDevolucion, this.devolucionesBusqueda.CodigoDevolucion)
                    }
                }
            },

            mostrarModalOrdenes: false,
            mostrarModalDevoluciones: false,

            ordenesBusqueda: {
                TipoBusqueda: 1,
                ValorBusqueda: null
            },

            devolucionesBusqueda: {
                SerieDevolucion: null,
                CodigoDevolucion: null,
                SerieCorta: null,
                CodigoCorta: null,
                Valor: null,
                TipoDocumento: null,
                Nombre: null,
                SerieAdmision: null,
                Admision: null
            },

            ordenes: [],
            devoluciones: [],

            tipoDevolucion: null, // Variable que indica si se buscará una factura o un recibo para la devolución

            notaDebito: false,

            busquedaOrdenes: [{
                id: 1,
                text: 'Nombre proveedor'
            }, {
                id: 2,
                text: 'Codigo proveedor'
            }, {
                id: 3,
                text: 'Nit proveedor'
            }, ],

            proveedores: [],
            proveedoresDataSource: {
                paginate: true,
                pageSize: 10,
                sort: 'Codigo',
                key: 'Codigo',
                // loadMode: 'raw',
                byKey: (e) => {
                    return this.proveedores.find(x => x.Codigo === e)
                },
                load: (e) => {
                    if (e.take) {
                        if (e.skip === e.take) {
                            e.take = e.take + 10
                        }
                    }
                    return this.$filtrado_avanzado_palabras(e.searchValue, this.proveedores, 'Display', e.skip, e.skip + e.take)
                }
            },

            valoresTabla: [],

            opciones: [],

            deshabilitarGrabar: true, // Se inicializa con true para que al iniciar el botón de grabar esté desactivado
            periodo: null,

            proveedorSeleccionado: false // Variable para verificar si ya se seleccionó el proveedor (Esto porque el evento de onSeletectItem siempre se ejecuta 2 veces)
        }

    },
    props: {
        TipoDoc: null, // Indica desde qué módulo se invoca el componente  1 = Cheques -> Proveedores -> Anticipos / 2 = Transferencias -> Proveedores -> Anticipos
        Corporativo: null
    },
    methods: {
        handleSubmit(e) {
            e.preventDefault()
        },

        CargarCuentas(asignar) { // La variable de asignar indica cuando debe seleccionar la primera cuenta de la lista
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 1
                })
                .then(resp => {
                    this.cuentas = resp.data.json

                    if (asignar == 1) {
                        this.AsignarBanco(this.cuentas[0])
                    } else if (asignar == 2) {
                        let codigo = this.cuentas.find((x) => x.Codigo == this.formulario.Cuenta)

                        this.formulario.SiguienteCheque = codigo.SiguienteCheque
                    }
                })
        },
        CargarOpciones() {
            this.axios.post('/app/v1_bancos/Opciones', {
                    Opcion: 1
                })
                .then(resp => {
                    this.opciones = resp.data.json[0]
                })
        },
        async BuscarProveedores() {
            await this.axios.post('/app/v1_bancos/Proveedores', {
                    Opcion: 4
                })
                .then(resp => {
                    this.proveedores = resp.data.json.map((x) => {
                        return {
                            Codigo: x.Codigo,
                            Nombre: x.Nombre,
                            Nit: x.Nit,
                            Retencion: parseFloat(x.Retencion).toFixed(2),
                            TipoRetencion: x.TipoRetencion,
                            CuentaRetencion: x.CuentaRetencion,
                            ContaAnticipo: x.ContaAnticipo,
                            NombreCheque: x.NombreCheque,
                            CuentaBanco: x.CuentaBanco,
                            Display: x.Codigo + ' - ' + x.Nit + ' - ' + x.Nombre
                        }
                    })
                })
        },

        CargarTiposAnticipo() {
            this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 5
                })
                .then(resp => {
                    this.tiposAnticipo = resp.data.json

                    this.AsignarAnticipo({
                        itemData: this.tiposAnticipo[0]
                    })
                })
        },

        AsignarSiguienteCheque(e) {
            let cuenta = this.cuentas.find((x) => x.Codigo == e.value)
            this.AsignarBanco(cuenta)
            if (cuenta.SiguienteCheque == null || cuenta.SiguienteCheque == '') {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cuenta sin número',
                    acceptText: 'Aceptar',
                    text: 'Debe ingresar un número para la cuenta seleccionada',
                    buttonCancel: 'border',
                    accept: () => {},
                })
            }
        },
        BuscarOrdenes() {
            this.axios.post('/app/v1_bancos/Ordenes', {
                    Opcion: this.ordenesBusqueda.TipoBusqueda,
                    ValorBusqueda: this.ordenesBusqueda.ValorBusqueda
                })
                .then(resp => {
                    this.ordenes = resp.data.json
                })
        },
        ValidaFactura() {
            this.axios.post('/app/v1_bancos/Anticipos', {
                    Opcion: 8,
                    SerieDevolucion: this.devolucionesBusqueda.SerieDevolucion,
                    CodigoDevolucion: this.devolucionesBusqueda.CodigoDevolucion,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.devolucionesBusqueda.SerieCorta = resp.data.json[0].Serie
                        this.devolucionesBusqueda.CodigoCorta = resp.data.json[0].Codigo

                        this.BuscarDevolucion(9, this.devolucionesBusqueda.SerieCorta, this.devolucionesBusqueda.CodigoCorta)
                    } else {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Factura no encontrada',
                            acceptText: 'Aceptar',
                            text: 'La factura no existe en esta empresa. Digite nuevamente.',
                            buttonCancel: 'border',
                            accept: () => {
                                this.LimpiarFormularioDevoluciones()
                            },
                        })
                        return
                    }
                    // this.ordenes = resp.data.json
                })
        },

        async ConsultarPeriodo() {
            await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 1,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.periodo = resp.data.json[0].Codigo
                    }
                })
        },

        async ConsultarSaldoFinal() {
            return await this.axios.post('/app/v1_bancos/Periodos', {
                    Opcion: 2,
                    Cuenta: this.formulario.Cuenta,
                    Periodo: this.periodo
                })
                .then(resp => {
                    if (resp.data.json.length === 0) {
                        return false

                    } else if (resp.data.json[0].SaldoFinal !== null) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Período no activo',
                            acceptText: 'Aceptar',
                            text: 'Debe reabrir el registro del libro negro para este período para poder grabar esta transacción.',
                            buttonCancel: 'border',
                            accept: () => {
                            },
                        })
                        return true
                    }
                })
        },

        BuscarDevolucion(opcion, serie, codigo) { // 9 = buscar factura / 10 = buscar recibo
            this.axios.post('/app/v1_bancos/Anticipos', {
                    Opcion: opcion,
                    SerieDevolucion: serie,
                    CodigoDevolucion: codigo,
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {

                        this.devoluciones.push({
                            Serie: this.devolucionesBusqueda.SerieDevolucion,
                            Documento: this.devolucionesBusqueda.CodigoDevolucion,
                            Valor: this.devolucionesBusqueda.Valor,
                            TipoDoc: opcion == 9 ? 'F' : 'R',
                            Nombre: resp.data.json[0].Nombre,
                            NombreCuenta: resp.data.json[0].NombreCuenta,
                            SerieAdmision: resp.data.json[0].SerieAdmision,
                            Admision: resp.data.json[0].Admision,
                            Cuenta: resp.data.json[0].Cuenta
                        })

                        this.LimpiarFormularioDevoluciones()
                        this.formDevolucionesInstance.getEditor('SerieDevolucion').focus()
                    } else {
                        if (String(this.formulario.NombreAnticipo).includes('Factura')) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Factura no encontrada',
                                acceptText: 'Aceptar',
                                text: 'No se encontró la Factura en el auxiliar de conta. Consulte a contabilidad.',
                                buttonCancel: 'border',
                                accept: () => {
                                    this.LimpiarFormularioDevoluciones()
                                },
                            })
                            return
                        } else if (String(this.formulario.NombreAnticipo).includes('Recibo')) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Recibo no encontrada',
                                acceptText: 'Aceptar',
                                text: 'No se encontró la Recibo en el auxiliar de conta. Consulte a contabilidad.',
                                buttonCancel: 'border',
                                accept: () => {
                                    this.LimpiarFormularioDevoluciones()
                                },
                            })
                            return
                        }
                    }
                    // this.ordenes = resp.data.json
                })
        },
        LimpiarFormularioDevoluciones() {
            this.devolucionesBusqueda.SerieDevolucion = null
            this.devolucionesBusqueda.CodigoDevolucion = null
            this.devolucionesBusqueda.Valor = null
        },
        BuscarOrdenNumeroOrden() {
            if (this.formulario.NumeroOrden !== '' && this.formulario.NumeroOrden !== null && this.formulario.NumeroOrden !== undefined) {
                this.axios.post('/app/v1_bancos/Ordenes', {
                        Opcion: 4,
                        CodigoOrden: this.formulario.NumeroOrden
                    })
                    .then(resp => {
                        if (resp.data.json.length > 0) { // Verifica que exista la orden
                            //Verifica el estado de la orden
                            if (resp.data.json[0].EstadoOrden !== 'S' || resp.data.json[0].EstadoOrden == null) { // Si la orden no está autorizada
                                this.$vs.dialog({
                                    type: 'alert',
                                    color: '#ed8c72',
                                    title: 'Orden no autorizada',
                                    acceptText: 'Aceptar',
                                    text: 'Esta orden NO ESTÁ AUTORIZADA',
                                    buttonCancel: 'border',
                                    accept: () => {
                                        this.LimpiarVariablesCambioOrden()
                                    },
                                })
                            } else {
                                if (resp.data.json[0].AutorizadoPor !== '' && resp.data.json[0].AutorizadoPor !== null) {
                                    this.formulario.AutorizadoPor = resp.data.json[0].AutorizadoPor
                                } else {
                                    this.formulario.AutorizadoPor = 'USUARIO NO DEFINIDO'
                                }
                                this.formulario.FechaAutorizacion = new Date(resp.data.json[0].FechaAutorizacion)

                                this.formulario.Proveedor = resp.data.json[0].Proveedor
                                this.formulario.CuentaRetencion = resp.data.json[0].CuentaRetencion

                                this.formulario.PagarNombre = resp.data.json[0].NombreCheque
                                this.formulario.Monto = resp.data.json[0].Monto
                                this.formulario.MontoMaximo = resp.data.json[0].Monto

                                this.formulario.TipoRetencion = resp.data.json[0].TipoRetencion
                                this.formulario.TipoValidacion = resp.data.json[0].Validacion

                                if (resp.data.json[0].TipoRetencion !== null && resp.data.json[0].TipoRetencion !== '') {
                                    this.formulario.AplicarRetencion = true
                                } else {
                                    this.formulario.AplicarRetencion = false
                                }

                                if (!isNaN(resp.data.json[0].Retencion) && resp.data.json[0].Retencion !== '' && resp.data.json[0].Retencion !== null) {
                                    this.formulario.Retencion = resp.data.json[0].Retencion
                                }

                                // Verificamos si posee cheques por anticipo
                                if (parseFloat(resp.data.json[0].Valor) > 0) {
                                    // this.formulario.Monto = 0

                                    if (resp.data.json[0].NumeroAnticipo == null || resp.data.json[0].NumeroAnticipo == '') {

                                        this.$vs.dialog({
                                            type: 'alert',
                                            color: '#ed8c72',
                                            title: 'Cheque existente',
                                            acceptText: 'Aceptar',
                                            text: 'Esta orden de compra ya posee cheque de anticipo...',
                                            buttonCancel: 'border',
                                            accept: () => {},
                                        })
                                    } else {

                                        let text = 'Esta orden de compra posee anticipo Cheque #' + resp.data.json[0].NumeroAnticipo + ' Cuenta #' + resp.data.json[0].CuentaAnticipo + ' Fecha ' + this.$formato_fecha(new Date(resp.data.json[0].FechaAnticipo).toLocaleDateString(), 'dd/MM/yyyy') + ' a Nombre de ' + resp.data.json[0].NombreBeneficiarioAnticipo + ' por un Valor de Q. ' + resp.data.json[0].MontoAnticipo

                                        this.$vs.dialog({
                                            type: 'alert',
                                            color: '#ed8c72',
                                            title: 'Anticipo existente',
                                            acceptText: 'Aceptar',
                                            text: text,
                                            buttonCancel: 'border',
                                            accept: () => {
                                                this.LimpiarVariablesCambioOrden() // VALIDAR SI SE LIMPIA, YA QUE EN DELPHI NO DEJA HACER NADA CUANDO CARGA UNA ORDEN CON CHEQUE
                                            },
                                        })
                                    }
                                } else { // Verificamos si posee cheques por medio de pagos generados
                                    this.axios.post('/app/v1_bancos/ChequesPagos', {
                                            Opcion: 1,
                                            CodigoOrden: this.formulario.NumeroOrden
                                        })
                                        .then(resp => {
                                            if (parseFloat(resp.data.json[0].Valor) > 0) {

                                                this.axios.post('/app/v1_bancos/ChequesPagos', {
                                                        Opcion: 2,
                                                        CodigoOrden: this.formulario.NumeroOrden
                                                    })
                                                    .then(resp => {
                                                        if (resp.data.json.length > 0) {
                                                            let text = 'Esta orden de compra posee el sig. Cheque #' + resp.data.json[0].Numero + ' Cuenta #' + resp.data.json[0].Cuenta + ' Fecha ' + new Date(resp.data.json[0].Fecha).toLocaleDateString() + ' a Nombre de ' + resp.data.json[0].NombreBeneficiario + ' por un Valor de ' + resp.data.json[0].MontoAnticipo
                                                            this.$vs.dialog({
                                                                type: 'alert',
                                                                color: '#ed8c72',
                                                                title: 'Cheques generador',
                                                                acceptText: 'Aceptar',
                                                                text: text,
                                                                buttonCancel: 'border',
                                                                accept: () => {
                                                                    this.LimpiarVariablesCambioOrden() // VALIDAR SI SE LIMPIA, YA QUE EN DELPHI NO DEJA HACER NADA CUANDO CARGA UNA ORDEN CON CHEQUE
                                                                },
                                                            })
                                                        } else {
                                                            this.$vs.dialog({
                                                                type: 'alert',
                                                                color: '#ed8c72',
                                                                title: 'Cheques generador',
                                                                acceptText: 'Aceptar',
                                                                text: 'Esta orden de compra ya posee cheques generados...',
                                                                buttonCancel: 'border',
                                                                accept: () => {},
                                                            })
                                                        }
                                                    })
                                            }
                                        })
                                }
                                this.formAnticipoInstance.repaint()
                            }

                        } else {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Orden no encontrada',
                                acceptText: 'Aceptar',
                                text: 'El número de orden NO ESTÁ REGISTRADA',
                                buttonCancel: 'border',
                                accept: () => {
                                    this.LimpiarVariablesCambioOrden()
                                },
                            })
                        }
                    })
            }
        },
        BuscarSiguienteNota() {
            this.axios.post('/app/v1_bancos/Anticipos', {
                    Opcion: 7,
                    TipoMov: 'NA'
                })
                .then(resp => {
                    if (resp.data.json.length > 0) {
                        this.formulario.SiguienteNota = resp.data.json[0].SiguienteNota
                        this.formulario.SiguienteCheque = resp.data.json[0].SiguienteNota
                    }
                })
        },
        SeleccionarOrden(e) {
            this.formulario.NumeroOrden = null
            this.formulario.AutorizadoPor = null
            this.formulario.FechaAutorizacion = null
            this.formulario.Proveedor = null
            this.formulario.NumeroOrden = e.data.Codigo

            this.mostrarModalOrdenes = false
            this.LimpiarVariablesModal()
        },

        LimpiarVariablesModal() {
            this.ordenesBusqueda.ValorBusqueda = null
            this.ordenes = []
            this.formOrdenBuscarInstance.repaint()
        },

        LimpiarVariablesCambioOrden() {
            this.formulario.NumeroOrden = null
            this.formulario.AutorizadoPor = null
            this.formulario.FechaAutorizacion = null
            this.formulario.NombreProveedor = null
            this.formulario.Proveedor = null
            this.formulario.Observaciones = null
            this.formulario.CuentaRetencion = null
            this.formulario.CuentaBancoProveedor = null
            this.formulario.ContaAnticipoProveedor = null
            this.formulario.PagarNombre = null
            this.formulario.Monto = 0
            this.formulario.MontoMaximo = null
            this.formulario.AplicarRetencion = false
            this.formulario.TipoRetencion = null
            this.formulario.Retencion = 0
            this.formulario.TipoValidacion = null
            this.formulario.NombreNotaDebito = null
            this.formulario.CuentaBi = null

            this.valoresTabla = []
        },
        CambioDeFuncion(funcion) {
            this.LimpiarVariablesCambioOrden()

            if (funcion === 1) {
                this.notaDebito = false
            } else if (funcion === 2) {
                this.notaDebito = true
            }
        },
        AplicarRetencionChanged(e) {
            if (e.value) {
                this.formulario.AplicarRetencion = true
            } else {
                this.formulario.AplicarRetencion = false
            }
        },
        LlenarTabla() {
            this.valoresTabla = []
            if (this.formulario.NombreCuentaBanco) { // Verifica que la cuenta de banco seleccionada esté registrada en cuentas contables
                if (this.formulario.SiguienteCheque !== null && this.formulario.SiguienteCheque !== '' && this.formulario.SiguienteCheque !== 0) {
                    //Datos de la cuenta de banco
                    this.valoresTabla.push({
                        Documento: this.formulario.SiguienteCheque,
                        Detalle: null,
                        Cuenta: this.formulario.BancoContabilidad,
                        Nombre: this.formulario.NombreCuentaBanco,
                        Haber: this.formulario.Monto,
                        Debe: null
                    })

                    if (this.formulario.Funcion === 'CH' || this.formulario.Funcion === 'NA') //Verifica que la función a realizar sea cheque o una acreditación
                    {
                        if (String(this.formulario.NombreAnticipo).includes('Factura')) //Verifica si el tipo de anticipo será una devolución
                        {
                            for (let i = 0; i < this.devoluciones.length; i++) {
                                this.valoresTabla.push({
                                    Documento: this.devoluciones[i].Serie + '-' + this.devoluciones[i].Documento,
                                    Detalle: this.formulario.NombreAnticipo,
                                    Cuenta: this.devoluciones[i].Cuenta,
                                    Nombre: this.devoluciones[i].NombreCuenta,
                                    Haber: null,
                                    Debe: this.devoluciones[i].Valor
                                })
                            }
                        } else if (String(this.formulario.NombreAnticipo).includes('Recibo')) //Verifica si el tipo de anticipo será una devolución de recibo
                        {
                            for (let i = 0; i < this.devoluciones.length; i++) {
                                this.valoresTabla.push({
                                    Documento: this.devoluciones[i].Serie + '-' + this.devoluciones[i].Documento,
                                    Detalle: this.formulario.NombreAnticipo,
                                    Cuenta: this.devoluciones[i].Cuenta,
                                    Nombre: this.devoluciones[i].NombreCuenta,
                                    Haber: null,
                                    Debe: this.devoluciones[i].Valor
                                })
                            }
                        } else {
                            this.valoresTabla.push({
                                Documento: null,
                                Detalle: null,
                                Cuenta: this.formulario.AnticipoContabilidad,
                                Nombre: this.formulario.NombreAnticipo,
                                Haber: null,
                                Debe: this.formulario.Monto
                            })
                        }
                    } else if (this.formulario.Funcion === 'ND') { // Nota de débito
                        if (this.formulario.ContaAnticipoProveedor == null) {
                            this.$vs.dialog({
                                type: 'alert',
                                color: '#ed8c72',
                                title: 'Cuenta no definida',
                                acceptText: 'Aceptar',
                                text: 'La cuenta del proveedor no está definida.',
                                buttonCancel: 'border',
                                accept: () => {},
                            })

                            this.valoresTabla = []
                        } else {
                            this.valoresTabla.push({
                                Documento: null,
                                Detalle: 'Anticipo nd',
                                Cuenta: this.formulario.ContaAnticipoProveedor,
                                Nombre: this.formulario.NombreNotaDebito,
                                Haber: null,
                                Debe: this.formulario.Monto
                            })
                        }
                    }
                }

            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cuenta no definida',
                    acceptText: 'Aceptar',
                    text: 'La cuenta de banco seleccionada no está definida.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
            }

        },

        customizeTextValores,

        async Grabar() {
            if (this.valoresTabla.length >= 2) { //La tabla debe tener al menos 2 montos para poder cuadrar la información

                // if (this.formulario.Funcion == 'NA') // Verifica si es Acreditamiento
                // {
                //     if (this.formulario.CuentaBi == null || this.formulario.CuentaBi.length < 5) { // Verifica que el número de la cuenta del Bi sea válida
                //         this.$vs.dialog({
                //             type: 'alert',
                //             color: '#ed8c72',
                //             title: 'Error con la cuenta',
                //             acceptText: 'Aceptar',
                //             text: 'La cuenta bancaria no es válida.',
                //             buttonCancel: 'border',
                //             accept: () => {

                //             },
                //         })
                //         return
                //     }
                // }

                if (this.formulario.Monto == null || this.formulario.Monto == 0) { // Verifica el monto del cheque 
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'El monto no es válido',
                        acceptText: 'Aceptar',
                        text: 'El monto del cheque debe ser mayor que cero. No puede continuar.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                    return
                }

                // Recorre los registros que se encuentran en la tabla, para saber si algún registro le falta el nombre de la cuenta
                for (let i = 0; i < this.valoresTabla.length; i++) {
                    if (!(this.valoresTabla[i].Nombre) && !(this.valoresTabla[i].Cuenta)) {
                        this.$vs.dialog({
                            type: 'alert',
                            color: '#ed8c72',
                            title: 'Cuentas erróneas',
                            acceptText: 'Aceptar',
                            text: 'Todas las cuentas del voucher deben estar llenas correctamente. No puede continuar.',
                            buttonCancel: 'border',
                            accept: () => {},
                        })
                        return
                    }
                }

                const totalDebe = this.dataGridDetalle.getTotalSummaryValue("Debe");
                const totalHaber = this.dataGridDetalle.getTotalSummaryValue("Haber");
                if (Math.abs(totalDebe - totalHaber) !== 0 ) {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Montos no cuadran',
                        acceptText: 'Aceptar',
                        text: 'Las columnas del voucher deben cuadrar y no estar a cero. No puede continuar.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                    return
                }

                if (!(this.formulario.PagarNombre) && this.formulario.Funcion != 'ND') {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Nombre beneficiario',
                        acceptText: 'Aceptar',
                        text: 'Debe indicar el nombre del beneficiario. No puede continuar.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                    return
                }

                if (this.periodo !== null) {
                    let saldoFinal = await this.ConsultarSaldoFinal()
                    if (!saldoFinal) {
                        if (this.valoresTabla.length > 16) {
                            this.$vs.dialog({
                                type: 'confirm',
                                color: '#ed8c72',
                                title: 'Exceso de líneas',
                                acceptText: 'Aceptar',
                                text: 'El voucher tiene más de 16 líneas y no caben en el cheque, si continúa las últimas líneas no se imprimirán.',
                                buttonCancel: 'border',
                                accept: () => {},
                                cancelText: 'Cancelar',
                                cancel: () => {}
                            })
                            return
                        }

                        if (!this.formulario.AplicarRetencion) {
                            return new Promise((resolve) => {
                                this.$vs.dialog({
                                    type: 'confirm',
                                    color: '#ed8c72',
                                    title: 'Retención',
                                    acceptText: 'Aceptar',
                                    text: '¿Está seguro que no quiere aplicar retención?',
                                    buttonCancel: 'border',
                                    accept: () => {
                                        this.Grabar1(this.periodo)
                                        resolve(true)
                                    },
                                    cancelText: 'Cancelar',
                                    cancel: () => {
                                        resolve(false)
                                    }
                                })
                            })
                        } else {
                            await this.axios.post('/app/v1_bancos/Anticipos', { // Busca en las tablas si hay registros con los datos de la empresa, cuenta, cheque y movimiento
                                    Cuenta: this.formulario.Cuenta,
                                    Numero: this.formulario.SiguienteCheque,
                                    TipoMov: this.formulario.Funcion,
                                    Opcion: 12
                                })
                                .then(resp => {
                                    if (resp.data.json.length > 0) {
                                        let datos = Object.values(resp.data.json[0]) // Obtiene la cantidad de registros que hay en cada tabla con los datos ingresados
                                        let valido = 0 // 
                                        for (let i = 0; i < datos.length; i++) {
                                            valido = parseInt(valido + datos[i])
                                        }
                                        if (valido === 0) { // Si el total de registros es igual a cero, se procede a insertar los datos
                                            this.Grabar1(this.periodo)
                                        } else {
                                            this.$vs.dialog({
                                                type: 'alert',
                                                color: '#ed8c72',
                                                title: 'Error número de transferencia',
                                                acceptText: 'Aceptar',
                                                text: 'Error al intentar grabar transferencia. Probablemente este Número de Orden ya fue registrado.',
                                                buttonCancel: 'border',
                                                accept: () => {},
                                            })
                                            return
                                        }
                                    }
                                })
                        }
                    }
                } else {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Período no definido',
                        acceptText: 'Aceptar',
                        text: 'No existe un período para la fecha actual.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                    return
                }
            } else {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Datos erróneos',
                    acceptText: 'Aceptar',
                    text: 'Revise los datos de las cuentas y agregue observaciones nuevamente.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
                return
            }
        },

        async Grabar1(periodo) {
            let valorCheque = this.formulario.Monto,
                valorRetencion = 0,
                baseRetencion = 0

            if (this.formulario.AplicarRetencion) {
                if (parseFloat(this.formulario.Retencion) > 0) {
                    baseRetencion = parseFloat((((100 * valorCheque) / (1 + (this.opciones.IVA / 100))) / 100).toFixed(2))
                    valorRetencion = parseFloat(((100 * baseRetencion * (this.formulario.Retencion / 100)) / 100).toFixed(2))
                    valorCheque = parseFloat((valorCheque - valorRetencion).toFixed(2))
                } else {
                    baseRetencion = parseFloat((((100 * valorCheque) / (1 + (this.opciones.IVA / 100))) / 100).toFixed(2))
                }
            }

            let anticipos = {
                BancoMovto: {},
                Devolucion: [],
                Retencion: {},
                AuxiliarBanco: [],
                ComprasPagos: {}
            }

            // Graba el cheque en movimientos del banco
            anticipos.BancoMovto = {
                Opcion: 1,
                Cuenta: this.formulario.Cuenta,
                Numero: this.formulario.SiguienteCheque,
                Periodo: periodo,
                Proveedor: this.formulario.Proveedor,
                Monto: valorCheque,
                Usuario: this.Corporativo,
                OrdenCompra: this.formulario.NumeroOrden,
                ValidacionCompra: this.formulario.TipoValidacion,
                Observaciones: this.formulario.Observaciones,
                TipoMov: this.formulario.Funcion,
                NombreBeneficiario: this.formulario.Funcion == 'ND' ? '' : this.formulario.PagarNombre,
                ClaveArchivo: this.formulario.Funcion == 'NA' ? this.formulario.CuentaBi : null,
                TipoCheque: 'P',
                Conciliado: 'N'
            }

            for (let i = 0; i < this.devoluciones.length; i++) {
                if (String(this.formulario.NombreAnticipo).includes('Recibo')) {
                    anticipos.Devolucion.push({
                        Opcion: 6,
                        SerieAdmision: this.devoluciones[i].SerieAdmision ? this.devoluciones[i].SerieAdmision : '',
                        Admision: this.devoluciones[i].Admision ? this.devoluciones[i].Admision : 0,
                        Cuenta: this.formulario.Cuenta,
                        Numero: this.formulario.SiguienteCheque,
                        Observaciones: this.formulario.Observaciones,
                        Valor: this.devoluciones[i].Valor,
                        Usuario: this.Corporativo,
                        Periodo: periodo,
                        SerieRecibo: this.devoluciones[i].Serie,
                        Recibo: this.devoluciones[i].Documento,
                    })
                } else if (String(this.formulario.NombreAnticipo).includes('Factura')) {
                    anticipos.Devolucion.push({
                        Opcion: 6,
                        SerieAdmision: this.devoluciones[i].SerieAdmision ? this.devoluciones[i].SerieAdmision : '',
                        Admision: this.devoluciones[i].Admision ? this.devoluciones[i].Admision : 0,
                        Cuenta: this.formulario.Cuenta,
                        Numero: this.formulario.SiguienteCheque,
                        Observaciones: this.formulario.Observaciones,
                        Valor: this.devoluciones[i].Valor,
                        Usuario: this.Corporativo,
                        Periodo: periodo,
                        SerieFactura: this.devoluciones[i].Serie,
                        Factura: this.devoluciones[i].Documento,
                    })
                }
            }

            let posicionTabla = 0 //Esta variable indicará si ya se ingresó el primer registro de la tabla valoresTabla

            if (this.formulario.AplicarRetencion) {
                // Insertar retencion

                anticipos.Retencion = {
                    Opcion: 3,
                    Cuenta: this.formulario.Cuenta,
                    Numero: this.formulario.SiguienteCheque,
                    Monto: valorRetencion,
                    Base: baseRetencion,
                    Porcentaje: parseFloat((this.formulario.Retencion / 100).toFixed(2)),
                    ValorBruto: this.formulario.Monto,
                    TipoRetencion: this.formulario.TipoRetencion,
                    Periodo: periodo
                }

                // Insertar en auxiliar de bancos
                // Se ingresa la primera línea de la tabla, que debe ser el monto que se abona al banco
                // Se le quita la retención

                anticipos.AuxiliarBanco.push({
                    Opcion: 4,
                    TipoMov: this.formulario.Funcion,
                    Cuenta: this.formulario.Cuenta,
                    Numero: this.formulario.SiguienteCheque,
                    Periodo: periodo,
                    CuentaBanco: this.valoresTabla[0].Cuenta,
                    Documento: this.valoresTabla[0].Documento,
                    Detalle: this.valoresTabla[0].Detalle,
                    Validacion: null,
                    Haber: valorCheque,
                    OrdenCompra: null,
                    Indice: 1
                })

                // Se sustitye el valor en la tabla que se muestra, para que la suma total cuadre
                this.valoresTabla[0].Haber = valorCheque

                // Si existe retención, la agrega a la tabla
                if (valorRetencion > 0) {
                    this.valoresTabla.push({
                        Documento: null,
                        Detalle: null,
                        Cuenta: this.formulario.CuentaRetencion,
                        Nombre: null,
                        Haber: valorRetencion,
                        Debe: null
                    })
                }

                posicionTabla = 1
            }

            let registroOrdenAutorizada = false

            // Se realiza la inserción de los demás registros de la tabla
            for (let i = posicionTabla; i < this.valoresTabla.length; i++) {
                let orden = this.formulario.NumeroOrden

                if (!registroOrdenAutorizada) {
                    if (Number.isInteger(orden)) {
                        orden = parseInt(this.formulario.NumeroOrden)
                        registroOrdenAutorizada = true
                    } else {
                        orden = null
                    }
                }

                anticipos.AuxiliarBanco.push({
                    Opcion: 4,
                    TipoMov: this.formulario.Funcion,
                    Cuenta: this.formulario.Cuenta,
                    Numero: this.formulario.SiguienteCheque,
                    Periodo: periodo,
                    CuentaBanco: this.valoresTabla[i].Cuenta,
                    Documento: this.valoresTabla[i].Documento,
                    Detalle: this.valoresTabla[i].Detalle,
                    Validacion: this.formulario.TipoValidacion,
                    Haber: this.valoresTabla[i].Haber,
                    Debe: this.valoresTabla[i].Debe,
                    OrdenCompra: i == posicionTabla ? orden : null,
                    Indice: i + 1
                })
            }

            // Graba el anticipo
            anticipos.ComprasPagos = {
                Opcion: 5,
                Proveedor: this.formulario.Proveedor,
                Cuenta: this.formulario.Cuenta,
                Numero: this.formulario.SiguienteCheque,
                Periodo: periodo,
                TipoMov: this.formulario.Funcion,
                Monto: this.formulario.Monto,
                Tipo: 'A',
                Usuario: this.Corporativo
            }

            await this.axios.post('/app/v1_bancos/AnticiposAgrupado', {
                    ...anticipos
                })
                .then(async resp => {
                    if (resp.data.codigo == 0) {
                        if (this.TipoDoc == 1) {
                            // Incrementa el número de cheque
                            await this.axios.post('/app/v1_bancos/Cuentas', {
                                    Codigo: this.formulario.Cuenta,
                                    SiguienteCheque: parseInt(this.formulario.SiguienteCheque + 1),
                                    Opcion: 6
                                })
                                .then(resp => {
                                    if (resp.data.codigo == 0) {
                                        this.LimpiarVariablesCambioOrden()
                                        this.CargarCuentas(2)
                                    }
                                })
                        } else if (this.TipoDoc == 2) {
                            // Actualiza el correlativo de la nota de acreditamiento
                            this.axios.post('/app/v1_bancos/Anticipos', {
                                    Opcion: 2,
                                    Numero: parseInt(this.formulario.SiguienteCheque + 1),
                                    TipoMov: 'NA',
                                })
                                .then(resp => {
                                    if (resp.data.codigo == 0) {
                                        this.LimpiarVariablesCambioOrden()
                                        this.BuscarSiguienteNota()
                                    }
                                })
                        }
                    }
                })

        },
        AsignarBanco(e) { //Asignar los valores del banco a los campos correspondientes del formulario
            this.formulario.Cuenta = e.Codigo
            this.formulario.BancoContabilidad = e.Contabilidad
            this.formulario.NumeroCuenta = e.Cuenta
            this.formulario.NombreCuentaBanco = e.NombreCuenta
            this.formulario.SiguienteCheque = null
            this.$nextTick(() => {
                if (this.TipoDoc == 2) {
                    this.formulario.SiguienteCheque = this.formulario.SiguienteNota
                } else {
                    this.formulario.SiguienteCheque = e.SiguienteCheque
                }
            })

        },
        AsignarAnticipo(e) { //Asignar los valores del banco a los campos correspondientes del formulario
            this.formulario.TipoAnticipo = e.itemData.Codigo
            this.formulario.AnticipoContabilidad = e.itemData.Cuenta
            this.formulario.NombreAnticipo = e.itemData.Descripcion

            let descripcion = this.formulario.NombreAnticipo

            if (descripcion.includes('Devoluciones')) {
                this.devoluciones = []
                this.mostrarModalDevoluciones = true

                if (descripcion.includes('Factura')) {
                    this.tipoDevolucion = 9
                } else if (descripcion.includes('Recibo')) {
                    this.tipoDevolucion = 10
                }
            }
        },

        HabilitarBoton() {
            if (this.formulario.Funcion === 'CH' || this.formulario.Funcion === 'NA') {
                if (Boolean(this.formulario.NumeroOrden) && this.formulario.Observaciones !== null && this.formulario.Observaciones !== '' && this.formulario.NombreCuentaBanco !== null && this.formulario.NombreCuentaBanco !== '' && Boolean(this.formulario.Proveedor)) {
                    this.formAnticipoInstance.itemOption('Grabar', 'visible', true)
                    this.LlenarTabla() // Verificar si llamar esto aquí 
                } else {
                    this.formAnticipoInstance.itemOption('Grabar', 'visible', false)
                }
            }

            if (this.formulario.Funcion === 'ND') {
                if (this.formulario.Observaciones !== null && this.formulario.Observaciones !== '' && this.formulario.NombreCuentaBanco !== null && this.formulario.NombreCuentaBanco !== '' && Boolean(this.formulario.Proveedor)) {
                    this.formAnticipoInstance.itemOption('Grabar', 'visible', true)
                    this.LlenarTabla() // Verificar si llamar esto aquí 
                } else {
                    this.formAnticipoInstance.itemOption('Grabar', 'visible', false)
                }
            }
        },
        async SeleccionarProveedor(e) // Se ejecutará cuando se selecciona un proveedor para una nota de débito
        {

            this.formulario.ContaAnticipoProveedor = e.itemData.ContaAnticipo
            this.formulario.CuentaRetencion = e.itemData.CuentaRetencion

            if (!isNaN(e.itemData.Retencion) && e.itemData.Retencion !== '' && e.itemData.Retencion !== null) {
                this.formulario.Retencion = e.itemData.Retencion
            }

            this.formulario.TipoRetencion = e.itemData.TipoRetencion

            if (e.itemData.TipoRetencion !== null && e.itemData.TipoRetencion !== '') {
                this.formulario.AplicarRetencion = true
            } else {
                this.formulario.AplicarRetencion = false
            }

            if (this.formulario.Funcion == 'ND') {

                if (this.formulario.ContaAnticipoProveedor == null || this.formulario.ContaAnticipoProveedor == '') {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Proveedor no válido',
                        acceptText: 'Aceptar',
                        text: 'El proveedor no cuenta con cuenta para el anticipo.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                    return
                }

                await this.axios.post('/app/v1_bancos/Cuentas', {
                    Opcion: 7,
                    Cuenta: this.formulario.ContaAnticipoProveedor
                }).then(resp => {
                    if (resp.data.json.length > 0) {
                        this.formulario.NombreNotaDebito = resp.data.json[0].Nombre
                    }
                })
            } else if (this.formulario.Funcion == 'NA') {
                this.formulario.PagarNombre = e.itemData.NombreCheque
                this.formulario.CuentaBancoProveedor = e.itemData.CuentaBanco

                if (e.itemData.CuentaBanco !== null && e.itemData.CuentaBanco !== '') {
                    this.formulario.CuentaBi = e.itemData.CuentaBanco

                    this.axios.post('/app/v1_bancos/Anticipos', {
                            Opcion: 13,
                            CuentaBanco: this.formulario.CuentaBi,
                        })
                        .then(resp => {
                            if (resp.data.json.length > 1) {
                                let otros = ''
                                for (let i = 0; i < resp.data.json.length; i++) { // Recorre la 
                                    if (resp.data.json[i].Codigo !== this.formulario.Proveedor) {
                                        otros = otros + resp.data.json[i].Codigo + ' - ' + resp.data.json[i].Nombre + '\n'
                                    }
                                }

                                if (otros !== '') {
                                    this.$vs.dialog({
                                        type: 'alert',
                                        color: '#ed8c72',
                                        title: 'Otros proveedores',
                                        acceptText: 'Aceptar',
                                        text: 'Esta cuenta también le pertenece a: \n' + otros,
                                        buttonCancel: 'border',
                                        accept: () => {
                                            // this.CambioDeFuncion(2)
                                        },
                                    })
                                }
                            }
                        })
                } else {
                    this.formulario.CuentaBi = null
                }

            }
        },
        editorDatagrid(e) {
            if (e.parentType === 'dataRow' && e.dataField !== 'Valor') {
                e.editorOptions.disabled = true
            }
        },
    },
    mounted() {

        this.formAnticipoInstance.itemOption('Buscar', 'visible', false)
        this.formAnticipoInstance.repaint()
    },
    beforeMount() {
        if (this.TipoDoc !== 1) {
            this.funciones = [{
                id: 'NA',
                text: 'Acreditamiento'
            }]
            this.formulario.Funcion = 'NA'

            this.BuscarSiguienteNota()
        }
        this.CargarCuentas(1)
        this.CargarOpciones()
        this.ConsultarPeriodo()
        this.CargarTiposAnticipo()
        this.BuscarProveedores()

    },
    watch: {
        'formulario.Funcion'(newval) {
            if (newval === 'ND') {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Nota de Débito',
                    acceptText: 'Aceptar',
                    text: 'Se cambió la funcionalidad para registrar una nota de débido. Asegurese de querer hacerlo.',
                    buttonCancel: 'border',
                    accept: () => {
                        this.CambioDeFuncion(2)
                    },
                })
            } else {
                this.CambioDeFuncion(1)
            }
        },

        //Valida que el valor del campo de número de orden haya cambiado para realizar la búsqueda cuando se de a la  tecla TAB
        'formulario.NumeroOrden'(newval) {
            if (newval !== null && newval !== '' && newval !== undefined && newval !== 0) {
                this.BuscarOrdenNumeroOrden()
            } else {
                this.LimpiarVariablesCambioOrden()
            }
        },

        'formOrdenBuscar.ValorBusqueda'(newval) {
            if (newval !== null && newval !== '' && newval !== undefined && newval !== 0) {
                this.BuscarOrdenes()
            }
        },

        'formulario.PagarNombre'(newval) {
            if ((newval == null || newval == '' || newval == undefined) && this.formulario.NumeroOrden !== null && this.Funcion !== 'ND') {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Nombre beneficiario',
                    acceptText: 'Aceptar',
                    text: 'Debe indicar el nombre del beneficiario. No puede continuar.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
            }
        },

        'formulario.Observaciones'(newval) {
            if (newval !== null && newval !== '' && newval !== undefined) {
                this.LlenarTabla()
            }
        },

        'formulario.NombreCuentaBanco'(newval) {
            if (newval === null || newval === '' || newval === undefined) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ed8c72',
                    title: 'Cuenta no definida',
                    acceptText: 'Aceptar',
                    text: 'La cuenta de banco seleccionada no está definida.',
                    buttonCancel: 'border',
                    accept: () => {},
                })
            }
        },

        'formulario.Monto'(newval) {
            if (newval !== null && newval !== '' && newval !== undefined && this.formulario.NumeroOrden !== null && this.formulario.Funcion == 'CH') {
                if (parseFloat(newval) <= 0) {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Monto incorrecto',
                        acceptText: 'Aceptar',
                        text: 'El valor del anticipo debe ser mayor a 0.',
                        buttonCancel: 'border',
                        accept: () => {
                            this.formulario.Monto = this.formulario.MontoMaximo
                        },
                    })
                } else if (parseFloat(newval) > 0 && parseFloat(newval) > this.formulario.MontoMaximo) {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Monto incorrecto',
                        acceptText: 'Aceptar',
                        text: 'El valor del anticipo no debe ser mayor al valor de la orden de compra autorizada.',
                        buttonCancel: 'border',
                        accept: () => {
                            this.formulario.Monto = this.formulario.MontoMaximo
                        },
                    })
                }
            } else if (newval !== null && newval !== '' && newval !== undefined && this.formulario.Funcion == 'ND') {
                if (parseFloat(newval) <= 0) {
                    this.$vs.dialog({
                        type: 'alert',
                        color: '#ed8c72',
                        title: 'Monto incorrecto',
                        acceptText: 'Aceptar',
                        text: 'El valor del anticipo debe ser mayor a 0.',
                        buttonCancel: 'border',
                        accept: () => {},
                    })
                }
            }
        },

        'mostrarModalDevoluciones'(newval) {
            if (newval === false) {
                this.LimpiarFormularioDevoluciones()
            }
        },

        'formulario.Proveedor'() {
            if (this.formulario.TipoRetencion !== null && this.formulario.TipoRetencion !== '') {
                this.formulario.AplicarRetencion = true
            } else {
                this.formulario.AplicarRetencion = false
            }
        },

        // 'formulario.Proveedor'(newval) {
        //     if (newval != null && newval != '' && newval != undefined && this.formulario.Funcion == 'ND') {
        //         this.formAnticipoInstance.repaint()
        //     }
        // },
    },
    computed: {

        colCountByScreen() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 1,
                    md: 2,
                    lg: 2
                };
        },

        colCountByScreenOrden() {
            return this.calculateColCountAutomatically ?
                null : {
                    sm: 2,
                    md: 1,
                    lg: 1
                };
        },

        dataGridOrdenes: function () {
            return this.$refs[gridOrdenes].instance;
        },

        dataGridDetalle: function () {
            return this.$refs[gridDetalle].instance;
        },

        formOrdenBuscarInstance: function () {
            return this.$refs[formOrdenBuscar].instance;
        },

        formAnticipoInstance: function () {
            return this.$refs[formAnticipo].instance;
        },

        formDevolucionesInstance: function () {
            return this.$refs[formDevoluciones].instance;
        },

        dataGridDevoluciones: function () {
            return this.$refs[gridDevoluciones].instance;
        },
    }
}
</script>

<style>
.buttonsOrdenes .dx-item-content .dx-box-item-content {
    place-content: end !important;
}
</style>
