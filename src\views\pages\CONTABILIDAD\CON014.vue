<template>
    <div>
        <vx-card :title="`${titulo}`" v-show="!ventana">
            <div class="content content-pagex">
                <div class="flex flex-wrap mb-1">
                    <vs-divider></vs-divider>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                        <ValidationProvider name="BuscarProveedor" rules="required" v-slot="{ errors }"
                            class="required">
                            <SM-Buscar v-model="proveedor.NIT" label="Proveedor (NIT)"
                                api="app/v2_api_compras/BuscarProveedor" :api_campos="['CODIGO', 'NOMBRE', 'NIT']"
                                :api_titulos="['Código', 'Nombre', 'NIT']" api_campo_respuesta="NIT"
                                :danger="errors.length > 0" :callback_buscar="Consulta().ListarCompras"
                                :dangertext="(errors.length > 0) ? errors[0] : null" :disabled_editar="true" />
                        </ValidationProvider>
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                    </div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                        <vs-button class="w-full mr-1 mt-5" title="Crear compra" color="success"
                            :disabled="(proveedor.ACTIVO == 'N' ? true : false)" @click="Otros().AbrirVentana()">
                            Crear
                        </vs-button>
                    </div>
                </div>
            </div>
            <br>
            <vs-divider></vs-divider>
            <div class="content content-pagex">
                <vs-table2 max-items="10" search pagination :data="listaCompras">
                    <template slot="thead">
                        <th order="Documento">Documento</th>
                        <th order="Tipo">Tipo</th>
                        <th order="Periodo">Periodo</th>
                        <th order="Fecha">Fecha</th>
                        <th order="Valor">Valor</th>
                        <th order="Status">Saldo</th>
                        <th order="Status">Status</th>
                        <th>Opciones</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{ tr.Documento }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.TipoDocumento }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.PeriodoDescripcion }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Fecha }}
                            </vs-td2>
                            <vs-td2>
                                {{ new Intl.NumberFormat('es-GT', {
                                    style: 'currency',
                                    currency: 'GTQ',
                                }).format(parseFloat(tr.Valor).toFixed(2)) }}
                            </vs-td2>
                            <vs-td2>
                                {{ new Intl.NumberFormat('es-GT', {
                                    style: 'currency',
                                    currency: 'GTQ',
                                }).format(parseFloat(tr.Saldo).toFixed(2)) }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.StatusDescripcion }}
                            </vs-td2>
                            <vs-td2>
                                <vs-button title="Ver Compra" color="warning" size="small" icon-pack="fas"
                                    icon="fa-info" class="mr-1" style="display:inline-block"
                                    @click="Consulta().DetalleCompra(tr, true, false)"></vs-button>
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
        </vx-card>
        <vx-card :title="tituloVentana" v-show="ventana">
            <div class="flex flex-wrap mb-1">
                <div class="w-full p-1">
                    <vs-alert :active.sync="proveedorExcento.ExcentoRetencion" color="danger"
                        style="text-align:center;height:40px">
                        ESTE PROVEEDOR ES AGENTE DE RETENCION
                    </vs-alert>
                </div>
                <div class="w-full p-1">
                    <vs-alert :active="this.compra.Status == 'A' ? true : false" color="danger"
                        style="text-align:center;height:40px">
                        ANULADA
                    </vs-alert>
                </div>
                <vs-divider style="font-size:20px">{{ this.proveedor.NOMBRE + " / " + this.proveedor.NIT }}</vs-divider>
            </div>
            <div class="flex flex-wrap mb-1">
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                    <!-- <ValidationProvider name="BuscarProveedor" rules="required" v-slot="{ errors }" class="required">
                        <SM-Buscar v-model="proveedor.NIT" label="Proveedor" api="app/v2_api_compras/BuscarProveedor"
                            :api_campos="['CODIGO', 'NOMBRE', 'NIT']" :api_titulos="['Código', 'Nombre', 'NIT']"
                            api_campo_respuesta="NIT" :danger="errors.length > 0"
                            :callback_buscar="Consulta().ListarCompras"
                            :dangertext="(errors.length > 0) ? errors[0] : null"
                            :disabled_texto="verCompra || bloquearConta" :mostrar_busqueda="!verCompra" />
                    </ValidationProvider> -->
                    <label style="font-size:12px">Proveedor: {{ this.proveedor.CODIGO }}</label>
                </div>
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                    <label style="font-size:12px">Anticipos: {{ this.compra.Anticipos }}</label>
                </div>
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                    <vs-checkbox :disabled="verCompra || aplicaAnticipo || compra.Tipo !== 'F' || bloquearConta"
                        v-model="compra.AplicaAnticipos" @input="Otros().CalcularSaldo()">Aplicar</vs-checkbox>
                </div>
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                    <vs-checkbox :disabled="verCompra || bloquearConta" v-model="compra.CreditoContado"
                        @input="Otros().CalcularSaldo()">Crédito</vs-checkbox>
                </div>
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                    <label style="font-size:12px">Caja: {{ compra.CajaChica }}</label>
                </div>
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1">
                    <label style="font-size:12px">Lote: {{ compra.CajaChicaLote }}</label>
                </div>
            </div>
            <div class="flex flex-wrap mb-1">
                <vs-divider style="font-size:17px">Tipo Documento</vs-divider>
                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1"
                    v-for="(item) in radioOptions.filter(opcion => opcion.Mostrar == true)" :key="item.Codigo">
                    <vs-radio :vs-value="item.Codigo" class="mr-4" v-model="compra.Tipo" vs-name="Tipo"
                        :disabled="verCompra || !item.Mostrar || editarCompra">{{ item.Descripcion }}
                    </vs-radio>
                </div>
                <vs-divider></vs-divider>
            </div>
            <div class="flex flex-wrap mb-1">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1" v-if="!verCompra">
                    <ValidationProvider name="Documento" rules="required|max:10" v-slot="{ errors }" class="required">
                        <label style="font-size:12px">Documento</label>
                        <vs-input v-model="compra.Documento" type="text" class="w-full" :danger="errors.length > 0"
                            :danger-text="(errors.length > 0) ? errors[0] : null" :disabled="verCompra || editarCompra"
                            @blur="Otros().ValidarDocumento()" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1" v-if="verCompra">
                    <SM-Buscar label="Documento" api="app/v2_api_compras/ListarCompras"
                        :api_filtro="{ Proveedor: proveedor.CODIGO }" v-model="compra.Documento"
                        :api_campos="['Proveedor', 'Documento', 'Tipo', 'Fecha', 'Valor', 'Saldo']" :api_preload="true"
                        :api_titulos="['#Proveedor', 'Documento', '#Tipo', '#Fecha', '#Valor', '#Saldo']"
                        api_campo_respuesta="Documento" :callback_buscar="Otros().BuscarDocumento"
                        :disabled_texto="verCompra" ref="Documento" />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <ValidationProvider name="Sucursal">
                        <label style="font-size:12px">Sucursal</label>
                        <v-select v-model="selectSucursal" label="Codigo" :options="listaSucursales" :clearable="false"
                            disabled />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <ValidationProvider name="Fecha">
                        <label style="font-size:12px">Fecha</label>
                        <flat-pickr class="w-full" v-model="compra.Fecha" :config="configFromdateTimePicker"
                            :disabled="verCompra || bloquearConta"
                            @input="verCompra ? Otros().CambiarPeriodo(false) : Otros().CambiarPeriodo(true)" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <ValidationProvider name="Periodo">
                        <label style="font-size:12px">Periodo</label>
                        <v-select v-model="selectPeriodo" label="Descripcion" :options="listaPeriodos"
                            :clearable="false" :disabled="verCompra || bloquearConta"
                            @input="Otros().ValidarPeriodo()" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <ValidationProvider name="Vencimiento">
                        <label style="font-size:12px">Vencimiento</label>
                        <flat-pickr class="w-full" v-model="compra.Vencimiento" :config="configFromdateTimePicker"
                            :disabled="verCompra || bloquearConta" @input="Otros().ValidarVencimiento()" />
                    </ValidationProvider>
                </div>
            </div>
            <div class="flex flex-wrap mb-1">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <ValidationProvider name="CodigoEFACE" rules="max:33" v-slot="{ errors }">
                        <label style="font-size:12px">Codigo EFACE</label>
                        <vs-input v-model="compra.CodigoEFACE" type="text" class="w-full" :danger="errors.length > 0"
                            :danger-text="(errors.length > 0) ? errors[0] : null"
                            :disabled="verCompra || bloquearConta" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <ValidationProvider name="CuentaAsociada">
                        <label style="font-size:12px">Cuenta Asociada Intregración</label>
                        <v-select v-model="selectCuentaAsociada" label="MostrarLabel" :options="listaCuentasAsociadas"
                            :clearable="false"
                            :disabled="verCompra || bloquearConta" /><!-- @input="Otros().OcultarOrdenCompra()" -->
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1"
                    v-if="!selectCuentaAsociada.SinOrdenCompra && compra.Tipo == 'F' && !verCajaChica && !verCompra && !editarCompra">
                    <SM-Buscar label="Orden de compra" api="app/v2_api_compras/ListarOrdenesCompra"
                        :api_filtro="{ Proveedor: proveedor.CODIGO }" v-model="compra.OrdenAutorizada"
                        :api_campos="['Codigo', 'Proveedor', 'Nombre', 'Validacion', 'Monto']"
                        :api_titulos="['Código', 'Proveedor', 'Nombre', 'Validación', 'Monto']"
                        api_campo_respuesta="Codigo" :api_preload="true" :callback_buscar="Consulta().InfoOrdenCompra"
                        :disabled_texto="compra.Documento == '' || compra.Documento == null || verCompra || bloquearConta"
                        :mostrar_busqueda="compra.Documento != '' && compra.Documento != null && !verCompra"
                        ref="OrdenAutorizada" />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1" v-if="compra.Tipo == 'N' && !verCajaChica">
                    <SM-Buscar label="Documento que afecta (NC)" api="app/v2_api_compras/BuscarDocumentoAfectar"
                        :api_filtro="{ Proveedor: proveedor.CODIGO }" v-model="compra.DocumentoQueAfecta"
                        :api_campos="['Proveedor', 'Documento', 'Tipo', 'Fecha', 'Valor', 'Saldo']" :api_preload="true"
                        :api_titulos="['#Proveedor', 'Documento', '#Tipo', '#Fecha', '#Valor', '#Saldo']"
                        api_campo_respuesta="Documento" :callback_buscar="Consulta().DocumentoQueAfecta"
                        :disabled_texto="compra.Documento == '' || compra.Documento == null || verCompra || editarCompra || bloquearConta"
                        :mostrar_busqueda="compra.Documento != '' && compra.Documento != null && !verCompra && !editarCompra"
                        ref="DocumentoQueAfecta" />
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1"
                    v-if="!selectCuentaAsociada.SinOrdenCompra && compra.Tipo == 'F' && !verCajaChica && (verCompra || editarCompra)">
                    <ValidationProvider name="Ordendeompra" v-slot="{ errors }">
                        <label style="font-size:12px">Orden de compra</label>
                        <vs-input v-model="compra.OrdenAutorizada" type="text" class="w-full"
                            :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                            disabled />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1"
                    v-if="!selectCuentaAsociada.SinOrdenCompra && compra.Tipo == 'F' && !verCajaChica">
                    <ValidationProvider name="Validacion" v-slot="{ errors }">
                        <label style="font-size:12px">Validación</label>
                        <vs-input v-model="compra.Validacion" type="text" class="w-full" :danger="errors.length > 0"
                            :danger-text="(errors.length > 0) ? errors[0] : null" disabled />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1" v-if="compra.Tipo == 'N' && !verCajaChica">
                    <ValidationProvider name="Saldo" v-slot="{ errors }">
                        <label style="font-size:12px">Saldo</label>
                        <vs-input v-model="DocumentoAfecta.Saldo" type="text" class="w-full" :danger="errors.length > 0"
                            :danger-text="(errors.length > 0) ? errors[0] : null" disabled />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1" v-if="editarCompra">
                    <vs-button v-show="!verCajaChica" class="w-full mr-1 mt-5" title="Ver caja chica"
                        @click="Otros().VerCajaChica(true)">Caja
                        Chica
                    </vs-button>
                    <vs-button v-show="verCajaChica" class="w-full mr-1 mt-5" color="danger" title="Ocultar caja chica"
                        @click="Otros().VerCajaChica(false)">Ocultar Caja
                        Chica
                    </vs-button>
                </div>
            </div>
            <vs-divider v-if="verCajaChica">Caja Chica</vs-divider>
            <div class="flex flex-wrap mb-1" v-if="verCajaChica">
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <ValidationProvider name="Caja">
                        <label style="font-size:12px">Caja</label>
                        <v-select v-model="selectCajaChica" label="NombreEmpleado" :options="listaCajaChica"
                            :clearable="false" @input="Consulta().ListarLotes()" :disabled="verCompra" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <ValidationProvider name="Lote">
                        <label style="font-size:12px">Lote</label>
                        <v-select v-model="selectLote" label="Siguiente"
                            :options="!lotesCerrados ? this.listaLotes.filter(lote => parseFloat(lote.Saldo) !== 0) : this.listaLotes"
                            :clearable="false" @input="Otros().CambiarLote()"
                            :disabled="verCompra || selectCajaChica.Codigo == '0'" />
                    </ValidationProvider>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <div class="flex flex-wrap mb-1" v-if="verCajaChica || verCompra">
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                            <vs-checkbox :disabled="verCompra || selectCajaChica.Codigo == '0'" v-model="lotesCerrados"
                                @input="Otros().MostrarLotesCerrados()">Lotes cerrados</vs-checkbox>
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1" style="text-align: center;">
                            <label style="font-size:12px">Documentos: {{ LoteDocumentos.Documentos }}</label>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1" v-if="LoteDocumentos.HayPagos > 0">
                        <div class="w-full" style="text-align: center;">
                            <label style="font-size:12px; color: red; font-weight: bold;">LA CAJA {{
                                selectLote.Siguiente }} SE
                                ENCUENTRA
                                CERRADA</label>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <div class="flex flex-wrap mb-1" v-if="verCajaChica || !verCompra">
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                            <vs-button class="w-full mr-1 mt-5" title="Nuevo Lote" color="success"
                                :disabled="selectCajaChica.Codigo == '0' || Object.keys(this.selectLote).length == 0"
                                @click="Insertar().NuevoLote()">Nuevo Lote
                            </vs-button>
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                            <vs-button class="w-full mr-1 mt-5" title="Asignar Lote" :disabled="compra.CajaChica != 0"
                                @click="Modificar().ModificarLote(true)">Asignar
                            </vs-button>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-1">
                    <div class="flex flex-wrap mb-1" v-if="verCajaChica || !verCompra">
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                            <vs-button class="w-full mr-1 mt-5" title="Des-asignar Lote"
                                :disabled="compra.CajaChica == 0" @click="Modificar().ModificarLote()">Des-asignar
                            </vs-button>
                        </div>
                        <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                            <vs-button class="w-full mr-1 mt-5" title="Imprimir"
                                :disabled="LoteDocumentos.Documentos == 0" @click="Otros().VentanaAdicional(2)">Imprimir
                            </vs-button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-wrap mb-1">
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <vs-divider style="font-size:17px">Valores de compra</vs-divider>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="Valor" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Valor</label>
                                <vs-input v-model="compra.Valor" type="number" class="w-full" id="valorCompra"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="verCompra || compra.Documento == null || compra.Documento == '' || bloquearCampo || listaCuentas.length > 0 || bloquearConta"
                                    ref="Valor" @blur="Otros().CalcularValores()" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="Tasa" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Tasa</label>
                                <vs-input v-model="compra.Tasa" type="number" class="w-full" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="verCompra || bloquearConta" @blur="Otros().CalcularTasa(true)" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="Dolares" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Dólares</label>
                                <vs-input v-model="compra.Dolares" type="number" class="w-full"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="verCompra || bloquearConta" @blur="Otros().CalcularTasa(false)" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="ServiciosAfectos" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Servicios Afectos</label>
                                <vs-input v-model="compra.ServiciosAfectos" type="number" class="w-full"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="verCompra || bloquearCampo || bloquearConta"
                                    @blur="Otros().CalcularMSAfectaExit('ServiciosAfectos', 'MercaderiaAfecta')"
                                    @keyup.enter="Otros().CalcularMSAfecta('ServiciosAfectos', 'MercaderiaAfecta')" />
                                <!--  -->
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="MercaderiaAfecta" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Mercadería Afecta</label>
                                <vs-input v-model="compra.MercaderiaAfecta" type="number" class="w-full"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="verCompra || bloquearCampo || bloquearConta"
                                    @blur="Otros().CalcularMSAfectaExit('ServiciosAfectos', 'MercaderiaAfecta')"
                                    @keyup.enter="Otros().CalcularMSAfecta('MercaderiaAfecta', 'ServiciosAfectos')" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="TotalImpuestos" v-slot="{ errors }">
                                <label style="font-size:12px">Total Impuestos</label>
                                <vs-input v-model="compra.TotalImpuestos" type="number" class="w-full"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    disabled />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="ServiciosNoAfectos" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Servicios NO Afectos</label>
                                <vs-input v-model="compra.ServiciosNoAfectos" type="number" class="w-full"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    @keyup.enter="Otros().CalcularMSNoAfecta('ServiciosNoAfectos', 'MercaderiaNoAfecta')"
                                    @blur="Otros().CalcularMSAfectaExit('ServiciosNoAfectos', 'MercaderiaNoAfecta')"
                                    :disabled="verCompra || bloquearCampo || bloquearConta" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="MercaderiaNoAfecta" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Mercadería NO Afecta</label>
                                <vs-input v-model="compra.MercaderiaNoAfecta" type="number" class="w-full"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    @keyup.enter="Otros().CalcularMSNoAfecta('MercaderiaNoAfecta', 'ServiciosNoAfectos')"
                                    @blur="Otros().CalcularMSAfectaExit('ServiciosNoAfectos', 'MercaderiaNoAfecta')"
                                    :disabled="verCompra || bloquearCampo || bloquearConta" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="Saldo" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Saldo</label>
                                <vs-input v-model="compra.Saldo" type="number" class="w-full"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="verCompra || bloquearConta" />
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="flex flex-wrap mb-1">
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="ValorRetencionISR" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Ret. ISR</label>
                                <vs-input v-model="compra.ValorRetencionISR" type="number" class="w-full"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="verCompra || bloquearCampo || compra.ValorRetencionISR == 0 || bloquearConta" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <ValidationProvider name="ValorRetencionIVA" v-slot="{ errors }" rules="numero_decimal:2">
                                <label style="font-size:12px">Ret. IVA</label>
                                <vs-input v-model="compra.ValorRetencionIVA" type="number" class="w-full"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                    :disabled="verCompra || bloquearCampo || compra.ValorRetencionIVA == 0 || bloquearConta" />
                            </ValidationProvider>
                        </div>
                        <div class="w-full md:w-1/3 lg:w-1/3 xl:w-1/3 p-1">
                            <div class="flex flex-wrap mb-1">
                                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1" v-if="compra.TieneNotas > 0">
                                    <vs-button class="w-full mr-1 mt-5" title="Ver notas de credito" icon-pack="fas"
                                        icon="fa-eye" @click="Otros().VentanaAdicional(1)">
                                    </vs-button>
                                </div>
                                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1" v-if="compra.TienePagos > 0">
                                    <vs-button class="w-full mr-1 mt-5" title="Ver pagos" icon-pack="fas"
                                        color="warning" icon="fa-dollar-sign" @click="Otros().VentanaAdicional(3)">
                                    </vs-button>
                                </div>
                                <div class="w-full md:w-1/6 lg:w-1/6 xl:w-1/6 p-1" v-if="compra.Status == 'R'">
                                    <vs-button class="w-full mr-1 mt-5" title="Imprimir Partida" icon-pack="fas"
                                        color="success" icon="fa-print" @click="Otros().ImprimirPartida()">
                                    </vs-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 lg:w-1/2 xl:w-1/2 p-1">
                    <div class="flex flex-wrap mb-1">
                        <vs-divider style="font-size:17px">Impuestos</vs-divider>
                        <div class="w-full  p-1">
                            <div class="content content-pagex">
                                <vs-table2 :data="listaImpuestos">
                                    <template slot="thead">
                                        <th>Nombre</th>
                                        <th>Monto</th>
                                    </template>
                                    <template slot-scope="{data}">
                                        <tr :key="indextr" v-for="(tr, indextr) in data">
                                            <vs-td2>
                                                {{ tr.Nombre }}
                                            </vs-td2>
                                            <vs-td2>
                                                {{ (tr.Monto != '' ? new
                                                    Intl.NumberFormat('es-GT', {
                                                        style: 'currency',
                                                        currency: 'GTQ',
                                                    }).format(parseFloat(tr.Monto).toFixed(2)) :
                                                    null) }}
                                            </vs-td2>
                                        </tr>
                                    </template>
                                </vs-table2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-wrap mb-1">
                <div class="w-full p-1">
                    <ValidationProvider name="Observaciones" v-slot="{ errors }">
                        <label style="font-size:12px">Observaciones</label>
                        <vs-textarea counter="150" :counter-danger.sync="counterDanger" class="w-full"
                            style="height: 100px;" rows="3" v-model="compra.Observaciones" :danger="errors.length > 0"
                            ref="iComentario" :danger-text="(errors.length > 0) ? errors[0] : null" required
                            :disabled="verCompra || bloquearConta" />
                    </ValidationProvider>
                </div>
            </div>
            <div class="flex flex-wrap mb-1">
                <div class="w-full  p-1"><!-- md:w-4/5 lg:w-4/5xl:w-4/5 -->
                    <vs-divider style="font-size:17px">Partida Contable</vs-divider>
                    <div class="content content-pagex">
                        <DxDataGrid :ref="gridDetalle" :show-borders="true" :data-source="listaCuentas"
                            :allow-column-reordering="false" :allow-column-resizing="true"
                            @editor-preparing="overrideOnEditorPreparing" @init-new-row="onInitNewRow"
                            :selected-row-keys="selectedItemKeys" @selection-changed="onSelectionChanged">
                            <!-- Habilitar la edición de celdas -->
                            <DxDataGridEditing mode="cell" :allow-updating="editarCompra" :allow-adding="editarCompra"
                                :allow-deleting="editarCompra" :use-icons="true" />
                            <!--  <DxDataGridEditing mode="cell" :allow-updating="true" :allow-adding="true"
                                :allow-deleting="true" :use-icons="true" /> -->
                            <DxDataGridSelection :mode="seleccion" />
                            <DxDataGridDxSorting mode="none" alignment="center" />
                            <DxDataGridColumn data-field="Cuenta" :width="200" alignment="center" />
                            <DxDataGridColumn data-field="NombreCuenta" caption="Nombre Cuenta" alignment="center"
                                :allow-editing="false" />
                            <DxDataGridColumn data-field="Debe" :width="200" data-type="number"
                                :customize-text="customizeTextValores" alignment="center" :format="{
                                    formatter: (value) =>
                                        `Q ${value.toLocaleString('en-US', {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2,
                                        })}`,
                                }" />
                            <DxDataGridColumn data-field="Haber" :width="200" data-type="number"
                                :customize-text="customizeTextValores" alignment="center" :format="{
                                    formatter: (value) =>
                                        `Q ${value.toLocaleString('en-US', {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2,
                                        })}`,
                                }" />
                            <DxDataGridToolbar>
                                <DxDataGridItem name="addRowButton" show-text="always" :disabled="!editarCompra" />
                                <DxDataGridItem location="after">
                                    <template #default>
                                        <DxButton icon="trash" text="Eliminar elementos seleccionados"
                                            :disabled="!selectedItemKeys.length || !editarCompra"
                                            @click="deleteRecords" />
                                    </template>
                                </DxDataGridItem>
                            </DxDataGridToolbar>
                            <!--  <DxDataGridColumn data-field="Sucursal" alignment="center" :width="125" /> -->
                            <DxDataGridSummary>
                                <DxDataGridTotalItem column="Debe" summary-type="sum"
                                    :value-format="{ type: 'fixedPoint', precision: 2 }"
                                    :customize-text="customizeTextValores" />
                                <DxDataGridTotalItem column="Haber" summary-type="sum"
                                    :value-format="{ type: 'fixedPoint', precision: 2 }"
                                    :customize-text="customizeTextValores" />
                            </DxDataGridSummary>

                        </DxDataGrid>
                    </div>
                </div>
            </div>
            <div class="flex flex-wrap mb-1">
                <vs-divider></vs-divider>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1" v-if="this.editarCompra || this.crearCompra">
                    <vs-button class="w-full mr-1 mt-5" title="Guardar compra" color="success"
                        @click="Otros().ValidarCompra()">
                        Guardar
                    </vs-button>
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"
                    v-if="this.compra.Status !== 'A' && this.compra.Status !== 'R' && this.verCompra">
                    <vs-button class="w-full mr-1 mt-5" title="Editar compra" color="success"
                        @click="Consulta().DetalleCompra(compra, false, true)">
                        Editar
                    </vs-button>
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"
                    v-if="(this.compra.Status == 'P' || this.compra.Status == 'F' || this.compra.Status == 'I' || this.compra.Status == 'N') && !this.editarCompra">
                    <vs-button class="w-full mr-1 mt-5" title="Bloquar partida" color="warning" icon-pack="fas"
                        icon="fa-lock" @click="Otros().RevisarPartida(compra.Status)">
                        Bloquear
                    </vs-button>
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"
                    v-if="this.compra.Status == 'R' && !this.editarCompra">
                    <vs-button class="w-full mr-1 mt-5" title="Debloquear partida" color="warning" icon-pack="fas"
                        icon="fa-unlock" @click="Otros().RevisarPartida(compra.Status)">
                        Desbloquear
                    </vs-button>
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"
                    v-if="this.compra.Status !== 'R' && this.compra.Status !== 'A' && this.verCompra">
                    <vs-button class="w-full mr-1 mt-5" title="Anular compra" color="danger"
                        @click="Otros().AnularCompra()">
                        Anular
                    </vs-button>
                </div>
                <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                    <vs-button class="w-full mr-1 mt-5" @click="Otros().AbrirVentana(true)">
                        Salir
                    </vs-button>
                </div>
            </div>
        </vx-card>
        <vs-popup :title="ventanaAdicional.titulo" v-show="ventanaAdicional.mostrar"
            :active.sync="ventanaAdicional.mostrar" style="z-index:99998;width:115%">
            <div class="content content-pagex" v-show="ventanaAdicional.opcion == 1">
                <vs-table2 max-items="10" search pagination :data="listaNotasXDocumento">
                    <template slot="thead">
                        <th order="Documento">Documento</th>
                        <th order="Tipo">Tipo</th>
                        <th order="Fecha">Fecha</th>
                        <th order="Valor">Valor</th>
                        <th order="Observaciones">Observaciones</th>
                    </template>
                    <template slot-scope="{data}">
                        <tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td2>
                                {{ tr.Documento }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Tipo }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Fecha }}
                            </vs-td2>
                            <vs-td2>
                                {{ new Intl.NumberFormat('es-GT', {
                                    style: 'currency',
                                    currency: 'GTQ',
                                }).format(parseFloat(tr.Valor).toFixed(2))
                                }}
                            </vs-td2>
                            <vs-td2>
                                {{ tr.Observaciones }}
                            </vs-td2>
                        </tr>
                    </template>
                </vs-table2>
            </div>
            <div class="content content-pagex" v-show="ventanaAdicional.opcion == 2">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"></div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"></div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"></div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                        <vs-button class="w-full mr-1 mt-5" title="Imprimir lote" color="success"
                            @click="Otros().Reporte()"><!-- @click="Otros().AbrirVentana()" -->
                            Imprimir
                        </vs-button>
                    </div>
                    <vs-divider></vs-divider>
                    <div class="w-full p-1">
                        <vs-table2 max-items="10" search pagination :data="listaDocumentoXLote" tooltip>
                            <template slot="thead">
                                <th order="Documento">Proveedor</th>
                                <th order="Nombre">Nombre</th>
                                <th order="Documento">Documento</th>
                                <th order="Tipo">Tipo</th>
                                <th order="Observaciones">Periodo</th>
                                <th order="Fecha">Fecha</th>
                                <th order="Valor">Valor</th>
                                <th order="Saldo">Saldo</th>
                                <th order="Observaciones">Observaciones</th>
                                <th order="Registro">Registro</th>
                                <th order="CuentaContable">CuentaContable</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.Proveedor }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Nombre }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Documento }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Tipo }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Periodo }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Fecha }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ new Intl.NumberFormat('es-GT', {
                                            style: 'currency',
                                            currency: 'GTQ',
                                        }).format(parseFloat(tr.Valor).toFixed(2))
                                        }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ new Intl.NumberFormat('es-GT', {
                                            style: 'currency',
                                            currency: 'GTQ',
                                        }).format(parseFloat(tr.Saldo).toFixed(2)
                                        ) }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Observaciones }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Registro }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.CuentaContable }}
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
            </div>
            <div class="content content-pagex" v-show="ventanaAdicional.opcion == 3">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full p-1">
                        <vs-table2 max-items="10" search pagination :data="listaDocumentoPagos" tooltip>
                            <template slot="thead">
                                <th order="Documento">Cuenta Banco</th>
                                <th order="Nombre">Tipo</th>
                                <th order="Documento">Número</th>
                                <th order="Tipo">Fecha</th>
                                <th order="Observaciones">Monto</th>
                            </template>
                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.CuentaBanco }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.TipoMovto }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NumeroMovto }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Fecha }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ new Intl.NumberFormat('es-GT', {
                                            style: 'currency',
                                            currency: 'GTQ',
                                        }).format(parseFloat(tr.Monto).toFixed(2))
                                        }}
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>
                </div>
            </div>
            <div class="content content-pagex" v-show="ventanaAdicional.opcion == 4">
                <div class="flex flex-wrap mb-1">
                    <div class="w-full p-1">
                        <vs-input v-model="razon" type="text" class="w-full" label="Razón de anulación" />
                    </div>
                </div>
                <div class="flex flex-wrap mb-1">
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"></div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"></div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1"></div>
                    <div class="w-full md:w-1/4 lg:w-1/4 xl:w-1/4  p-1">
                        <vs-button class="w-full mr-1 mt-5" title="Anular compra" color="success"
                            @click="Otros().acceptAlert()" :disabled="razon == ''">
                            Anular
                        </vs-button>
                    </div>
                </div>
            </div>
        </vs-popup>
    </div>
</template>

<script>
const gridDetalle = "gridDetalle";

import vSelect from 'vue-select'
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import {
    Spanish as SpanishLocale
} from 'flatpickr/dist/l10n/es.js';
import bitacora from '@/components/sermesa/funciones/SMBitacora'

export default {
    name: 'Compras',

    /*created() {

    },*/
    components: {
        'v-select': vSelect,
        flatPickr
    },
    data() {
        return {
            gridDetalle,
            constantes: {},
            ctrlFechaPCCreditoFiscal: new Date("2012-04-01"),
            titulo: "Compras",
            verCompra: false,
            editarCompra: false,
            crearCompra: false,
            proveedor: { NIT: '', ACTIVO: 'N' },
            proveedorExcento: { ExcentoRetencion: false, ExcentoRetenISR: false },
            empresaRetencion: { DebeRetenerIVA: true },
            listaCompras: [],
            listaSucursales: [],
            selectSucursal: { Codigo: null },
            listaPeriodos: [],
            selectPeriodo: { Codigo: null, Descripcion: null, ComprasBlockeo: null },
            listaCuentasAsociadas: [],
            selectCuentaAsociada: {},
            listaImpuestos: [],
            listaCuentas: [],
            ventana: false,
            ventanaAdicional: { titulo: "", mostrar: false, opcion: 0 },
            tituloVentana: "",
            counterDanger: false,
            compra: {},
            aplicaAnticipo: true,
            documentoEsAfectoRetencion: true,
            periodoValido: true,
            fechaVencimientoValida: true,
            configFromdateTimePicker: {
                locale: SpanishLocale,
                dateFormat: "d-m-Y",
                allowInput: true
            },
            listaNotasXDocumento: [],
            radioOptions: [{ Codigo: 'F', Descripcion: 'Factura', Mostrar: true, DocumentoEsAfectoRetencion: true }, { Codigo: 'R', Descripcion: 'Recibo', Mostrar: true, DocumentoEsAfectoRetencion: false },
            { Codigo: 'N', Descripcion: 'Nota/Créd', Mostrar: true, DocumentoEsAfectoRetencion: false }, { Codigo: 'P', Descripcion: 'Poliza Import.', Mostrar: false },
            { Codigo: 'E', Descripcion: 'Factura Esp.', Mostrar: false }, { Codigo: 'M', Descripcion: 'Factura Med.', Mostrar: false },
            { Codigo: 'H', Descripcion: 'Interpretación', Mostrar: false }, { Codigo: 'C', Descripcion: 'Referencia', Mostrar: false },
            { Codigo: 'U', Descripcion: 'Cupones', Mostrar: false }, { Codigo: 'S', Descripcion: 'Servicios Profesionales', Mostrar: false },
            { Codigo: 'G', Descripcion: 'Compras de multiniveles', Mostrar: false }, { Codigo: 'Y', Descripcion: 'Devolución Consignación', Mostrar: false },
            { Codigo: 'Z', Descripcion: 'Envío Consignación', Mostrar: false }, { Codigo: 'X', Descripcion: 'Compromiso Pago COEX', Mostrar: false },
            { Codigo: 'A', Descripcion: 'Nota/Abono', Mostrar: false }, { Codigo: 'L', Descripcion: 'Lotes de Honorarios', Mostrar: false }
            ],
            DocumentoAfecta: {},
            //Caja Chica
            verCajaChica: false,
            listaCajaChica: [],
            selectCajaChica: {},
            listaLotes: [],
            selectLote: {},
            lotesCerrados: false,
            LoteDocumentos: { Documentos: 0 },
            listaDocumentoXLote: [],
            listaDocumentoPagos: [],
            documentoExiste: false,
            bloquearCampo: false,
            //proveedor: null
            activePrompt: false,
            razon: '',
            ordenCompra: {},
            columns: ['Cuenta', 'Nombre Cuenta', 'Debe', 'Haber', 'Sucursal'],
            polizaCorrecta: true,
            selectedItemKeys: [],
            seleccion: 'none',
            bloquearConta: false
        };
    },
    mounted() {
        this.Consulta().ListarConstantes()

        document.addEventListener("keydown", (e) => {
            if (e.key == 'Escape' && !this.ayuda && window.location.pathname == '/CONTABILIDAD/CON014' && this.ventana) {
                this.ventana = false
                this.Consulta().ListarCompras(this.proveedor)
            }
            if (e.key == 'Insert' && window.location.pathname == '/CONTABILIDAD/CON014' && !this.ventana) {
                this.Otros().AbrirVentana()
            }
        })
    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        },
        Valor: function () {
            return this.$refs["Valor"];
        }
    },
    watch: {
        'compra.Tipo'() {
            this.Consulta().ListarCuentasAsociadas()
            this.selectCuentaAsociada = {}
            this.listaImpuestos = []
            this.compra.TotalImpuestos = 0

            this.radioOptions.forEach(element => {
                if (element.Codigo == this.compra.Tipo) { this.documentoEsAfectoRetencion = element.DocumentoEsAfectoRetencion }
            })

            if (this.compra.Tipo !== 'F') {
                if (this.compra.AplicaAnticipos) {
                    this.compra.AplicaAnticipos = !this.compra.AplicaAnticipos
                }
            }
            
            this.Otros().CalcularValores()

        },
        'compra.Valor'() {
            if (this.compra.Valor == '' || this.compra.Valor == null || Number.isNaN(this.compra.Valor)) {
                this.compra.Valor = 0
            }
            /* if (this.compra.Valor < 0 && this.compra.Tipo !== 'N') {
                this.$vs.dialog({
                    type: 'confirm',
                    color: 'danger',
                    title: 'Confirmación',
                    text: 'Hola....'
                })
            } */
        },
        'compra.ServiciosAfectos'() {
            if (this.compra.ServiciosAfectos == '' || this.compra.ServiciosAfectos == null || Number.isNaN(this.compra.ServiciosAfectos)) {
                this.compra.ServiciosAfectos = 0
            }
        },
        'compra.MercaderiaAfecta'() {
            if (this.compra.MercaderiaAfecta == '' || this.compra.MercaderiaAfecta == null || Number.isNaN(this.compra.MercaderiaAfecta)) {
                this.compra.MercaderiaAfecta = 0
            }
        },
        'compra.ServiciosNoAfectos'() {
            if (this.compra.ServiciosNoAfectos == '' || this.compra.ServiciosNoAfectos == null || Number.isNaN(this.compra.ServiciosNoAfectos)) {
                this.compra.ServiciosNoAfectos = 0
            }
        },
        'compra.MercaderiaNoAfecta'() {
            if (this.compra.MercaderiaNoAfecta == '' || this.compra.MercaderiaNoAfecta == null || Number.isNaN(this.compra.MercaderiaNoAfecta)) {
                this.compra.MercaderiaNoAfecta = 0
            }
        },
        'compra.Documento'() {
            if (this.compra.Documento.length == 0) {
                this.DocumentoAfecta = {}
                this.compra.OrdenAutorizada = null
                this.compra.Validacion = null
            }
        }
    },
    methods: {
        onInitNewRow(e) {
            e.data.Cuenta = ""
            e.data.Debe = 0;
            e.data.Haber = 0;
            e.data.Indice = this.listaCuentas[this.listaCuentas.length - 1].Indice + 1
        },
        customizeTextValores(cellInfo) {
            if (cellInfo.value !== null) {
                return new
                    Intl.NumberFormat('es-GT', {
                        style: 'currency',
                        currency: 'GTQ',
                    }).format(parseFloat(cellInfo.value).toFixed(2))
            }
            return "Q. 0.00"; // Si el valor es null, devolver "Q. 0"
        },
        overrideOnEditorPreparing(e) {
            if (e.dataField === "Cuenta" && e.parentType === "dataRow") {
                const defaultValueChangeHandler = e.editorOptions.onValueChanged;

                e.editorOptions.onValueChanged = (args) => {
                    const cuenta = args.value; // Captura el valor ingresado de la cuenta
                    this.polizaCorrecta = true

                    if (this.polizaCorrecta) {
                        this.axios
                            .post('/app/v2_api_compras/BuscarCuenta', {
                                Cuenta: cuenta
                            })
                            .then((resp) => {
                                if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                    const updatedNombre = resp.data.json[0].Nombre;
                                    const rowIndex = e.row.rowIndex;

                                    this.$refs.gridDetalle.instance.cellValue(
                                        rowIndex,
                                        "NombreCuenta",
                                        updatedNombre
                                    );

                                    this.$refs.gridDetalle.instance.refresh();
                                } else {
                                    this.polizaCorrecta = false
                                    this.Otros().Notificacion("Poliza", "Cuenta no encontrada.", "danger")
                                }
                            })
                            .catch(() => {
                            });
                    }

                    defaultValueChangeHandler(args);
                };
            }
        },
        deleteRecords() {
            this.selectedItemKeys.forEach(e => {
                let indice = this.listaCuentas.indexOf(e)
                this.listaCuentas.splice(indice, 1);
            })

            this.$refs.gridDetalle.instance.refresh();
        },
        onSelectionChanged(e) {
            this.selectedItemKeys = e.selectedRowKeys
        },
        Consulta: function () {
            return {
                ListarConstantes: () => {
                    this.axios.post('/app/v2_api_compras/Constantes', {})
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.constantes = resp.data.json[0];
                                this.Otros().EmpresaRetencion()
                            }
                        })
                },
                ListarCompras: (obj) => {
                    this.proveedor = obj
                    this.titulo = "Compras / " + this.proveedor.NOMBRE + " - " + this.proveedor.NIT
                    this.Otros().ProveedorExcento()

                    if (this.ventana) {
                        this.Otros().AbrirVentana(true)
                    }

                    this.axios.post('/app/v2_api_compras/ListarCompras', { "Proveedor": this.proveedor.CODIGO })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.listaCompras = []
                                this.listaCompras = resp.data.json;
                            } else {
                                this.listaCompras = [];
                            }
                        })
                },
                DetalleCompra: (obj, verCompra = false, editar = false) => {
                    this.verCompra = verCompra
                    this.editarCompra = editar
                    this.bloquearCampo = false
                    this.ventana = true;
                    this.verCajaChica = false;
                    this.crearCompra = false
                    this.bloquearConta = false
                    this.editarCompra ? this.seleccion = 'multiple' : this.seleccion = 'none'

                    if (this.editarCompra && this.compra.Tipo !== 'N' && this.compra.Tipo !== 'F' && this.compra.Tipo !== 'R') {
                        this.bloquearConta = true
                    }

                    this.axios.post('/app/v2_api_compras/ListarCompras', { "Proveedor": this.proveedor.CODIGO, "Documento": obj.Documento })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.compra = resp.data.json[0];
                                this.tituloVentana = "Detalle de compra " + this.compra.Documento
                                this.Consulta().ListarCuentasAsociadas()
                                this.Consulta().ListarComprasImpuestos()
                                this.Consulta().ListarComprasPartida()
                                this.Consulta().ListarSucursales()
                                this.Consulta().ListarPeriodos()
                                this.Consulta().ListarCajasChicas()
                                this.Consulta().ListarLotes()

                                if (this.editarCompra) this.Otros().CambiarLote()

                                this.selectSucursal.Codigo = this.compra.Hospital
                                this.selectPeriodo = { Codigo: this.compra.Periodo, Descripcion: this.compra.PeriodoDescripcion, ComprasBlockeo: this.compra.ComprasBlockeo }
                                this.compra.CreditoContado == 'C' ? this.compra.CreditoContado = true : this.compra.CreditoContado = false
                                this.compra.AplicaAnticipos == 'S' ? this.compra.AplicaAnticipos = true : this.compra.AplicaAnticipos = false

                                this.compra.Valor = parseFloat(this.compra.Valor).toFixed(2)
                                this.compra.Anticipos = parseFloat(this.compra.Anticipos).toFixed(2)

                                this.compra.Tasa != '' ? this.compra.Tasa = parseFloat(this.compra.Tasa).toFixed(2) : this.compra.Tasa = null
                                this.compra.Dolares != '' ? this.compra.Dolares = parseFloat(this.compra.Dolares).toFixed(2) : this.compra.Dolares = null

                                this.compra.ServiciosAfectos = parseFloat(this.compra.ServiciosAfectos).toFixed(2)
                                this.compra.MercaderiaAfecta = parseFloat(this.compra.MercaderiaAfecta).toFixed(2)
                                this.compra.TotalImpuestos = parseFloat(this.compra.TotalImpuestos).toFixed(2)
                                this.compra.ServiciosNoAfectos = parseFloat(this.compra.ServiciosNoAfectos).toFixed(2)
                                this.compra.MercaderiaNoAfecta = parseFloat(this.compra.MercaderiaNoAfecta).toFixed(2)
                                this.compra.Saldo = parseFloat(this.compra.Saldo).toFixed(2)
                                this.compra.ValorRetencionIVA = parseFloat(this.compra.ValorRetencionIVA).toFixed(2)
                                this.compra.ValorRetencionISR = parseFloat(this.compra.ValorRetencionISR).toFixed(2)
                                this.compra.DocumentoAnterior = this.compra.Documento
                                this.compra.PeriodoAnterior = this.compra.Periodo

                                this.radioOptions = [{ Codigo: 'F', Descripcion: 'Factura', Mostrar: true, DocumentoEsAfectoRetencion: true }, { Codigo: 'R', Descripcion: 'Recibo', Mostrar: true, DocumentoEsAfectoRetencion: false },
                                { Codigo: 'N', Descripcion: 'Nota/Créd', Mostrar: true, DocumentoEsAfectoRetencion: false }, { Codigo: 'P', Descripcion: 'Poliza Import.', Mostrar: false },
                                { Codigo: 'E', Descripcion: 'Factura Esp.', Mostrar: false }, { Codigo: 'M', Descripcion: 'Factura Med.', Mostrar: false },
                                { Codigo: 'H', Descripcion: 'Interpretación', Mostrar: false }, { Codigo: 'C', Descripcion: 'Referencia', Mostrar: false },
                                { Codigo: 'U', Descripcion: 'Cupones', Mostrar: false }, { Codigo: 'S', Descripcion: 'Servicios Profesionales', Mostrar: false },
                                { Codigo: 'G', Descripcion: 'Compras de multiniveles', Mostrar: false }, { Codigo: 'Y', Descripcion: 'Devolución Consignación', Mostrar: false },
                                { Codigo: 'Z', Descripcion: 'Envío Consignación', Mostrar: false }, { Codigo: 'X', Descripcion: 'Compromiso Pago COEX', Mostrar: false },
                                { Codigo: 'A', Descripcion: 'Nota/Abono', Mostrar: false }, { Codigo: 'L', Descripcion: 'Lotes de Honorarios', Mostrar: false }
                                ]

                                this.radioOptions.forEach(element => {
                                    if (this.compra.Tipo == element.Codigo && !element.Mostrar) {
                                        element.Mostrar = true
                                    }
                                })

                                if (this.editarCompra) {
                                    this.compra.ValorAnterior = this.compra.Valor
                                    if (this.selectPeriodo.ComprasBlockeo == 'S') {
                                        this.verCompra = true
                                        this.Otros().Notificacion("Editar compra", "No se puede editar, ya que el periodo se encuentra cerrado", "danger")
                                    }

                                    if ((this.compra.Tipo == 'F' && this.compra.OrdenAutorizada != '') || this.compra.AplicaAnticipos || this.compra.TienePagos > 0) {
                                        this.bloquearCampo = true
                                    }
                                }

                                if (this.verCompra) {
                                    setTimeout(() => {
                                        bitacora.registrar(this.compra)
                                    }, 2000)
                                }
                            }
                        })
                },
                ListarSucursales: () => {
                    this.axios.post('/app/v2_api_compras/ListarSucursales', {})
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.listaSucursales = resp.data.json;

                                if (!this.verCompra) {
                                    this.listaSucursales.forEach(element => {
                                        if (element.Codigo == this.sesion.sesion_sucursal) {
                                            this.selectSucursal = element
                                        }
                                    });
                                }
                            }
                        })
                },
                ListarPeriodos: (obj = {}) => {
                    return new Promise((resolve, reject) => {
                        this.axios.post('/app/v2_api_compras/ListarPeriodos', obj)
                            .then(resp => {
                                if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                    this.listaPeriodos = resp.data.json;

                                    if (!this.compra.hasOwnProperty("Periodo")) {
                                        this.Otros().CambiarPeriodo(true)
                                    }

                                    resolve();
                                }
                            }).catch((error) => {
                                reject(error);
                            })
                    });
                },
                ListarCuentasAsociadas: () => {
                    this.axios.post('/app/v2_api_compras/ListarCuentasAsociadas', { Tipo: this.compra.Tipo })
                        .then(resp => {
                            if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                this.listaCuentasAsociadas = resp.data.json;

                                if (!this.compra.hasOwnProperty("CuentaDef")) {
                                    this.compra.CuentaDef = '0'
                                }

                                this.listaCuentasAsociadas.forEach(element => {
                                    if (element.Codigo == this.compra.CuentaDef) {
                                        this.selectCuentaAsociada = element
                                    }
                                });
                            }
                        })
                },
                ListarComprasImpuestos: () => {
                    this.axios.post('/app/v2_api_compras/ListarComprasImpuestos', { "Proveedor": this.proveedor.CODIGO, "Documento": this.compra.Documento })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaImpuestos = resp.data.json;
                            }
                        })
                },
                ListarComprasPartida: () => {
                    this.axios.post('/app/v2_api_compras/ListarComprasPartida', { "Proveedor": this.proveedor.CODIGO, "Documento": this.compra.Documento })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaCuentas = resp.data.json;
                            }
                        })
                },
                BuscarAnticipos: () => {
                    this.aplicaAnticipo = true

                    this.axios.post('/app/v2_api_compras/BuscarAnticipos', { "Proveedor": this.proveedor.CODIGO })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.compra.Anticipos = resp.data.json[0].TotalAnticipos;

                                if (this.compra.Anticipos !== '') {
                                    this.compra.Anticipos = parseFloat(this.compra.Anticipos).toFixed(2)
                                    this.aplicaAnticipo = false
                                    this.compra.AplicaAnticipos = false
                                }
                            }
                        })
                },
                InfoOrdenCompra: (datos) => {
                    if (datos.Validacion != '') {
                        this.axios.post('/app/v2_api_compras/ValidarOrdenCompra', { "OrdenAutorizada": this.compra.OrdenAutorizada, "Documento": this.compra.Documento })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    if (resp.data.json[0].Cuantos == '0') {
                                        this.compra.Validacion = datos.Validacion
                                        this.compra.Valor = parseFloat(datos.Monto).toFixed(2)
                                        this.ordenCompra = datos
                                        document.getElementById("valorCompra").focus();
                                    } else {
                                        this.Otros().Notificacion("Orden de compra", "Orden de compra ya fue operada previamente", "danger")
                                    }
                                }
                            })
                    } else {
                        this.compra.OrdenAutorizada = null
                        this.compra.Validacion = null
                        this.compra.Valor = 0
                        this.Otros().Notificacion("Orden de compra", "Orden Pendiente de Validación", "danger")
                    }
                },
                DocumentoQueAfecta: (datos) => {
                    this.DocumentoAfecta = datos
                    this.DocumentoAfecta.Saldo = parseFloat(this.DocumentoAfecta.Saldo).toFixed(2)
                    document.getElementById("valorCompra").focus();

                    if (parseFloat(this.DocumentoAfecta.Saldo) <= 0) {
                        this.Otros().Notificacion("Factura sin saldo disponible", "Factura sin saldo disponible. No procede la nota.  Verifique", "danger")
                    }
                },
                ListarCajasChicas: () => {
                    this.axios.post('/app/v2_api_compras/ListarCajasChicas', {})
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.listaCajaChica = resp.data.json;

                                if (!this.compra.hasOwnProperty("CajaChica") || this.compra.CajaChica == '') {
                                    this.compra.CajaChica = '0'
                                }

                                this.listaCajaChica.forEach(element => {
                                    if (element.Codigo == this.compra.CajaChica) {
                                        this.selectCajaChica = element
                                    }
                                });
                            }
                        })
                },
                ListarLotes: () => {
                    if (this.compra.CajaChica !== '') {
                        this.axios.post('/app/v2_api_compras/ListarLotes', { Caja: (this.selectCajaChica.Codigo !== undefined) ? this.selectCajaChica.Codigo : this.compra.CajaChica })
                            .then(resp => {
                                this.LoteDocumentos.Documentos = 0
                                if (resp.data.codigo == 0) {
                                    this.listaLotes = resp.data.json;
                                    this.selectLote = {}
                                    this.lotesCerrados = false

                                    if (!this.compra.hasOwnProperty("CajaChicaLote") || this.selectCajaChica.Codigo == '0') {
                                        this.compra.CajaChicaLote = null
                                    }

                                    this.listaLotes.forEach(element => {
                                        if (element.Siguiente == this.compra.CajaChicaLote) {
                                            this.selectLote = element
                                        }
                                    });

                                    if (Object.keys(this.selectLote).length == 0) {
                                        this.selectLote.Siguiente = this.compra.CajaChicaLote
                                    }

                                    this.Otros().MostrarLotesCerrados()
                                }
                            })
                    }
                }

            }
        },
        Insertar: function () {
            return {
                NuevoLote: () => {
                    this.$vs.dialog({
                        type: 'confirm',
                        //color: '',
                        title: 'Confirmación',
                        text: '¿Desea crear un nuevo?',
                        acceptText: 'Si',
                        cancelText: 'No',
                        accept: () => {
                            this.axios.post('/app/v2_api_compras/NuevoLote', { Caja: this.selectCajaChica.Codigo, Lote: this.selectLote.Siguiente })
                                .then(resp => {
                                    if (resp.data.json.length > 0) {
                                        if (resp.data.json[0].codigo == 0) {
                                            this.Consulta().ListarLotes()
                                            this.Otros().Notificacion("Crear lote", resp.data.json[0].descripcion, "success")
                                        } else {
                                            this.Otros().Notificacion("Error al crear lote", resp.data.json[0].descripcion, "danger")
                                        }
                                    }
                                })
                        },
                        cancel: () => {
                        }
                    })
                },
                NuevaCompra: () => {
                    this.axios.post('/app/v2_api_compras/InsertarCompra', this.compra)
                        .then(resp => {
                            let tempArray = this.listaCuentas

                            if (resp.data.codigo == 0) {
                                tempArray = resp.data.json
                                this.verCompra = true
                                this.crearCompra = false
                                this.Otros().Notificacion('Crear compra', "Compra creada correctamente", "success")

                                tempArray.forEach(item => {
                                    if (item.Debe == null) {
                                        item.Debe = ''
                                    }

                                    if (item.Haber == null) {
                                        item.Haber = ''
                                    }
                                })
                            }

                            this.listaCuentas = tempArray
                            this.compra.AplicaAnticipos == 'S' ? this.compra.AplicaAnticipos = true : this.compra.AplicaAnticipos = false

                        })
                }
            }
        },
        Modificar: function () {
            return {
                ModificarLote: (asignar = false) => {
                    if (this.editarCompra) {
                        if (this.selectCajaChica.Codigo == '0' || Object.keys(this.selectLote).length == 0 || this.selectLote.Siguiente == null) {
                            this.Otros().Notificacion("Asignar Lote", "Debe seleccionar caja y lote", "danger")
                        } else {
                            this.$vs.dialog({
                                type: 'confirm',
                                title: 'Confirmación',
                                text: '¿Desea ' + (asignar ? 'asignar' : 'des-asignar') + ' el lote?',
                                acceptText: 'Si',
                                cancelText: 'No',
                                accept: () => {
                                    this.axios.post('/app/v2_api_compras/AsignarLote', { Caja: this.selectCajaChica.Codigo, Lote: this.selectLote.Siguiente, Documento: this.compra.Documento, Proveedor: this.proveedor.CODIGO, Asignar: asignar })
                                        .then(resp => {
                                            if (resp.data.json.length > 0) {
                                                if (resp.data.json[0].codigo == 0) {
                                                    this.Consulta().DetalleCompra(this.compra)
                                                    this.Otros().Notificacion((asignar ? 'Asignar' : 'Des-asignar') + ' lote', resp.data.json[0].descripcion, "success")
                                                } else {
                                                    this.Otros().Notificacion('Error al ' + (asignar ? 'Asignar' : 'Des-asignar') + ' lote', resp.data.json[0].descripcion, "danger")
                                                }
                                            }
                                        })
                                },
                                cancel: () => {
                                }
                            })
                        }
                    }
                },
                ModificarCompra: () => {
                    this.compra.Bitacora2 = bitacora.obtener()
                    const arrayTemp = []
                    this.listaCuentas.forEach(element => arrayTemp.push(element.Cuenta + '-' + element.Debe + '-' + element.Haber))
                    this.compra.Poliza = arrayTemp.join("|")

                    this.axios.post('/app/v2_api_compras/ModificarCompra', this.compra)
                        .then(resp => {
                            let tempArray = this.listaCuentas

                            if (resp.data.codigo == 0) {
                                this.compra.Status = 'P'
                                tempArray = resp.data.json
                                this.Consulta().ListarCompras(this.proveedor)
                                this.verCompra = true
                                this.Otros().Notificacion('Crear compra', "Compra modificada correctamente", "success")

                                tempArray.forEach(item => {
                                    if (item.Debe == null) {
                                        item.Debe = ''
                                    }

                                    if (item.Haber == null) {
                                        item.Haber = ''
                                    }
                                })
                            }

                            this.listaCuentas = tempArray
                            this.compra.AplicaAnticipos == 'S' ? this.compra.AplicaAnticipos = true : this.compra.AplicaAnticipos = false
                            this.verCompra = true
                            this.editarCompra = false
                        })
                }
            }
        },
        Otros: function () {
            return {
                AbrirVentana: (volver = false) => {
                    if (volver) {
                        this.ventana = false;
                    } else {
                        this.tituloVentana = 'Crear Compra'
                        this.radioOptions = [{ Codigo: 'F', Descripcion: 'Factura', Mostrar: true, DocumentoEsAfectoRetencion: true }, { Codigo: 'R', Descripcion: 'Recibo', Mostrar: true, DocumentoEsAfectoRetencion: false },
                        { Codigo: 'N', Descripcion: 'Nota/Créd', Mostrar: true, DocumentoEsAfectoRetencion: false }, { Codigo: 'P', Descripcion: 'Poliza Import.', Mostrar: false },
                        { Codigo: 'E', Descripcion: 'Factura Esp.', Mostrar: false }, { Codigo: 'M', Descripcion: 'Factura Med.', Mostrar: false },
                        { Codigo: 'H', Descripcion: 'Interpretación', Mostrar: false }, { Codigo: 'C', Descripcion: 'Referencia', Mostrar: false },
                        { Codigo: 'U', Descripcion: 'Cupones', Mostrar: false }, { Codigo: 'S', Descripcion: 'Servicios Profesionales', Mostrar: false },
                        { Codigo: 'G', Descripcion: 'Compras de multiniveles', Mostrar: false }, { Codigo: 'Y', Descripcion: 'Devolución Consignación', Mostrar: false },
                        { Codigo: 'Z', Descripcion: 'Envío Consignación', Mostrar: false }, { Codigo: 'X', Descripcion: 'Compromiso Pago COEX', Mostrar: false },
                        { Codigo: 'A', Descripcion: 'Nota/Abono', Mostrar: false }, { Codigo: 'L', Descripcion: 'Lotes de Honorarios', Mostrar: false }
                        ]
                        this.crearCompra = true
                        this.compra = {
                            Tipo: 'F', Documento: null, OrdenAutorizada: null, Validacion: null, Fecha: new Date(), Vencimiento: new Date(), Valor: 0,
                            ServiciosAfectos: 0, MercaderiaAfecta: 0, TotalImpuestos: 0, PContribuyente: null, ValorRetencionIVA: 0, ValorRetencionISR: 0,
                            AplicaAnticipos: false, CreditoContado: true, Saldo: 0, Tasa: 0, Dolares: 0, ServiciosNoAfectos: 0, MercaderiaNoAfecta: 0,
                            DocumentoQueAfecta: null, Observaciones: null, CodigoEFACE: null, DocumentoAnterior: '', PeriodoAnterior: 0, ValorAnterior: 0
                        }
                        this.verCompra = false
                        this.verCajaChica = false
                        this.ventana = true
                        this.editarCompra = false
                        this.bloquearCampo = false
                        this.listaCuentas = []
                        this.listaImpuestos = []
                        this.ordenCompra = {}
                        this.selectLote = {}
                        delete this.compra.CajaChicaLote

                        this.Consulta().BuscarAnticipos()
                        this.Consulta().ListarSucursales()
                        this.Consulta().ListarCuentasAsociadas()
                        this.Consulta().ListarPeriodos()
                        this.Consulta().ListarCajasChicas()
                    }
                },
                ValidarCompra: () => {
                    let esValida = true
                    if (this.compra.Documento == null || this.compra.Documento == '') {
                        this.Otros().Notificacion("Compra", "Debe ingresar documento", "danger")
                        esValida = false
                    }

                    if (this.documentoExiste) {
                        this.Otros().Notificacion("Compra", "El documento ya existe", "danger")
                        esValida = false
                    }

                    if (!this.periodoValido) {
                        this.Otros().Notificacion("Compra", "Debe seleccionar un periodo valido", "danger")
                        esValida = false
                    }

                    if (!this.fechaVencimientoValida) {
                        this.Otros().Notificacion("Compra", "Validar fecha de vencimiento", "danger")
                        esValida = false
                    }

                    if (this.compra.Tipo == 'N' || this.compra.Tipo == 'F' || this.compra.Tipo == 'R') {
                        if (this.selectCuentaAsociada.Codigo == "0" || this.selectCuentaAsociada.Codigo == "" || this.selectCuentaAsociada.Codigo == null) {
                            this.Otros().Notificacion("Compra", "Debe seleccionar una cuenta asociada de intregración", "danger")
                            esValida = false
                        } else {
                            this.compra.CuentaDef = this.selectCuentaAsociada.Codigo
                        }
                    }

                    if (this.compra.Tipo == 'F' && this.selectCuentaAsociada.SinOrdenCompra == false && (this.compra.OrdenAutorizada == null || this.compra.OrdenAutorizada == '') && (this.compra.Validacion == null || this.compra.Validacion == '')) {
                        this.Otros().Notificacion("Compra", "Debe seleccionar una orden de compra", "danger")
                        esValida = false
                    }

                    if (parseFloat(this.compra.Valor) == 0 || Number.isNaN(this.compra.Valor)) {
                        this.Otros().Notificacion("Compra", "No ha especificado el valor", "danger")
                        esValida = false
                    }

                    if (Math.abs((parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra.ServiciosNoAfectos) + parseFloat(this.compra.MercaderiaAfecta) + parseFloat(this.compra.MercaderiaNoAfecta) + parseFloat(this.compra.TotalImpuestos)) - parseFloat(this.compra.Valor)) >= 0.01) {
                        this.Otros().Notificacion("Compra", "Revise los valores referentes a servicios y mercadería por favor. (Valor = suma afectos + total impuestos + suma no afectos)", "danger")
                        esValida = false
                    }

                    if (parseFloat(this.compra.Valor) > parseFloat(this.DocumentoAfecta.Saldo) && this.compra.Tipo == 'N') {
                        this.Otros().Notificacion("Nota de crédito", "Saldo insuficiente para cubrir la nota", "danger")
                        esValida = false
                    }

                    this.listaCuentas.forEach(e => {
                        if (e.Cuenta == "") {
                            this.polizaCorrecta = false
                        }
                    })


                    if (!this.polizaCorrecta) {
                        this.Otros().Notificacion("Poliza", "Revisar poliza, ya que alguna cuenta no es correcta.", "danger")
                        esValida = false
                    }

                    if (esValida) {
                        this.compra.Periodo = this.selectPeriodo.Codigo
                        this.compra.CreditoContado ? this.compra.CreditoContado = 'C' : this.compra.CreditoContado = 'S'
                        if (this.compra.AplicaAnticipos) {
                            this.compra.AplicaAnticipos = 'S'
                            this.compra.Anticipos = parseFloat(this.compra.Anticipos).toFixed(2)
                        } else {
                            this.compra.AplicaAnticipos = 'N'
                            this.compra.Anticipos = 0
                        }

                        this.compra.PContribuyente ? this.compra.PContribuyente = 1 : this.compra.PContribuyente = 0
                        this.compra.CajaChica = this.selectCajaChica.Codigo
                        this.compra.CajaChicaLote = (this.selectLote.Siguiente == undefined || this.selectLote.Siguiente == '') ? 0 : this.selectLote.Siguiente
                        this.compra.Proveedor = this.proveedor.CODIGO

                        if (!this.editarCompra) {
                            this.Insertar().NuevaCompra()
                        } else {
                            this.Modificar().ModificarCompra()
                        }
                    }
                },
                CambiarPeriodo: (validar) => {
                    var date = this.compra.Fecha.split('-')
                    date = date[2] + "-" + date[1] + "-" + date[0]
                    date = new Date(date)
                    date.setDate(date.getDate() + 1)

                    this.listaPeriodos.forEach(element => {
                        if ((date.getTime() >= new Date(element.FechaInicial).getTime() && date.getTime() <= new Date(element.FechaFinal).getTime()) && validar) {
                            if (element.ComprasBlockeo == 'N') {
                                this.selectPeriodo = element
                                this.periodoValido = true
                            } else {
                                this.periodoValido = false

                                this.Otros().Notificacion('Periodo cerrado', "Este periodo ya fue cerrado, seleccione un periodo valido", "danger")
                            }
                        }
                    });
                },
                ValidarVencimiento: () => {
                    if (!this.verCompra) {
                        var date = this.compra.Fecha.split('-')
                        date = date[2] + "-" + date[1] + "-" + date[0]
                        date = new Date(date)
                        date.setDate(date.getDate() + 1)

                        var dateVencimiento = this.compra.Vencimiento.split('-')
                        dateVencimiento = dateVencimiento[2] + "-" + dateVencimiento[1] + "-" + dateVencimiento[0]
                        dateVencimiento = new Date(dateVencimiento)
                        dateVencimiento.setDate(dateVencimiento.getDate() + 1)

                        this.fechaVencimientoValida = true
                        if (dateVencimiento.getTime() < date.getTime()) {
                            this.Otros().Notificacion('Fecha de vencimiento invalida', "NO puede ser anterior a la fecha de la factura", "danger")
                            this.fechaVencimientoValida = false
                        }
                    }
                },
                ValidarPeriodo: () => {
                    if (this.selectPeriodo.ComprasBlockeo == 'N') {
                        this.periodoValido = true
                    } else {
                        this.periodoValido = false

                        this.Otros().Notificacion('Periodo cerrado', "Este periodo ya fue cerrado, seleccione un periodo valido", "danger")
                    }
                },
                /* OcultarOrdenCompra: () => {
                    if (this.selectCuentaAsociada.SinOrdenCompra || this.compra.Tipo !== 'F') {
                        this.compra.Validacion = null
                        this.compra.OrdenAutorizada = null
                    }
                }, */
                CalcularValores: () => {
                    if ((this.compra.Documento == null || this.compra.Documento == '') && this.ventana) {
                        this.Otros().Notificacion('Datos compras', "Debe especificar el número de documento", "danger")
                    } else if ((parseFloat(this.compra.Valor) == 0 || Number.isNaN(this.compra.Valor)) && this.ventana) {
                        this.Otros().Notificacion("Compra", "No ha especificado el valor", "danger")
                    } else if ((parseFloat(this.compra.Valor) > parseFloat(this.ordenCompra.Monto)) && this.ventana) {
                        this.Otros().Notificacion("Compra", "El valor de la factura no puede ser mayor al monto de la orden autorizada. Valor autorizado: " + new Intl.NumberFormat('es-GT', {
                            style: 'currency',
                            currency: 'GTQ',
                        }).format(parseFloat(this.ordenCompra.Monto).toFixed(2)), "danger")
                    } else {
                        this.compra.Valor = parseFloat(this.compra.Valor).toFixed(2)

                        if (this.compra.Tipo == 'N') {
                            if (Object.keys(this.DocumentoAfecta).length == 0) {
                                this.Otros().Notificacion("Nota de crédito", "Falta asignar documento que afecta.", "danger")
                            } else if (parseFloat(this.compra.Valor) > parseFloat(this.DocumentoAfecta.Saldo)) {
                                this.Otros().Notificacion("Nota de crédito", "Saldo insuficiente para cubrir la nota", "danger")
                            } else {
                                Math.sign(this.compra.Valor) == 1 ? this.compra.Valor = parseFloat((-1 * this.compra.Valor)).toFixed(2) : this.compra.Valor = parseFloat((this.compra.Valor)).toFixed(2)
                                this.compra.Saldo = parseFloat(this.compra.Valor).toFixed(2)
                            }
                        }

                        if (this.compra.Valor < 0 && this.compra.Tipo !== 'N') {
                            this.$vs.dialog({
                                type: 'confirm',
                                color: 'danger',
                                title: 'Confirmación',
                                text: '¿Desea cambiar el tipo a N/Crédito?',
                                acceptText: 'Si',
                                cancelText: 'No',
                                accept: () => {
                                    this.compra.Tipo = 'N'
                                },
                                cancel: () => {
                                    this.compra.Valor = 0
                                    this.Consulta().CalcularValores()
                                }
                            })
                        }

                        if (parseFloat(this.compra.Valor) !== (parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra.MercaderiaAfecta) + parseFloat(this.compra.ServiciosNoAfectos) + parseFloat(this.compra.MercaderiaNoAfecta) + parseFloat(this.compra.TotalImpuestos))) {
                            this.compra.ServiciosAfectos = 0
                            this.compra.MercaderiaAfecta = 0
                        }

                        if (this.compra.Valor !== 0) {
                            if (this.compra.Tipo !== 'R') {
                                if (this.listaImpuestos.length !== 0 && this.proveedor.TIPOCONTRIBUYENTE !== 'P') {
                                    this.Otros().RecalcularImpuestos(false)
                                } else {
                                    this.Otros().RevisaImpuestos()
                                }
                            }
                        }

                        this.Otros().CalcularIVA()
                        this.Otros().CalcularISR()
                        this.Otros().CalcularSaldo()
                    }
                },
                RevisaImpuestos: () => {
                    var date = this.compra.Fecha.split('-')
                    date = date[2] + "-" + date[1] + "-" + date[0]
                    date = new Date(date)
                    date.setDate(date.getDate() + 1)

                    this.ctrlFechaPCCreditoFiscal.setDate(this.ctrlFechaPCCreditoFiscal.getDate() + 1)

                    if ((this.proveedor.TIPOCONTRIBUYENTE !== 'P' && this.proveedor.TIPOCONTRIBUYENTE !== 'E') || date.getTime() < this.ctrlFechaPCCreditoFiscal.getTime()) {
                        this.axios.post('/app/v2_api_compras/ListarImpuestos', { Tipo: this.compra.Tipo })
                            .then(resp => {
                                if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                    this.listaImpuestos = []
                                    this.listaImpuestos = resp.data.json;
                                    this.compra.TotalImpuestos = 0

                                    this.listaImpuestos.forEach(element => {
                                        element.Monto = 0

                                        if (element.Factor !== 0) {
                                            if (this.compra.ServiciosAfectos !== 0 || this.compra.MercaderiaAfecta !== 0) {
                                                element.Monto = ((this.compra.ServiciosAfectos + this.compra.MercaderiaAfecta) / (1 + (this.constantes.IVA / 100)) * element.Factor)
                                                element.Monto = parseFloat(element.Monto).toFixed(2)
                                            } else {
                                                element.Monto = (((this.compra.Valor - this.compra.ServiciosNoAfectos - this.compra.MercaderiaNoAfecta) / (1 + (this.constantes.InvFactorImpuestoCompra / 100))) * element.Factor)
                                                element.Monto = parseFloat(element.Monto).toFixed(2)
                                            }

                                            this.compra.TotalImpuestos += parseFloat(element.Monto)
                                        }
                                    })

                                    //this.Otros().CalcularIVA()
                                }
                            })
                    }
                },
                ProveedorExcento: () => {
                    this.proveedorExcento.ExcentoRetencion = false
                    this.proveedorExcento.ExcentoRetenISR = false

                    if (this.proveedor.ESAGENTERETENEDOR !== null && this.proveedor.ESAGENTERETENEDOR == 'S') {
                        this.proveedorExcento.ExcentoRetencion = true
                        this.proveedorExcento.ExcentoRetenISR = true
                    } else if (this.proveedor.TIPORETENCION == this.constantes.RetencionPagosTrimestral) {
                        this.proveedorExcento.ExcentoRetencion = false
                        this.proveedorExcento.ExcentoRetenISR = true
                    } else if (this.proveedor.TIPORETENCION == this.constantes.RetencionDefinitiva) {
                        this.proveedorExcento.ExcentoRetencion = false
                        this.proveedorExcento.ExcentoRetenISR = false
                    } else if (this.proveedor.TIPOCONTRIBUYENTE !== null && this.proveedor.TIPOCONTRIBUYENTE == 'P') {
                        this.proveedorExcento.ExcentoRetencion = false
                        this.proveedorExcento.ExcentoRetenISR = true
                    } else if (this.proveedor.TIPOCONTRIBUYENTE !== null && this.proveedor.TIPOCONTRIBUYENTE == 'E') {
                        this.proveedorExcento.ExcentoRetencion = false
                        this.proveedorExcento.ExcentoRetenISR = true
                    }
                },
                EmpresaRetencion: () => {
                    if (this.constantes.EsAgenteRetenedor !== 'S') {
                        this.empresaRetencion.DebeRetenerIVA = false
                    }
                },
                CalcularIVA: () => {
                    this.compra.ValorRetencionIVA = 0

                    if (!this.proveedorExcento.ExcentoRetencion && this.empresaRetencion.DebeRetenerIVA && this.documentoEsAfectoRetencion) {
                        if (this.proveedor.TIPOCONTRIBUYENTE == 'P' && parseFloat(this.compra.Valor) >= (parseFloat(this.constantes.MontoInicialAfectoRetencion + 0.01))) {
                            this.compra.PContribuyente = true
                            this.compra.ValorRetencionIVA = (this.compra.Valor * (this.constantes.Retencion_IVA_PC / 100)).toFixed(2)
                        } else if (this.proveedor.TIPOCONTRIBUYENTE == 'E' && parseFloat(this.compra.Valor) >= (parseFloat(this.constantes.MontoInicialAfectoRetencion + 0.01))) {
                            this.compra.PContribuyente = true
                            this.compra.ValorRetencionIVA = (this.compra.Valor * (parseFloat(this.constantes.Retencion_IVA_PCE) / 100)).toFixed(2)
                        } else {
                            if (this.compra.ServiciosAfectos !== 0 || this.compra.MercaderiaAfecta !== 0) {
                                if ((parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra.MercaderiaAfecta)) * (1 + (this.constantes.IVA / 100)) >= parseFloat(this.constantes.MontoInicialAfectoRetencion)) {
                                    let valorIVACompra = parseFloat((parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra.MercaderiaAfecta)) * ((this.constantes.IVA / 100))).toFixed(2)
                                    this.compra.ValorRetencionIVA = (valorIVACompra * (this.constantes.Retencion_IVA / 100)).toFixed(2)
                                } else {
                                    this.compra.ValorRetencionIVA = 0
                                }
                            } else {
                                if (this.compra.Valor >= parseFloat(this.constantes.MontoInicialAfectoRetencion) && this.compra.ServiciosAfectos == 0 || this.compra.MercaderiaAfecta == 0) {
                                    let valorIVACompra = ((this.compra.Valor / (1 + (this.constantes.IVA / 100))) * (this.constantes.IVA / 100)).toFixed(2)
                                    this.compra.ValorRetencionIVA = (valorIVACompra * (this.constantes.Retencion_IVA / 100)).toFixed(2)
                                } else {
                                    if ((this.compra.Valor - this.compra.ServiciosNoAfectos - this.compra.MercaderiaNoAfecta) >= parseFloat(this.constantes.MontoInicialAfectoRetencion)) {
                                        let valorIVACompra = (((this.compra.Valor - this.compra.ServiciosNoAfectos - this.compra.MercaderiaNoAfecta) / (1 + (this.constantes.IVA / 100))) * (this.constantes.IVA / 100)).toFixed(2)
                                        this.compra.ValorRetencionIVA = (valorIVACompra * (this.constantes.Retencion_IVA / 100)).toFixed(2)
                                    } else {
                                        this.compra.ValorRetencionIVA = 0
                                    }
                                }
                            }
                        }
                    } 
                },
                CalcularISR: () => {
                    this.compra.ValorRetencionISR = 0

                    if (!this.proveedorExcento.ExcentoRetenISR && this.documentoEsAfectoRetencion) {
                        if (parseFloat(this.constantes.ExcedenteRetencion_ISR) > 0) {
                            if (this.compra.ServiciosAfectos !== 0 || this.compra.MercaderiaAfecta !== 0) {
                                if (((parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra.MercaderiaAfecta)) * (1 + (this.constantes.IVA / 100))) >= parseFloat(this.constantes.MontoInicialRetenISR)) {
                                    let valorExcedente = ((parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra.MercaderiaAfecta)) - parseFloat(this.constantes.MontoExcedenteRetenISR))
                                    if (valorExcedente > 0) {
                                        let valorIVACompra = ((parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra.MercaderiaAfecta)) - valorExcedente)
                                        this.compra.ValorRetencionISR = parseFloat((valorIVACompra * (this.constantes.Retencion_ISR / 100)) + (valorExcedente * (this.constantes.ExcedenteRetencion_ISR / 100))).toFixed(2)
                                    } else {
                                        valorExcedente = 0
                                        let valorIVACompra = (parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra.MercaderiaAfecta))
                                        this.compra.ValorRetencionISR = parseFloat((valorIVACompra * (this.constantes.Retencion_ISR / 100))).toFixed(2)
                                    }
                                } else {
                                    this.compra.ValorRetencionISR = 0
                                }
                            } else {
                                if (this.compra.Valor >= parseFloat(this.constantes.MontoInicialRetenISR) && this.compra.ServiciosAfectos == 0 && this.compra.MercaderiaAfecta == 0) {
                                    let valorIVACompra = (this.compra.Valor / (1 + (this.constantes.IVA / 100))).toFixed(2)
                                    let valorExcedente = (valorIVACompra - parseFloat(this.constantes.MontoExcedenteRetenISR))

                                    if (valorExcedente > 0) {
                                        valorIVACompra = (valorIVACompra - valorExcedente)

                                        this.compra.ValorRetencionISR = ((valorIVACompra * (this.constantes.Retencion_ISR / 100)) + (valorExcedente * (this.constantes.ExcedenteRetencion_ISR / 100))).toFixed(2)
                                    } else {
                                        valorExcedente = 0
                                        this.compra.ValorRetencionISR = (valorIVACompra * (parseFloat(this.constantes.Retencion_ISR) / 100)).toFixed(2)
                                    }
                                } else {
                                    if ((this.compra.Valor - this.compra.ServiciosNoAfectos - this.compra.MercaderiaNoAfecta) >= parseFloat(this.constantes.MontoInicialRetenISR)) {
                                        let valorIVACompra = ((this.compra.Valor - this.compra.ServiciosNoAfectos - this.compra.MercaderiaNoAfecta) / (1 + (this.constantes.IVA / 100))).toFixed(2)
                                        let valorExcedente = (valorIVACompra - parseFloat(this.constantes.MontoExcedenteRetenISR))

                                        if (valorExcedente > 0) {
                                            valorIVACompra = (valorIVACompra - valorExcedente)
                                            this.compra.ValorRetencionISR = ((valorIVACompra * (this.constantes.Retencion_ISR / 100)) + (valorExcedente * (this.constantes.ExcedenteRetencion_ISR / 100))).toFixed(2)
                                        } else {
                                            valorExcedente = 0
                                            this.compra.ValorRetencionISR = (valorIVACompra * (parseFloat(this.constantes.Retencion_ISR) / 100)).toFixed(2)
                                        }
                                    } else {
                                        this.compra.ValorRetencionISR = 0
                                    }
                                }
                            }
                        }
                    }
                },
                CalcularSaldo: () => {
                    if (this.compra.Tipo !== 'N') {
                        this.compra.Saldo = null
                        if (this.compra.CreditoContado) {
                            if (this.compra.AplicaAnticipos) {
                                if (parseFloat(this.compra.Anticipos) > parseFloat(this.compra.Valor)) {
                                    this.compra.Saldo = 0
                                } else {
                                    this.compra.Saldo = parseFloat(this.compra.Valor - this.compra.Anticipos - this.compra.ValorRetencionIVA - this.compra.ValorRetencionISR).toFixed(2)
                                }
                            } else {
                                this.compra.Saldo = parseFloat(this.compra.Valor - this.compra.ValorRetencionIVA - this.compra.ValorRetencionISR).toFixed(2)
                            }
                        } else {
                            this.compra.Saldo = 0
                        }
                    }
                },
                CalcularTasa: (isTasa) => {
                    if (this.compra.Tasa !== null && this.compra.Tasa !== '' && this.compra.Tasa > 0 && isTasa) {
                        this.compra.Dolares = 0
                        this.compra.Dolares = parseFloat(this.compra.Valor / this.compra.Tasa).toFixed(2)
                        this.compra.Tasa = parseFloat(this.compra.Tasa).toFixed(2)
                    } else if (this.compra.Dolares !== null && this.compra.Dolares !== '' && this.compra.Dolares > 0 && !isTasa) {
                        this.compra.Tasa = 0
                        this.compra.Tasa = parseFloat(this.compra.Valor / this.compra.Dolares).toFixed(2)
                        this.compra.Dolares = parseFloat(this.compra.Dolares).toFixed(2)
                    } else {
                        this.compra.Dolares = null
                        this.compra.Tasa = null
                    }
                },
                CalcularMSAfecta: (param1, param2) => {
                    //if (this.compra.Valor !== null) {
                    this.Otros().RecalcularImpuestos()
                    let param1Temp = parseFloat(this.compra.Valor) - (parseFloat(this.compra.TotalImpuestos) + parseFloat(this.compra[param2]))
                    if ((this.compra.MercaderiaNoAfecta == 0 || this.compra.MercaderiaNoAfecta == '') && (this.compra.ServiciosNoAfectos == 0 || this.compra.ServiciosNoAfectos == '') && param1Temp > 0) {
                        this.compra[param1] = parseFloat(parseFloat(this.compra.Valor) - (parseFloat(this.compra.TotalImpuestos) + parseFloat(this.compra[param2]))).toFixed(2)
                    } else {
                        if (this.listaImpuestos.length == 1) {
                            this.compra[param2] = 0
                            this.compra[param1] = parseFloat((parseFloat(this.compra.Valor) - (parseFloat(this.compra.ServiciosNoAfectos) + parseFloat(this.compra.MercaderiaNoAfecta))) / (1 + parseFloat(this.listaImpuestos[0].Factor))).toFixed(2)
                        }
                    }

                    this.Otros().CalcularIVA()
                    this.Otros().CalcularISR()
                    this.Otros().CalcularSaldo()
                    //}
                },
                CalcularMSNoAfecta: (param1, param2) => {
                    this.Otros().RecalcularImpuestos(false)
                    this.compra[param1] = parseFloat(parseFloat(this.compra.Valor) - (parseFloat(this.compra.TotalImpuestos) + parseFloat(this.compra.MercaderiaAfecta) + parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra[param2]))).toFixed(2)
                },
                CalcularMSAfectaExit: (param1, param2) => {
                    /* f (parseFloat(this.compra[param1]) > 0) {
                        if (parseFloat(this.compra.TotalImpuestos) > 0) {
                            this.compra[param2] = parseFloat((this.compra.Valor) - (parseFloat(this.compra.MercaderiaNoAfecta) + parseFloat(this.compra.ServiciosNoAfectos)) - (parseFloat(this.compra[param1]) + parseFloat(this.compra.TotalImpuestos))).toFixed(2)
                        } else {
                            this.compra[param2] = 0
                        }
                    }
                    */
                    this.compra[param1] = parseFloat(this.compra[param1]).toFixed(2)
                    this.compra[param2] = parseFloat(this.compra[param2]).toFixed(2)
                },
                RecalcularImpuestos: (desdeAfecto = true) => {
                    this.compra.TotalImpuestos = 0
                    let tempArray = this.listaImpuestos
                    this.listaImpuestos = []

                    tempArray.forEach(element => {
                        element.Monto = 0
                        if (element.Factor !== 0) {
                            //if ((this.compra.ServiciosAfectos !== 0 || this.compra.MercaderiaAfecta !== 0)) {
                            if (desdeAfecto) {
                                element.Monto = ((parseFloat(this.compra.Valor) - ((parseFloat(this.compra.ServiciosNoAfectos) + parseFloat(this.compra.MercaderiaNoAfecta)))) / (1 + (this.constantes.InvFactorImpuestoCompra / 100)) * element.Factor)
                                element.Monto = parseFloat(element.Monto).toFixed(2)
                            } else {
                                element.Monto = ((parseFloat(this.compra.ServiciosAfectos) + parseFloat(this.compra.MercaderiaAfecta)) * element.Factor)
                                element.Monto = parseFloat(element.Monto).toFixed(2)
                            }

                            /*} else {
                                element.Monto = (((((parseFloat(this.compra.Valor) - parseFloat(this.compra.ServiciosNoAfectos) - parseFloat(this.compra.MercaderiaNoAfecta)) / (1 + (this.constantes.InvFactorImpuestoCompra / 100))) * element.Factor) * 100) / 100)
                                element.Monto = parseFloat(element.Monto).toFixed(2)
                            }*/

                            this.compra.TotalImpuestos += parseFloat(element.Monto)
                        }
                    })

                    this.listaImpuestos = tempArray
                },
                VentanaAdicional: (opcion) => {
                    this.ventanaAdicional.mostrar = true
                    this.ventanaAdicional.opcion = opcion
                    this.listaNotasXDocumento = []
                    if (opcion == 1) {
                        this.axios.post('/app/v2_api_compras/BuscarDocumentos', { Proveedor: this.proveedor.CODIGO, Documento: this.compra.Documento })
                            .then(resp => {
                                if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                    this.listaNotasXDocumento = resp.data.json
                                    this.ventanaAdicional.titulo = "Notas de crédito para documento: " + this.compra.Documento
                                }
                            })
                    } else if (opcion == 2) {
                        this.listaDocumentoXLote = []
                        this.ventanaAdicional.titulo = "Contenido de Caja chica: " + this.compra.CajaChica + " - Lote: " + this.compra.CajaChicaLote
                        this.axios.post('/app/v2_api_compras/ImprimirLote', {
                            Caja: (this.selectCajaChica.Codigo !== undefined) ? this.selectCajaChica.Codigo : this.compra.CajaChica,
                            Lote: (this.selectLote.Siguiente !== undefined) ? this.selectLote.Siguiente : this.compra.CajaChicaLote
                        })
                            .then(resp => {
                                if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                    this.listaDocumentoXLote = resp.data.json
                                }
                            })
                    } else if (opcion == 3) {
                        this.listaDocumentoPagos = []
                        this.ventanaAdicional.titulo = "Pagos para documento: " + this.compra.Documento
                        this.axios.post('/app/v2_api_compras/BuscarDocumentoPagos', {
                            Proveedor: this.proveedor.CODIGO, Documento: this.compra.Documento
                        })
                            .then(resp => {
                                if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                    this.listaDocumentoPagos = resp.data.json
                                }
                            })
                    } else if (opcion == 4) {
                        this.listaDocumentoPagos = []
                        this.ventanaAdicional.titulo = "Anulación de documento: " + this.compra.Documento

                    }
                },
                VerCajaChica: (mostrar) => {
                    this.verCajaChica = mostrar
                },
                CambiarLote: () => {
                    this.LoteDocumentos = { Documentos: 0 }

                    if (this.compra.CajaChicaLote !== '') {
                        this.axios.post('/app/v2_api_compras/CajaChicaDocumentos', {
                            Caja: (this.selectCajaChica.Codigo !== undefined) ? this.selectCajaChica.Codigo : this.compra.CajaChica,
                            Lote: (this.selectLote.Siguiente !== undefined) ? this.selectLote.Siguiente : this.compra.CajaChicaLote
                        })
                            .then(resp => {
                                this.LoteDocumentos.Documentos = 0

                                if (resp.data.codigo == 0 && resp.data.json.length > 0) {
                                    this.LoteDocumentos = resp.data.json[0]
                                } else {
                                    this.LoteDocumentos.Documentos = 0
                                }
                            })
                    }
                },
                MostrarLotesCerrados: () => {
                    if (!this.verCompra && !this.editarCompra) this.selectLote = {}
                },
                Reporte: () => {
                    this.$reporte_modal({
                        Nombre: "Documentos por Lote",
                        Formato: "EXCEL",
                        Opciones: {
                            nombrereporte: "DocumentosXLote",
                            Caja: (this.selectCajaChica.Codigo !== undefined) ? this.selectCajaChica.Codigo : this.compra.CajaChica,
                            Lote: (this.selectLote.Siguiente !== undefined) ? this.selectLote.Siguiente : this.compra.CajaChicaLote
                        },
                        Descargar: true,
                        NombreArchivoDescargar: 'Prueba '
                    })
                },
                Notificacion: (titulo, texto, color) => {
                    //console.log(titulo, texto, color)
                    this.$vs.notify({
                        title: titulo,
                        text: texto,
                        color: color,
                        position: 'bottom-center'
                    })
                },
                RevisarPartida: (estado) => {
                    if (this.selectPeriodo.ComprasBlockeo == 'S') {
                        this.Otros().Notificacion("Revisar Partida", "Este período ya está cerrado, no puede modificar el registro", "danger")
                    } else {
                        switch (estado) {
                            case "R":
                                this.Otros().CambiarEstadoPartida('P')
                                break;
                            default:
                                let cambiar = true
                                if (this.listaCuentas.length == 0) {
                                    this.Otros().Notificacion("Revisar Partida", "Auxiliar no tiene datos", "danger")
                                    cambiar = false
                                }

                                this.compra.Tipo == 'N' ? this.compra.Valor = parseFloat(this.compra.Valor).toFixed(2) * -1 : null

                                if (parseFloat(this.$refs.gridDetalle.instance.getTotalSummaryValue("Debe")).toFixed(2) !== parseFloat(this.$refs.gridDetalle.instance.getTotalSummaryValue("Haber")).toFixed(2)) {
                                    this.Otros().Notificacion("Revisar Partida", "No cuadran los datos", "danger")
                                    cambiar = false
                                } else if (parseFloat(this.$refs.gridDetalle.instance.getTotalSummaryValue("Debe")).toFixed(2) !== parseFloat(this.compra.Valor).toFixed(2) || parseFloat(this.$refs.gridDetalle.instance.getTotalSummaryValue("Haber")).toFixed(2) !== parseFloat(this.compra.Valor).toFixed(2)) {
                                    this.Otros().Notificacion("Revisar Partida", "Los totales de la poliza no coinciden con el valor de la compra", "danger")
                                    cambiar = false
                                }

                                if (cambiar) this.Otros().CambiarEstadoPartida('R')
                                break;
                        }
                    }
                },
                CambiarEstadoPartida: (nuevoEstado) => {
                    this.axios.post('/app/v2_api_compras/CambiarEstado', {
                        Proveedor: this.proveedor.CODIGO, Documento: this.compra.Documento, Estado: nuevoEstado
                    })
                        .then(resp => {
                            if (resp.data.codigo == 0) {
                                this.Consulta().DetalleCompra(this.compra, true, false)
                                this.Otros().Notificacion("Revisar Partida", resp.data.descripcion, "success")
                            } else {
                                this.Otros().Notificacion("Revisar Partida", "Error al cambiar estado", "danger")
                            }
                        })
                },
                ValidarDocumento: () => {
                    if (this.compra.Documento !== this.compra.DocumentoAnterior) {
                        this.axios.post('/app/v2_api_compras/BuscarDocumento', {
                            Proveedor: this.proveedor.CODIGO, Documento: this.compra.Documento
                        })
                            .then(resp => {
                                if (resp.data.codigo == 0) {
                                    this.documentoExiste = false
                                    if (resp.data.json[0].Existe != "0") {
                                        this.Otros().Notificacion("Documento", "Documento ya existe", "danger")
                                        this.documentoExiste = true
                                    }
                                }
                            })
                    } else if (this.compra.Documento == this.compra.DocumentoAnterior && this.compra.DocumentoAnterior != '') {
                        this.documentoExiste = false
                    }
                },
                AnularCompra: async () => {
                    this.Otros().ConsultarAnulacion().then((resp) => {
                        if (resp.data.json.length == 0) {
                            this.Otros().VentanaAdicional(4)
                        } else {
                            this.Otros().Notificacion("Anulación", resp.data.json[0].descripcion, "danger")
                        }
                    })

                },
                ConsultarAnulacion: async () => {
                    return await this.axios.post('/app/v2_api_compras/BuscarAnulacion', {
                        Proveedor: this.proveedor.CODIGO, Documento: this.compra.Documento
                    })
                },
                acceptAlert: () => {
                    this.axios.post('/app/v2_api_compras/AnularCompra', {
                        Proveedor: this.proveedor.CODIGO, Documento: this.compra.Documento, Razon: this.razon
                    }).then(resp => {
                        if (resp.data.codigo == 0) {
                            this.Otros().Notificacion("Anulación", resp.data.descripcion, "success")
                            this.compra.Status = 'A'
                            this.razon = ''
                            this.ventanaAdicional.mostrar = false
                            this.Consulta().ListarCompras(this.proveedor)
                            this.Consulta().DetalleCompra(this.compra, true, false)
                        }
                    })
                },
                BuscarDocumento: (obj) => {
                    this.Consulta().DetalleCompra(obj, true, false)
                },
                ImprimirPartida: () => {
                    this.$reporte_modal({
                        Nombre: "Impresión de Partidas",
                        Formato: "PDF",
                        Opciones: {
                            nombrereporte: "ImpresionPartidas",
                            Documento: this.compra.Documento
                        },
                        NombreArchivoDescargar: 'Prueba '
                    })
                }
            }
        }
    }
}
</script>
