<!-- BaseRadioButtonGroup.vue -->
<template>
  <div class="input-group">
    <BaseRadioButton v-for="(option, index) in options" 
    :option="option" 
    :key="'Id' + index" 
    :id=generateID()
    :groupName="groupName" 
    v-model="selectedValue.Descripcion"
    @radio-selected="onRadioSelected" 
    :option-selected="selectedOption"/>
  </div>
</template>
<!-- :option-selected="selectedValue.Descripcion" -->
<script>
import BaseRadioButton from "@/components/sermesa/global/radioButtons/BaseRadioButton";

export default {
  name: "BaseRadionButtonGroup",
  components: {
    BaseRadioButton
  },
  props: {
    options: {
      required: true,
      type: Array
    },
    groupName: {
      required: true,
      type: String
    },
    selectedOption: {
      required: false,
      type: String
    }  
  },
  data() {
    return {
      selectedValue: ''
            
    };
  },
  methods: {
    generateID() {
      return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c => (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16));
    },
    onRadioSelected(value) {
      this.selectedValue = value;
      this.$emit("input", value);
    }
  }
};
</script>



<style scoped>
.input-group {
  width: 100% !important;
  /* margin: 20px auto; */
  border-radius: 8px !important;
}


label {
  cursor: pointer !important;
}
</style>