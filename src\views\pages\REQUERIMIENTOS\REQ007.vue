<template>
    <vx-card title="Límite en Requerimientos de Compra Sub-Bodega">
        <div class="flex flex-wrap">
            <div class="md:pl-2">
                <vs-button color="primary" type="filled" icon="save" @click="cargarMontosLimite">
                    Guardar</vs-button>
            </div>
            <div class="md:pl-2">
                <vs-button color="red" type="filled"  icon="close" @click="recargar">
                    Cancelar</vs-button>
            </div>
            <div class="md:pl-2">
                <vs-button color="green" type="filled" @click="consultaBodegasLimite" icon="sync">
                    Recargar</vs-button>
            </div>
            <div class="break"></div>
            <div class="w-full">
                <vs-divider>Monto Límite Sub-Bodegas </vs-divider>
                <vs-table max-items="15" pagination :data="ListaLimitesMontosSubBodegas" search noDataText="Sin datos disponibles">
                    <template slot="thead">
                        <vs-th>Código</vs-th>
                        <vs-th>Nombre</vs-th>
                        <vs-th>Hospital</vs-th>
                        <vs-th>Monto Límite Mensual</vs-th>
                        <vs-th>Monto Acumulado Mensual</vs-th>
                        <vs-th>Estado</vs-th>  
                        <vs-th>Actualizar</vs-th>                    
                    </template>
                    <template slot-scope="{data}">
                        <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                            <vs-td :data="indextr+'codigobodega'" width='5%'>{{ tr.CodigoBodega }}</vs-td>
                            <vs-td :data="indextr+'bodega'" width='25%'>{{ tr.NombreBodega }}</vs-td>
                            <vs-td :data="indextr+'hospital'" width='25%'>{{ tr.Hospital }}</vs-td>
                            <vs-td :data="indextr+'montolimitemensual'" width='20%'>       
                                <DxNumberBox v-model=tr.MontoMensual format="Q #,##0.00" />
                            </vs-td>
                            <vs-td :data="indextr+'montoacumulado'" width='10%'>{{ $formato_moneda(tr.MontoAcumulado) }}</vs-td>
                            <vs-td style="text-align: center;" :data="indextr+'estado'" width='10%'>
                                <label v-if="tr.Estado == 'S'" style="height: 30px;background-color:green;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 6%;"> ACTIVO </label>
                                <label v-else-if="tr.Estado == 'N'" style=" height: 30px;background-color:#B2BABB;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 1%;"> INACTIVO</label>
                                <label v-else style=" height: 30px;background-color:red;width:150px;display: inline-table;color:white;text-align: center;  border-radius: 1%;"> N/A</label>
                            </vs-td>    
                            <vs-td width="5%">
                                <div v-if="tr.Estado" style="text-align: center;">
                                    <vs-switch style="text-align:center;" color="green" v-model="tr.valorEstado" @click="()=>{!tr.valorEstado==true?tr.Estado='S':tr.Estado='N'
                                                                                                                              !tr.valorEstado==true?tr.textoEstado='ACTIVO':tr.textoEstado='INACTIVO'}"/>                                    
                                </div>                                
                            </vs-td>                                                
                        </vs-tr>
                    </template>
                </vs-table>
            </div>
        </div>        
    </vx-card>
</template>    
<script>
import {
    DxNumberBox
} from 'devextreme-vue/number-box'

export default{
    components:{
        DxNumberBox
    },
    data(){
        return{
            ListaLimitesMontosSubBodegas: [],
            ListaInicialLimitesMontosSubBodegas: []
        }
    },
    mounted(){
        this.consultaBodegasLimite();        
    },
    methods:{
        cargarMontosLimite(){            
            var ListaMontosModificados = 
            this.ListaLimitesMontosSubBodegas.map((bodegaModificada,index)=> { var tipoCambio='';
                                                                             if(bodegaModificada.MontoMensual != this.ListaInicialLimitesMontosSubBodegas[index].MontoMensual && 
                                                                                 bodegaModificada.Estado != this.ListaInicialLimitesMontosSubBodegas[index].Estado)
                                                                              {
                                                                                tipoCambio = 'M,E'
                                                                              }
                                                                              else if(bodegaModificada.MontoMensual != this.ListaInicialLimitesMontosSubBodegas[index].MontoMensual){
                                                                                tipoCambio = 'M'
                                                                              }
                                                                              else if(bodegaModificada.Estado != this.ListaInicialLimitesMontosSubBodegas[index].Estado){
                                                                                tipoCambio = 'E'
                                                                              } 
                                                                              return {
                                                                                ...bodegaModificada,
                                                                                tipoCambio
                                                                              }
                                                                            })
            ListaMontosModificados =                                                                 
            ListaMontosModificados.filter(bodegas=>bodegas.tipoCambio != '')           
            
            
            this.axios.post("/app/v1_OrdenCompra/MontoLimiteSubBodega",
            {
                opcion:"I",
                subOpcion:"A",
                listaBodegas: ListaMontosModificados.map(subBodega=>{ return{
                                                                                bodega:subBodega.CodigoBodega,
                                                                                montoMensual:subBodega.MontoMensual, 
                                                                                estado:subBodega.Estado,
                                                                                tipoCambio:subBodega.tipoCambio
                                                                            }
                                                                    }
                                                        )
            }).then( resp => {
                
                
                
                
                
                if(resp.data.codigo == 0){
                    this.consultaBodegasLimite();
                }else{
                    this.$vs.notify({
                        position:'top-center',
                        title:'Montos Límite',
                        text:resp.data.mensaje,
                        color: '#B71C1C'
                    })
                }
            }).catch(()=>{});
            
        },
        recargar(){
            var ListaMontosModificados = 
            this.ListaLimitesMontosSubBodegas.map((bodegaModificada,index)=> { var tipoCambio='';
                                                                             if(bodegaModificada.MontoMensual != this.ListaInicialLimitesMontosSubBodegas[index].MontoMensual && 
                                                                                 bodegaModificada.Estado != this.ListaInicialLimitesMontosSubBodegas[index].Estado)
                                                                              {
                                                                                tipoCambio = 'M,E'
                                                                              }
                                                                              else if(bodegaModificada.MontoMensual != this.ListaInicialLimitesMontosSubBodegas[index].MontoMensual){
                                                                                tipoCambio = 'M'
                                                                              }
                                                                              else if(bodegaModificada.Estado != this.ListaInicialLimitesMontosSubBodegas[index].Estado){
                                                                                tipoCambio = 'E'
                                                                              } 
                                                                              return {
                                                                                ...bodegaModificada,
                                                                                tipoCambio
                                                                              }
                                                                            })
            ListaMontosModificados = ListaMontosModificados.filter(bodegas=>bodegas.tipoCambio != '')     
            if(ListaMontosModificados.length > 0){
                this.$vs.dialog({
                        type: 'confirm',
                        color: '#ED8C72',
                        acceptText: 'Continuar',
                        cancelText: 'Cancelar',
                        title: 'Recargar montos límite',
                        text: 'Tiene limites modificados pendientes de guardar ',
                        clientWidth: 100,                               
                        accept: () => {                    
                            this.consultaBodegasLimite()
                            
                        }
                })      
            }
        },
        consultaBodegasLimite(){
            this.axios.post("/app/v1_OrdenCompra/MontoLimiteSubBodega",
            {
                opcion:"C",
                subOpcion:"B",
                codigoAgrupacionMayorBodega: 7,
                codigoAgrupacionMenorBodega: 2
            }).then( resp => {
                this.ListaLimitesMontosSubBodegas = resp.data.json.map(subBodega => { return {'valorEstado':subBodega.Estado=='S'?true:false,
                                                                                              'textoEstado':subBodega.Estado=='S'?'ACTIVO':subBodega.Estado=='N'?'INACTIVO':'N/A',
                                                                                               ...subBodega,
                                                                                               MontoMensual: this.RedondearDigitos(subBodega.MontoMensual), 
                                                                                               }});
                
                this.ListaInicialLimitesMontosSubBodegas = JSON.parse(JSON.stringify(this.ListaLimitesMontosSubBodegas));
                
            }).catch(()=>{});
        },
        RedondearDigitos(digito){
            try{
                if(!digito)
                    digito = 0.00                
                return +Number.parseFloat(digito).toFixed(2)
            }catch(error){
                return 0.00
            }
        }
    }
}
</script>
<style scoped>
.money-input{
    white-space: nowrap;
    color: #008FBE;
    border-radius: 5px;
    height: 30px;
 }

 .money-input:focus{
    border:1px solid #008FBE!important;
  }
</style>