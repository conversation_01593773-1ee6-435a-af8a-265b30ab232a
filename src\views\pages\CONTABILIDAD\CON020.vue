<template>
    <vx-card title="Actualización Periodo Cierre">

        <div class="flex flex-wrap w-full md:w-6/12 lg:w-4/12 xl:w-6/12  pr-2">

            <div class="w-1/6">

               <vs-input
                 label="Periodo"
                 class="w-full"
                 v-model="Periodo"
                 @focus="limpiarDescripcion"
                 @blur="limpiarDescripcion">
                 </vs-input>

            </div>

            <div class="w-5/6 pl-4">
                <vs-input
                  label="Descripción Periodo"
                  class="w-full"
                  v-model="DescripcionPeriodo"
                  @focus="limpiarPeriodo"
                  @blur="limpiarPeriodo" 
                />
            </div>


            <div class="flex w-full p-1">
                <div class="pl-2">
                    <vs-button @click="ConsultarPeriodo" >
                        Consultar
                    </vs-button>
                </div>

            </div>

        </div>

        <vs-row class="pt-4">

            <DxDataGrid :data-source="listaPeriodos" :show-borders="true" :column-auto-width="true"
                :paging="{ enabled: true, pageSize: 12 }" :row-alternation-enabled="true">

                <DxDataGridColumn caption="Codigo Periodo" width="150" data-field="Codigo" />
                <DxDataGridColumn caption="Descripcion Periodo" width="100%" data-field="Descripcion" />
                <DxDataGridColumn caption="Fecha Inicial" width="100%" data-field="FechaInicial" format="dd/MM/yyyy HH:mm" data-type="date" />
                <DxDataGridColumn caption="Fecha Final" width="100%" data-field="FechaFinal" format="dd/MM/yyyy HH:mm" data-type="date" />
                <!-- <DxColumn caption="Cerrado"  width="200" data-field="ComprasBlockeo" /> -->
                <DxDataGridColumn caption="Cerrado" width="150" :allow-resizing="false" data-field="ComprasBlockeo"
                    cell-template="arrowRight" alignment="center" />

                <template #arrowRight="{ data: info }">
                    <div style="display: flex; flex-wrap: wrap; justify-content: left;">
                        <vs-tooltip text="Cambiar estado" position="bottom" style="cursor: pointer;">
                            <font-awesome-icon v-if="info.data.ComprasBlockeo === 'S'" :icon="['fas', 'toggle-on']"
                                style="font-size: 20px; color: #ff4502;" @click="ActivaPeriodo(info)" />
                            <font-awesome-icon v-if="info.data.ComprasBlockeo === 'N'" :icon="['fas', 'toggle-off']"
                                style="font-size: 20px; color: #337ab7;" @click="CerrarPeriodo(info)" />
                        </vs-tooltip>
                        <span class="pl-2">{{ info.data.ComprasBlockeo }}</span>
                    </div>
                </template>

            </DxDataGrid>

        </vs-row>

    </vx-card>
</template>


<script>


export default {
    name: "CON020",

    data() {
        return {
            Periodo: 0,
            DescripcionPeriodo: '',
            listaPeriodos: []
        }
    },
    mounted() {
        
        var currentTime = new Date()
        const year = currentTime.getFullYear()
        this.DescripcionPeriodo =  year.toString()
        

        this.ConsultarPeriodo()
    },
    methods: {

        async CerrarPeriodo(info) {

            if (!info.data.Codigo) {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ED8C72',
                    acceptText: 'Continuar',
                    title: 'Operación',
                    text: 'Periodo requerido'
                })
                return
            }


            this.axios.post('/app/v2_api_compras/CerrarPeriodo', {
                CodigoPeriodo: info.data.Codigo,
            })
            .then(() => {
                info.data.ComprasBlockeo = 'S';
            })
            .catch((error) => {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ED8C72',
                    acceptText: 'Continuar',
                    title: 'Operación',
                    text: 'Error al cerrar el periodo' + error
                })
                
            });


        },
        async ActivaPeriodo(info) {

            if (!info.data.Codigo) {
                this.$vs.dialog({
                    type: 'confirm',
                    color: '#ED8C72',
                    acceptText: 'Continuar',
                    title: 'Operación',
                    text: 'Periodo requerido'
                })

                return
            }

            this.axios.post('/app/v2_api_compras/ActivarPeriodo', {
                CodigoPeriodo: info.data.Codigo,
            })
            .then(() => {
                info.data.ComprasBlockeo = 'N';
            })
            .catch((error) => {
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ED8C72',
                    acceptText: 'Continuar',
                    title: 'Operación',
                    text: 'Error al activar el periodo' + error
                })
            });

        },
        async ConsultarPeriodo() {

            if (this.Periodo == 0 && this.DescripcionPeriodo.trim() == '' ) {
                
                this.$vs.dialog({
                    type: 'alert',
                    color: '#ED8C72',
                    acceptText: 'Continuar',
                    title: 'Operación',
                    text: 'Periodo requerido o Descripción Periodo Requerido '
                })
                return
            }
            
            const resp = await this.axios.post('/app/v1_OrdenCompra/ConsultarPeriodoCierre', {
                descripcion: this.DescripcionPeriodo,
                Periodo: this.Periodo > 0 ? this.Periodo :0
            })
            this.listaPeriodos = resp.data.json

        },
        limpiarDescripcion() {
            this.DescripcionPeriodo = '';
        },
        limpiarPeriodo() {
            this.Periodo = 0;
        },
        buscarPorPeriodo() {
            if (this.Periodo.trim() !== '') {
                this.ConsultarPeriodo();
            }
        },
        buscarPorDescripcion() {
            if (this.DescripcionPeriodo.trim() !== '') {
                this.ConsultarPeriodo();
            }
        },
    }

}


</script>


<style></style>