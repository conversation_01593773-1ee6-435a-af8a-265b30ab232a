<template>
    <div id="app">

        <div class="datos_medico pl-4">

            <!-- <div class="container"> -->

            <div class="row">
            
                <div class="container-fluid" style="vertical-align: middle; margin-top: -15px;">

                    <vs-row>
                        <vs-col vs-w="2" class="pr-10">
                            <ValidationProvider name="Codigo" rules="required|numero_entero|numero_min:1"
                                v-slot="{ errors }" class="required">
                                <SM-Buscar v-model="CodigoMedico" label="Código" api="app/Ajenos/Busqueda_Ajenos"
                                    :api_campo_respuesta_mostrar="['Nombre', 'Apellido']"
                                    :api_campos="['Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo', 'CorreoElectronico']"
                                    :api_titulos="['Codigo', 'Nombre', 'Apellido', 'Corporativo', 'Tipo', 'CorreoElectronico']"
                                    api_campo_respuesta="Codigo" :callback_buscar="DatosMedicos().Consultar"
                                    :dangertext="(errors.length > 0) ? errors[0] : null" :api_preload="true"
                                    :disabled_texto="true" />
                            </ValidationProvider>
                        </vs-col>

                        <vs-col class="pl-4" vs-w="6">
                            <validationProvider name="NombreMedico">
                                <vs-input v-model="NombreMedico" label="Nombre del Medico" class="w-full" readonly />
                            </validationProvider>
                        </vs-col>
                    </vs-row>

                    <div class="datosCorreoMedico align-center">
                        <vs-row>
                            <div class="checkboxStyle pt-4">
                                <vs-col vs-w="2">
                                    <div class="w-full   pt-8">
                                        <BaseCheckboxGroup
                                          :options="checkboxOptions"
                                          v-model="optionChecked"
                                          :groupName="'correoMedico'">
                                          </BaseCheckboxGroup>
                                    </div>
                                </vs-col>
                                <vs-col class="pl-4" vs-w="6">
                                    <vs-input
                                      v-model="CorreoElectronicoMedico"
                                      v-bind:readonly="noCorreoElectronico"
                                      label="Correo Electrónico Médico"
                                      class="w-2/3"
                                      v-on:change="obtieneCorreoMedico()" />
                                </vs-col>
                            </div>
                        </vs-row>
                    </div>

                </div>
            </div>
            <!-- </div> -->
        </div>
    </div>
</template>


<script>

import BaseCheckboxGroup from "@/components/sermesa/global/checkbox/BaseCheckboxGroup";

export default {
    name: "ConsultarMedicos",
    data() {
        return {
            CodigoMedico: '',
            NombreMedico: '',
            CorreoElectronicoMedico: '',
            checkboxOptions: [],
            optionChecked: [],
            noCorreoElectronico:false
        };
    },
    components: {
        BaseCheckboxGroup
    },
    computed: {
        
    },
    mounted() {
        this.CargarOpcionCorreo()
    },
    watch: {
        'CodigoMedico'(newValue) {

            this.$emit('codigo-medico', { CodigoMedico: newValue, CorreoMedico: this.CorreoElectronicoMedico });
        },
        'optionChecked'(newValues) {

            if (!this.CodigoMedico)
                return

            let noMailChecked = 0

            if (newValues.length === 0) {
                noMailChecked = 0    
            }
            else {
                noMailChecked = newValues[0].value
                this.CorreoElectronicoMedico =''
            }
            this.noCorreoElectronico = newValues.length > 0?true:false
            
            this.$emit("no-mail-checked", { CodigoMedico: this.CodigoMedico, CorreoMedico: this.CorreoElectronicoMedico, NoCorreo: noMailChecked });
        }
        
    },
    methods: {
        DatosMedicos() {
            return {

                Consultar: (datos) => {
                    this.NombreMedico = datos.Nombre.trim() + ' ' + datos.Apellido.trim()
                    this.CorreoElectronicoMedico = datos.CorreoElectronico

                },
            }
        },
        CargarOpcionCorreo() {
            this.checkboxOptions = [
                {
                    label: 'No posee correo',
                    value: '1',
                }
            ]
        },
        obtieneCorreoMedico() {

            if (!this.CodigoMedico)
                return

            let noMailChecked = 0
      
            this.$emit("mail-changed", { CodigoMedico: this.CodigoMedico, CorreoMedico: this.CorreoElectronicoMedico, NoCorreo: noMailChecked });

        }

    }

}


</script>



<style scoped>
.datos_medico {
    padding-top: 2rem;
    padding-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
    border: 1px solid #888;
    border-radius: 5px;
    margin: 4px;
}

.datosCorreoMedico {
    align-items: center;
}

.align-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.checkboxStyle {
    display: flex;
    justify-content: left;
    align-items: left;
    width: 100%;
}
</style>

