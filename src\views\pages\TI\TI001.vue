<template>
    <div>
        <div class="container">

            <div class="notificacion " @click="barra.mostrar = true">
                <i class="fas fa-laptop-medical"></i>
                <div class="titulo">Soporte</div>
                <div v-if="conteo.pendientes + conteo.pendientesqueAsig > 0" class="notificaciones">{{ conteo.pendientes +
                    conteo.pendientesAsig }}</div>
            </div>

            <div class="notificacion" @click="() => { popupActivo = true; ConsultaHorario().archivo() }">
                <i class="fas fa fa-calendar">
                </i>
                <div class="titulo">Horario Soporte</div>
            </div>

        </div>


        <vs-popup ref="buscador" title="Soporte" :active.sync="detalle.mostrar" style="z-index:52001" id="div-with-loading">
            <div v-if="detalle.info">
                <table width="100%">
                    <tr>
                        <td colspan="2">
                            <b><small>Ticket</small></b>
                            <h3>{{ detalle.info.Codigo }}</h3>
                            <hr>
                        </td>
                    </tr>
                    <tr>
                        <td width="50%">
                            <b> <small>Usuario</small></b>
                            <br>
                            {{ detalle.info.Usuario }} - {{ detalle.info.NombreUsuario }}
                            <br>
                            <b> <small>Area</small></b>
                            <br>
                            {{ detalle.info.Area }}|
                            <br>
                            <b> <small>Extensión</small></b>
                            <br>
                            {{ detalle.info.Extension }}
                        </td>
                        <td>
                            <b> <small>Hospital</small><br></b>
                            {{ (detalle.info.Hospital) ? listado.hospitales.filter(f => f.Codigo ==
                                detalle.info.Hospital).length > 0 ? listado.hospitales.filter(f => f.Codigo ==
                                    detalle.info.Hospital)[0].Nombre : null : null }}
                            <br>
                            <b> <small>Tipo</small></b>
                            <br>
                            {{ (detalle.info.IdTipo) ? listado.tipo.filter(f => f.value == detalle.info.IdTipo)[0].text :
                                null }}
                            <br>
                            <b> <small>Fecha</small></b>
                            <br>
                            {{ detalle.info.Fecha }}
                        </td>
                    </tr>
                </table>
                <!-- {{detalle.info}} -->
                <b><small>Descripción</small></b>
                <br>
                <div v-html="Otros().limpiarDescripcion(detalle.info.SDescrpcion)"
                    style="border:1px solid #ccc;border-radius:4px; padding:5px;height:100px;overflow:auto;font-size:12px;white-space:pre-wrap;">
                </div>

                <vs-divider></vs-divider>
                <div v-if="((!detalle.info.respuesta && !detalle.info.asignar) || detalle.info.Status != 'P')">
                    <vs-divider></vs-divider>
                    <b><small>Problema</small></b>
                    <br>
                    {{ detalle.info.Sproblema ? detalle.info.Sproblema : 'Sin Información' }}
                    <vs-divider></vs-divider>
                    <b><small>Analisis</small></b>
                    <br>
                    {{ detalle.info.SAnalisis ? detalle.info.SAnalisis : 'Sin Información' }}
                    <vs-divider></vs-divider>
                    <table width="100%">
                        <td v-if="detalle.info.NombreTecnico">
                            <b><small>Tecnico Asignado:</small></b>
                            <br>
                            {{ detalle.info.NombreTecnico }}
                        </td>
                        <td v-if="detalle.info.NombreTecnicoFinaliza">
                            <b><small>Tecnico Finalizo:</small></b>
                            <br>
                            {{ detalle.info.NombreTecnicoFinaliza }}
                        </td>
                        <td>
                            <b><small>Fecha Finalización</small></b>
                            <br>
                            {{ detalle.info.FechaFinalizado ? detalle.info.FechaFinalizado : 'Sin Información' }}
                        </td>
                        <td>
                            <b><small>Estado</small></b>
                            <br>
                            <div v-if="detalle.info.Status == 'S'"><i style="color:#2ECC71;font-size:20px"
                                    class="far fa-check-circle"></i> Finalizado</div>
                            <div
                                v-else-if="detalle.info.TecnicoAsigna != null && detalle.info.TecnicoAsigna != '' && detalle.info.Status == 'P'">
                                <i class="fas fa-user-check" style="color:#3498DB;font-size:20px"></i> Asignado
                            </div>
                            <div v-else-if="detalle.info.Status == 'R'"><i style="color:#E74C3C;font-size:20px"
                                    class="far fa-times-circle"></i> Rechazado </div>
                            <div v-else><i style="color:#E67E22;font-size:20px" class="far fa-paper-plane"></i> Sin Asignar
                            </div>
                        </td>
                    </table>
                </div>
                <!-- Respueta del técnico -->
                <div v-else>
                    <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">

                        <div v-if="detalle.info.asignar">
                            <ValidationProvider name="Asignar" rules="required" v-slot="{ errors }" class="required">
                                <SM-Buscar label="Asignar a:" v-model="detalle.info.usuarioAsignar"
                                    api="app/administracion/Busqueda_Usuario_Privilegio_Soporte"
                                    :api_campos="['IdCorporativo', 'Nombre', 'Asignaciones']"
                                    :api_titulos="['#Corporativo', 'Nombre', 'Asignaciones#']"
                                    :api_filtro="{ NombreFuncionalidad: 'TI001', NombrePrivilegio: 'RESPUESTA' }"
                                    api_campo_respuesta="IdCorporativo" api_campo_respuesta_mostrar="Nombre"
                                    :api_preload="true" :disabled_texto="true" :mostrar_busqueda="true"
                                    :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" />
                            </ValidationProvider>
                        </div>

                        <div v-else>
                            <b><small>Problema</small></b>
                            <br>
                            <ValidationProvider name="Problema" rules="required|max:1500" v-slot="{ errors }"
                                class="required">
                                <vs-textarea class="mt-4" counter="1500" label="Descripción"
                                    v-model="detalle.info.Sproblema" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" required />
                            </ValidationProvider>
                            <vs-divider></vs-divider>
                            <b><small>Analisis</small></b>
                            <br>
                            <ValidationProvider name="Analisis" rules="required|max:1500" v-slot="{ errors }"
                                class="required">
                                <vs-textarea class="mt-4" counter="1500" label="Descripción"
                                    v-model="detalle.info.SAnalisis" :danger="errors.length > 0"
                                    :danger-text="(errors.length > 0) ? errors[0] : null" required />
                            </ValidationProvider>
                            <vs-divider></vs-divider>
                        </div>
                                                <br>
                        <div class="vx-row">
                            <div class="vx-col   w-full ml-auto">
                                <vs-button color="danger" type="border" v-if="detalle.info.asignar"
                                    @click.native="rechazar.mostrar = true; rechazar.Codigo = detalle.info.Codigo"
                                    class="mr-3 mb-2">Rechazar Solicitud</vs-button>
                                <vs-button style="float:right"
                                    @click.native="handleSubmit((detalle.info.asignar) ? Guardar().Asignar(detalle.info) : Guardar().Respuesta(detalle.info))"
                                    :disabled="invalid" class="mr-3 mb-2">Guardar</vs-button>
                            </div>
                        </div>
                    </ValidationObserver>
                </div>
            </div>
        </vs-popup>

        <!-- 
        RECHAZAR
     -->
        <vs-popup ref="buscador" title="Rechazar Soporte" :active.sync="rechazar.mostrar" style="z-index:52002"
            id="div-with-loading" class="vs-con-loading__container">
            <ValidationObserver ref="formValidate" v-slot="{ invalid, handleSubmit }" mode="lazy">
                <ValidationProvider name="Codigo" rules="required|max:1500" v-slot="{ errors }" class="required">
                    <vs-textarea class="mt-4" counter="1500" label="Analisis" v-model="rechazar.Analisis"
                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null" required />
                </ValidationProvider>
                <div class="vx-row">
                    <div class="vx-col   w-full ml-auto">
                        <vs-button style="float:right" @click.native="handleSubmit(Guardar().Rechazar(rechazar))"
                            :disabled="invalid" class="mr-3 mb-2">Aceptar</vs-button>
                    </div>
                </div>
            </ValidationObserver>
        </vs-popup>

        <!-- 
        Pantalla Principal 
    -->
        <vs-popup ref="buscador" title="Soporte" :active.sync="barra.mostrar" style="z-index:52000" id="div-with-loading"
            class="Ticket">
            <div style="position:relative" v-if="barra.mostrar">
                <i class="fas fa-laptop-medical fondo"></i>
                <SM-Tabs v-model="opcion">
                    <div index="0" class="tab" label="Nueva Solicitud" style="padding:10px">
                        <ValidationObserver ref="solicitud" v-slot="{ invalid, handleSubmit }" mode="lazy">
                            <h4>Nueva Solicitud</h4>
                            <div
                                style="border:2px solid rgba(231, 76, 60,0.5);border-radius:5px;padding:5px;font-size:12px;background-color:rgba(231, 76, 60,0.2)">
                                Toda solicitud en horario inhábil (4:30 pm - 6:59 am) no cambia su proceso, deben llamar a
                                recepción solicitando comunicarse con el técnico de llamada.
                            </div>
                            <div class="content content-pagex" style="padding-left:5px; padding-right:5px">
                                <div class="flex flex-wrap ">
                                    <div class=" mb-4 bg-grid-color-secondary h-12 mt-5">
                                        <b><small>
                                                Usuario:
                                            </small></b>
                                        <br>[{{ sesion.corporativo }}] {{ sesion.usuario }}
                                    </div>
                                </div>
                                
                                <!-- <vs-divider></vs-divider> -->
                                <div class="flex flex-wrap ">
                                    <div class="w-1/3  mb-4 bg-grid-color-secondary">
                                        <ValidationProvider name="area" rules="required" v-slot="{ errors }"
                                            class="required">
                                            <!-- <vs-input label="Area" v-model="info.area" :danger="errors.length>0" :danger-text="(errors.length>0)?errors[0]:null" required></vs-input> -->
                                            <vs-select label="Area" class=" w-full" name="item-category" v-model="info.area"
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" required>
                                                <vs-select-item :key="item.area" :value="item.text" :text="item.value"
                                                    v-for="item in listado.areas" />
                                            </vs-select>
                                        </ValidationProvider>
                                    </div>
                                    <div class="w-1/3 mb-4 bg-grid-color h-12 pl-1">
                                        <ValidationProvider name="extension" rules="required|max:20" v-slot="{ errors }"
                                            class="required">
                                            <vs-input label="Extensión" v-model="info.extension" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" required></vs-input>
                                        </ValidationProvider>
                                    </div>
                                </div>
                                <div class="flex flex-wrap ">
                                    <div class="w-1/2  mb-4 bg-grid-color-secondary h-12">
                                        <ValidationProvider name="Hospital" rules="required" v-slot="{ errors }"
                                            class="required">
                                            <vs-select label="Hospital" class="mt-5 w-full" name="item-category"
                                                v-model="info.hospital" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" required>
                                                <vs-select-item :key="item.Codigo" :value="item.Codigo" :text="item.Nombre"
                                                    v-for="item in listado.hospitales" />
                                            </vs-select>
                                        </ValidationProvider>
                                    </div>
                                    <div class="w-1/2 mb-4 bg-grid-color h-12 pl-1">
                                        <ValidationProvider name="Tipo" rules="required" v-slot="{ errors }"
                                            class="required">
                                            <vs-select label="Tipo" class="mt-5 w-full" name="item-category"
                                                v-model="info.tipo" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" required>
                                                <vs-select-item :key="item.value" :text="item.text" :value="item.value"
                                                    v-for="item in listado.tipo" />
                                            </vs-select>
                                        </ValidationProvider>
                                    </div>
                                </div>
                                <br>
                                <ValidationProvider name="Codigo" rules="required|max:1500" v-slot="{ errors }"
                                    class="required">
                                    <vs-textarea class="mt-4" counter="1500" label="Descripción" v-model="info.descripcion"
                                        :danger="errors.length > 0" :danger-text="(errors.length > 0) ? errors[0] : null"
                                        required height="170px" />
                                </ValidationProvider>
                                <br>
                                <div class="vx-row">
                                    <div class="vx-col   w-full ml-auto" style="text-align:right">
                                        <!-- <vs-button color="danger" type="border" class="mb-2" @click="input1 = input2 = input3 = input4 = input4 = ''; check1 = false;" style="margin-right:7px">Cancelar</vs-button> -->
                                        <vs-button color="success" @click.native="handleSubmit(Guardar().Solicitud)"
                                            :disabled="invalid" class="mr-3 mb-2">Enviar</vs-button>
                                    </div>
                                </div>

                            </div>
                        </ValidationObserver>
                    </div>

                    <div index="1" class="tab" label="Mis Solicitudes" style="padding:10px">
                        <h4>Mis Solicitudes</h4>
                        <div class="content content-pagex" style="padding-left:5px; padding-right:5px">
                            <div class="flex flex-wrap ">
                                <div class=" mb-4 bg-grid-color-secondary h-12 mt-5">
                                    <b><small>
                                            Usuario:
                                        </small></b>
                                    <br>[{{ sesion.corporativo }}] {{ sesion.usuario }}
                                </div>
                            </div>
                            <br>
                            <b>Estado:</b> <i class="far fa-paper-plane" style="color:#E67E22;"></i> Enviado / <i
                                class="fas fa-user-check" style="color:#3498DB"></i> Asignado / <i
                                class="far fa-check-circle" style="color:#2ECC71"></i> Finalizado / <i style="color:#E74C3C"
                                class="far fa-times-circle"></i> Rechazada
                            <vs-table2 max-items="10" search pagination :data="listado.misSolicitudes">
                                <template slot="thead">
                                    <th width="70px">No.</th>
                                    <th width="210px">Información</th>
                                    <th>Descripción</th>
                                    <th width="70px">Estado</th>
                                </template>
                                <template slot-scope="{data}">
                                    <tr :key="indextr" v-for="(tr, indextr) in data" style="cursor:pointer"
                                        @click="detalle.info = tr; detalle.mostrar = true">
                                        <vs-td2>
                                            {{ tr.Codigo }}
                                        </vs-td2>
                                        <vs-td2>
                                            {{ (tr.Hospital) ? listado.hospitales.filter(f => f.Codigo ==
                                                tr.Hospital).length > 0 ? listado.hospitales.filter(f => f.Codigo ==
                                                    tr.Hospital)[0].Nombre : null : null }}
                                            <br>
                                            <small>{{ (tr.IdTipo) ? listado.tipo.filter(f => f.value == tr.IdTipo)[0].text :
                                                null }}</small>
                                            <br>
                                            <small>{{ $formato_fecha(tr.Fecha) }}</small>
                                        </vs-td2>
                                        <vs-td2 notTooltip>
                                            <div style="height:50px" v-html="Otros().limpiarDescripcion(tr.SDescrpcion)">
                                            </div>
                                        </vs-td2>
                                        <vs-td2 noTooltip>
                                            <!-- {{tr.TecnicoAsigna}} -->
                                            <i v-if="tr.Status == 'S'" style="color:#2ECC71;font-size:20px"
                                                class="far fa-check-circle"></i>
                                            <i v-else-if="tr.TecnicoAsigna != null && tr.TecnicoAsigna != '' && tr.Status == 'P'"
                                                class="fas fa-user-check" style="color:#3498DB;font-size:20px"></i>
                                            <i v-else-if="tr.Status == 'R'" style="color:#E74C3C;font-size:20px"
                                                class="far fa-times-circle"></i>
                                            <i v-else style="color:#E67E22;font-size:20px" class="far fa-paper-plane"></i>

                                        </vs-td2>
                                    </tr>
                                </template>
                            </vs-table2>

                        </div>
                    </div>
                    <div v-if="permisos.asignar" index="2" class="tab" label="Asignación de Solicitudes"
                        style="padding:10px">
                        <h4>Asignar Solicitud</h4>
                        <vs-table2 max-items="10" search pagination :data="listado.pendientes">
                            <template slot="thead">
                                <th width="70px">No.</th>
                                <th>Información</th>
                                <th>Asignado</th>
                                <th width="70px">Acción</th>
                            </template>

                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.Codigo }}
                                    </vs-td2>
                                    <vs-td2>
                                        <div v-html="tr.Detalle"></div>
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.NombreTecnico }}
                                    </vs-td2>
                                    <vs-td2 notTooltip>
                                        <vs-button color="success" size="small" icon-pack="fas" icon=" fa-user-plus"
                                            class="mr-1" style="display:inline-block"
                                            v-on:click="detalle.info = { ...tr }; detalle.mostrar = true"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>

                    <div v-if="permisos.listado" index="3" class="tab" label="Listado Solicitudes" style="padding:10px">
                        <h4>Listado de Solicitudes</h4>
                        <apexchart v-if="listado.todas.length > 0" type="donut" width="340" :options="chartOptions"
                            :series="series"></apexchart>
                        <vs-table2 max-items="10" search pagination :data="listado.todas">
                            <template slot="thead">
                                <th width="50px">No.</th>
                                <th>Información</th>
                                <th>Asignado</th>
                                <th width="40px">Estatus</th>
                                <th width="70px">Acción</th>
                            </template>

                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data">
                                    <vs-td2>
                                        {{ tr.Codigo }}
                                    </vs-td2>
                                    <vs-td2>
                                        <div v-html="Otros().limpiarDescripcion(tr.Detalle)"></div>
                                    </vs-td2>
                                    <vs-td2>
                                        <small>
                                            <div v-if="tr.NombreTecnico">
                                                <b>Asignado:</b><br>
                                                {{ tr.NombreTecnico }}
                                            </div>
                                            <div v-if="tr.NombreTecnicoFinaliza">
                                                <b>Finalizo:</b><br>
                                                {{ $formato_fecha(tr.NombreTecnicoFinaliza) }}
                                            </div>
                                            <div v-if="tr.FechaFinalizado">
                                                <b>Fecha Finalización:</b><br>
                                                {{ $formato_fecha(tr.FechaFinalizado) }}
                                            </div>
                                        </small>
                                    </vs-td2>
                                    <vs-td2>
                                        <i v-if="tr.Status == 'S'" style="color:#2ECC71;font-size:20px"
                                            class="far fa-check-circle"></i>
                                        <i v-else-if="tr.TecnicoAsigna != null && tr.TecnicoAsigna != '' && tr.Status == 'P'"
                                            class="fas fa-user-check" style="color:#3498DB;font-size:20px"></i>
                                        <i v-else-if="tr.Status == 'R'" style="color:#E74C3C;font-size:20px"
                                            class="far fa-times-circle"></i>
                                        <i v-else style="color:#E67E22;font-size:20px" class="far fa-paper-plane"></i>
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <vs-button color="success" size="small" icon-pack="fas" icon=" fa-info" class="mr-1"
                                            style="display:inline-block"
                                            v-on:click="detalle.info = { ...tr }; detalle.mostrar = true"></vs-button>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>

                    <div v-if="permisos.respuesta" index="4" class="tab" label="Solicitud Pendientes" style="padding:10px">
                        <h4>Solicitud Pendientes</h4>
                        <vs-table2 max-items="10" search pagination :data="listado.pendientesAsig">
                            <template slot="thead">
                                <th width="70px">No.</th>
                                <th width="210px">Información</th>
                                <th>Descripción</th>
                                <th width="70px">Estado</th>
                            </template>

                            <template slot-scope="{data}">
                                <tr :key="indextr" v-for="(tr, indextr) in data" style="cursor:pointer"
                                    @click="detalle.info = { ...tr }; detalle.mostrar = true">
                                    <vs-td2>
                                        {{ tr.Codigo }}
                                    </vs-td2>
                                    <vs-td2>
                                        {{ tr.Usuario }} - {{ tr.NombreUsuario }}
                                        <br>
                                        <small>{{ (tr.Hospital) ? listado.hospitales.filter(f => f.Codigo ==
                                            tr.Hospital).length > 0 ? listado.hospitales.filter(f => f.Codigo ==
                                                tr.Hospital)[0].Nombre : null : null }}</small>
                                        <br>
                                        <small>{{ (tr.IdTipo) ? listado.tipo.filter(f => f.value == tr.IdTipo)[0].text :
                                            null }}</small>
                                        <br>
                                        <small>{{ $formato_fecha(tr.Fecha) }}</small>
                                    </vs-td2>
                                    <vs-td2 notTooltip>
                                        <div style="height:50px" v-html="Otros().limpiarDescripcion(tr.SDescrpcion)"></div>
                                    </vs-td2>
                                    <vs-td2 noTooltip>
                                        <i v-if="tr.Status == 'R'" style="color:#E74C3C;font-size:20px"
                                            class="far fa-times-circle"></i>
                                        <i v-else-if="!tr.FechaFinalizado || tr.FechaFinalizado == ''"
                                            style="color:#E67E22;font-size:20px" class="fas fa-exclamation-circle"></i>
                                        <i v-else style="color:#2ECC71;font-size:20px" class="far fa-check-circle"></i>
                                    </vs-td2>
                                </tr>
                            </template>
                        </vs-table2>
                    </div>

                    <div  v-if="permisos.Status"  index="5" class="tab" label="Estados Solicitudes Por Técnico" style="padding:10px">
                        <ValidationObserver ref="solicitud"  mode="lazy">
                            <h4>Estados Solicitudes Por Técnico</h4>
                            
                            <div class="content content-pagex" style="padding-left:5px; padding-right:5px">
                                <div class="flex flex-wrap ">
                                    <div class=" mb-4 bg-grid-color-secondary h-12 mt-5">
                                        <b><small>
                                                Usuario:
                                            </small></b>
                                        <br><label for="" style="color: black;">{{ sesion.corporativo }}</label> <label for="" style="color: BLA;">{{ sesion.usuario }}</label>
                                    </div>
                            </div>
                                


                                <!-- <vs-divider></vs-divider> -->
                                <div class="w-1/1  mb-4 bg-grid-color-secondary">
                                         <ValidationProvider name="area" rules="required" v-slot="{ errors }"
                                            class="required">
                                            <vs-select
                                                placeholder="Selección Técnico"
                                                class="w-full"
                                                label="Tecnicos"
                                                label-placeholder="Autocomplete"
                                                multiple
                                                autocomplete
                                                v-model="SelectTecnicos"
                                                :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" required >
                                                <vs-select-item :key="item.Tecnico" :value="item.value" :text="item.text"
                                                v-for="item in listado.Tecnico" />
                                                </vs-select>
                                        </ValidationProvider> 
                                    </div>
                                <div style="display: flex;">
                                    
                                    <div class="flex flex-wrap ">
                                        <div class="w-1/2 mb-4 bg-grid-color h-12 pl-1">
                                            <ValidationProvider name="extension" rules="required|max:20" v-slot="{ errors }"
                                                class="required">
                                                <vs-input label="Fecha Inicio"     type="date" v-model="info.Fecha" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null" required></vs-input>
                                            </ValidationProvider>
                                        </div>
                                        
                                        <div class="w-1/2 mb-4 bg-grid-color h-12 pl-1">
                                            <ValidationProvider name="extension" rules="required|max:20" v-slot="{ errors }"
                                                class="required">
                                                <vs-input label="Fecha Final"     type="date" v-model="info.FechaFinalizado" :danger="errors.length > 0"
                                                    :danger-text="(errors.length > 0) ? errors[0] : null" required></vs-input>
                                            </ValidationProvider>
                                        </div>
                                    </div>
                                </div>
                                <div style="display: flex;">
                                    <div class="w-1/4 mb-4 bg-grid-color h-12 pl-1">
                                        <ValidationProvider name="Tipo" rules="required" v-slot="{ errors }"
                                            class="required">
                                            <vs-select label="Tipo" class="mt-5 w-full" name="item-category"
                                                v-model="info.tipoD" :danger="errors.length > 0"
                                                :danger-text="(errors.length > 0) ? errors[0] : null" required>
                                                <vs-select-item :key="item.value" :text="item.text" :value="item.value"
                                                    v-for="item in listado.TipoCasoReporte" />
                                            </vs-select>
                                        </ValidationProvider>
                                    </div>
                                    <div style="margin-top: 35px; margin-left: 20px; display:flex;" class="w-1/2 mb-4 h-12 pl-1">
                                        <vs-button  type="filled" @click="ConsultaTickes">Consultar</vs-button>&nbsp;
                                        <vs-button color="danger" type="filled" @click="Limpiar()" >Limpiar</vs-button>&nbsp;
                                        <vs-button color="success" type="filled" @click="CargarExcel()">Excel</vs-button>&nbsp;
                                    </div> 
                                </div>
                                  <div>
                                        <vs-table2
                                        max-items="5"
                                        pagination
                                        :data="TickesTecnicos"
                                        class="mt-0 mb-0"
                                        >
                                        <template slot="thead">
                                            <th order="Codigo">Tickect</th>
                                            <th oorder="HOSPITAL" width="100px">Hospital</th>
                                            <th order="TecnicoFinaliza">Tecnico</th>
                                            <th>Usuario Atendido</th>
                                            <th>Departamento</th>
                                            <th>Analicis</th>
                                            <th>Descrioción</th>
                                            <th>Fecha Creado</th>
                                            <th>Fecha Finalizado</th>
                                            <th>Fecha Asignacion</th>
                                        </template>
                                        <template slot-scope="{ data }">
                                            <tr :key="indextr" v-for="(tr, indextr) in data" @click="showTicketDetails(tr)">
                                            <vs-td2><label>{{ tr.Codigo }}</label></vs-td2>
                                            <vs-td2><label>{{ tr.HOSPITAL }}</label></vs-td2>
                                            <vs-td2><label>{{ tr.Tecnico }}</label></vs-td2>
                                            <vs-td2><label>{{ tr.UsuarioAtendido }}</label></vs-td2>
                                            <vs-td2><label>{{ tr.departamento }}</label></vs-td2>
                                            <vs-td2><label>{{ tr.sanalisis }}</label></vs-td2>
                                            <vs-td2><label>{{ tr.sdescrpcion }}</label></vs-td2>
                                            <vs-td2><label>{{ tr.FECHA }}</label></vs-td2>
                                            <vs-td2><label>{{ tr.FECHAFINALIZADO }}</label></vs-td2>
                                            <vs-td2><label>{{ tr.FechaAsignacion }}</label></vs-td2>
                                            </tr>
                                        </template>
                                        </vs-table2>
                                    </div>

                            </div>
                        </ValidationObserver>
                    </div>

                
                </SM-Tabs>
             
            </div>
        </vs-popup>

        <vs-popup title="Detalle Ticket" style="z-index:52002" :active.sync="popupActivoTecnicos">
            <div style="display: flex;">
                <h1 style="display: flex;">No.<h1></h1> {{ selectedTicket.Codigo }}</h1>
            </div>
            <div style="display: flex;">
                    <vx-card>
                        <vs-list-header title="Detalle Técnico" color="success"></vs-list-header>
                        <vs-list-item title="Técnico Finaliza:"><p style="color: black;">{{ selectedTicket.Tecnico }}</p></vs-list-item>
                        <vs-list-item title="Nombre:"><p style="color: black;">{{ selectedTicket.NombreTecnico || selectedTicket.NombreTecnicoP }}</p></vs-list-item>
                        <vs-list-item title="Hospital:"><p style="color: black;">{{ selectedTicket.HOSPITAL }}</p></vs-list-item>
                        <vs-list-item title="Nombre Hospital:"><p style="color: black;">{{ selectedTicket.NombreHospital }}</p></vs-list-item>
                    </vx-card>
                    &nbsp;
                    <vx-card>
                        <vs-list-header title="Detalle Ticket" color="success"></vs-list-header>
                        <vs-list-item title="Usuario Atendido:"><p style="color: black;">{{ selectedTicket.UsuarioAtendido }}</p></vs-list-item>
                        <vs-list-item title="Nombre:"><p style="color: black;">{{ selectedTicket.NombreUsuario }}</p></vs-list-item>
                        <vs-list-item title="Fecha Creado:"><p style="color: black;">{{ selectedTicket.FECHA }}</p></vs-list-item>
                        <vs-list-item title="Fecha Finalizado:"><p style="color: black;">{{ selectedTicket.FECHAFINALIZADO }}</p></vs-list-item>
                        <vs-list-item title="Fecha Finalizado:"><p style="color: black;">{{ selectedTicket.FechaAsignacion }}</p></vs-list-item>
                    </vx-card> 
            </div>
            <div style="margin-top: 10px;">
                <vs-textarea counter="400" label="Analisis:" :counter-danger.sync="counterDanger" disabled v-model="selectedTicket.sanalisis" />
                <vs-textarea counter="400" label="Descripción" :counter-danger.sync="counterDanger" disabled v-model="selectedTicket.sdescrpcion" />
            </div>
        </vs-popup>
      
        <!--
            Horarios Tecnivos
        -->
        <vs-popup ref="buscador " title="Horario Soporte" :active.sync="popupActivo" style="z-index:52000"

            id="div-with-loading">
            <div>
                <div class="tab" style="padding:5px">
                    <div class="container">
                        <div class="calendario">
                            <center>
                                <h3>-- Calendario de Turnos - Soporte Técnico --</h3>
                                <div id="app">
                                    <FechaYMes />
                                </div>
                            </center>
                                <div class="w-full md:w-1/5 lg:w-1/5 xl:w-1/5 p-2 imagen">
                                    <img :src="'data:image/png;base64,' + imagen_src" width="800" >
                                </div>
                            <center>
                                <div class="tamaño">
                                    <p>
                                    Tomar en cuenta que entre semana en días hábiles (LUNES A VIERNES) el turno da inicio a
                                    las
                                    16:30
                                    hrs y finaliza el día siguiente a las
                                    07:30 hrs. Durante el horario hábil de labores pueden comunicarse al Helpdesk a las
                                    extensiones
                                    16555 y 16556. Los fines de semana
                                    deben comunicarse con el técnico de turno ya que los compañeros de Call Center no
                                    laboran. Tomar
                                    en
                                    cuenta que el turno del fin de
                                    semana inicia el día viernes a las 16:30 hrs y finaliza el día lunes a las 7:00 am.
                                    de tener algun inconveniente puede comunicarse con la persona encargada de la coordinación de soporte. 
                                    
                                    Datos del Coordinación:
                                    Gerbert  Guillermo Pelico Noj No.5631-9783 Extensión:11037
                                    </p>
                                </div>
                            </center>
                        </div>
                        <div class="Contactos">
                            <center>
                                <Font FACE="impact" SIZE=7 COLOR="#3498DB">
                                    Contactos
                                </Font>
                            </center>
                            <center>
                                <Font FACE="impact" SIZE=5 COLOR="#3498DB">
                                    Horario Hábil
                                </Font>
                            </center>
                            <table class="content-table">
                                <thead>
                                    <tr>
                                        <th>
                                            <i class="fa fa-user"></i>
                                            Técnico
                                        </th>
                                        <th>
                                            <i class="fa fa-mobile"></i>
                                            Extensión
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            Guillermo Juárez
                                        </td>
                                        <td>
                                            Ext.16555
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Selvyn Ixtacuy 
                                        </td>
                                        <td>
                                            Ext.16556
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <center>
                                <Font FACE="impact" SIZE=5 COLOR="#3498DB">
                                    Horario Inhábil
                                </Font>
                                <br>
                                <Font face="impact" size=3 color="#3498DB">
                                    Según Calendario
                                </Font>
                            </center>
                            <table class="content-table">
                                <thead>
                                    <tr>
                                        <th>
                                            <i class="fa fa-user"></i>
                                            Técnico
                                        </th>
                                        <th>
                                            <i class="fa fa-mobile"></i>
                                            Teléfono
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            Anderson Isem
                                        </td>
                                        <td>
                                            3757-1107
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            David Gonzalez
                                        </td>
                                        <td>
                                            3757-3971
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Douglas Coc
                                        </td>
                                        <td>
                                            5631-1754
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Erick Godoy
                                        </td>
                                        <td>
                                            4028-4735
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Gilberto Lopez
                                        </td>
                                        <td>
                                            5978-1850
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Heder Cividanis
                                        </td>
                                        <td>
                                            5631-1762
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                                <div>
                                    <div v-if="permisos.cambiar_imagen">
                                        <SMHorario :v-show="false" api='/app/externo/ArchivosVariables_DB' @metodo_emit="ConsultaHorarios"
                                        :api_parametros="{ idControl: 'WEBROLTECNICOS' }"></SMHorario>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </vs-popup>

    </div>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
import SMHorario from '/src/components/sermesa/modules/archivos/SMHorario.vue'
import FechaYMes from '/src/components/sermesa/modules/horario/FechaYMes.vue';


export default {
    data() {
        return {

            popupActivo: false,
            popupActivoTecnicos : false,

            imagen_src: null,

            //Espacio Actual
            opcion: 0,

            TickesTecnicos:[],
            showModal: false,
            selectedTicket: {},

            series: [],
            chartOptions: {
                chart: {
                    width: 250,
                    type: 'donut',
                },
                labels: ['Asignado', 'Finalizado', 'Pendiente de Asignar', 'Rechazado', 'Horarios'],
                dataLabels: {
                    enabled: false
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: 200
                        },
                        legend: {
                            show: false
                        }
                    }
                }],
                legend: {
                    formatter: function (val, opts) {
                        const valor = opts.w.globals.series[opts.seriesIndex].toString()
                        return valor.padStart(2, '0') + ' - ' + val
                    },
                    position: 'right',
                    offsetY: 0,
                    height: 230,
                }
            },


            barra: {
                mostrar: false
            },
            detalle: {
                mostrar: false,
                info: null
            },

            rechazar: {
                mostrar: false,
                Codigo: null,
                Analisis: null
            },

            info: {
                area: null,
                extensión: null,
                hospital: null,
                tipo: null,
                descripcion: '',

                // Datos para GenerarReporte 
                corporativoTecnico: '',
                FechaFinalizado: '',
                Fecha:'',
                tipoD:''
            },

      
            counterDanger: false,


            listado: {
                areas: [
                    {
                        text: "Admisiones",
                        value: "Admisiones",
                    },
                    {
                        text: "Autorizaciones SASI",
                        value: "Autorizaciones SASI",
                    },
                    {
                        text: "Autorizaciones",
                        value: "Autorizaciones",
                    },
                    {
                        text: "Bodega",
                        value: "Bodega",
                    },
                    {
                        text: "Cafetería",
                        value: "Cafetería",
                    },
                    {
                        text: "Caja Central",
                        value: "Caja Central",
                    },
                    {
                        text: "Caja Clínica",
                        value: "Caja Clínica",
                    },
                    {
                        text: "Caja Diagnóstico",
                        value: "Caja Diagnóstico",
                    },
                    {
                        text: "Clínicas",
                        value: "Clínicas",
                    },
                    {
                        text: "Cocina",
                        value: "Cocina",
                    },
                    {
                        text: "Comercial Seguros",
                        value: "Comercial Seguros",
                    },
                    {
                        text: "Compras",
                        value: "Compras",
                    },
                    {
                        text: "Contabilidad",
                        value: "Contabilidad",
                    },
                    {
                        text: "Contraloría",
                        value: "Contraloría",
                    },
                    {
                        text: "Coordinación",
                        value: "Coordinación",
                    },
                    {
                        text: "Diagnóstico",
                        value: "Diagnóstico",
                    },
                    {
                        text: "Dirección Médica",
                        value: "Dirección Médica",
                    },
                    {
                        text: "Emergencia",
                        value: "Emergencia",
                    },
                    {
                        text: "Enfermería",
                        value: "Enfermería",
                    },
                    {
                        text: "Farmacia",
                        value: "Farmacia",
                    },
                    {
                        text: "Gerencia",
                        value: "Gerencia",
                    },
                    {
                        text: "Honorarios Médicos",
                        value: "Honorarios Médicos",
                    },
                    {
                        text: "Información",
                        value: "Información",
                    },
                    {
                        text: "Intensivo",
                        value: "Intensivo",
                    },
                    {
                        text: "Inventarios",
                        value: "Inventarios",
                    },
                    {
                        text: "Jurídico",
                        value: "Jurídico",
                    },
                    {
                        text: "Laboratorio",
                        value: "Laboratorio",
                    },
                    {
                        text: "Lavandería y limpieza",
                        value: "Lavandería y limpieza",
                    },
                    {
                        text: "Liquidaciones",
                        value: "Liquidaciones",
                    },
                    {
                        text: "Mantenimiento",
                        value: "Mantenimiento",
                    },
                    {
                        text: "Mercadeo",
                        value: "Mercadeo",
                    },
                    {
                        text: "Parenteral",
                        value: "Parenteral",
                    },
                    {
                        text: "Parqueo",
                        value: "Parqueo",
                    },
                    {
                        text: "Patología",
                        value: "Patología",
                    },
                    {
                        text: "Pediatría",
                        value: "Pediatría",
                    },
                    {
                        text: "Proyectos",
                        value: "Proyectos",
                    },
                    {
                        text: "Recursos Humanos",
                        value: "Recursos Humanos",
                    },
                    {
                        text: "Sala Cuna",
                        value: "Sala Cuna",
                    },
                    {
                        text: "Sala de Operaciones",
                        value: "Sala de Operaciones",
                    },
                    {
                        text: "Seguros",
                        value: "Seguros",
                    },
                    {
                        text: "Servicio al Cliente",
                        value: "Servicio al Cliente",
                    },
                    {
                        text: "Subcordinación",
                        value: "Subcordinación",
                    },
                    {
                        text: "Tecnología",
                        value: "Tecnología",
                    },
                    {
                        text: "Terapia respiratoria",
                        value: "Terapia respiratoria",
                    },
                    {
                        text: "Transportes",
                        value: "Transportes",
                    }],

                Tecnico:[{
                    text:'Anderson Isem',
                    value: 69794
                },
                {
                    text:'Aramis Lopez',
                    value: 61175
                },
                {
                    text:'Douglas Coc',
                    value: 37407
                },
                {
                    text:'David Gonzales',
                    value: 72153
                },
                {
                    text:'Erick Calito',
                    value: 46047
                },
                {
                    text:'Guillermo Juarez',
                    value: 40081
                },
                {
                    text:'Heder Cividanis',
                    value: 45999
                },
                {
                    text:'Serlvin Ixtacuy',
                    value: 48850
                },],

                TipoCasoReporte:[{
                    text: 'Pendientes',
                    value: 'PENDIENTES'
                },
                {
                    text: 'Finalizados',
                    value: 'FINALIZADOS'
                }],

                tipo: [{
                    text: 'Software (Sistema)',
                    value: 1
                }, {
                    text: 'Hardware (Dispositivos o Equipo)',
                    value: 2
                }, {
                    text: 'Telefonía',
                    value: 3
                }, {
                    text: 'Redes y Seguridad',
                    value: 4
                }, {
                    text: 'Tareas/Otros',
                    value: 5
                }],
                hospitales: [],
                misSolicitudes: [], //Solicitudes personales
                pendientes: [],
                pendientesAsig: [],
                todas: [] //Lista todas las solicitudes
              
            },

            permisos: {
                asignar: false,
                respuesta: false,
                listado: false,
                Status: false,
                cambiar_imagen: true,
            },

            TicketsTecnicos:[],
            SelectTecnicos:[],


            conteo: {
                pendientes: 0,
                pendientesAsig: 0
            }
        }
    },
    components: {
        apexchart: VueApexCharts,
        SMHorario,
        FechaYMes

    },
    computed: {
        sesion() {
            return this.$store.state.sesion
        }
    },




    watch: {
        'barra.mostrar'(value) {
            if (value) {
                // Limpiando
                this.info.tipo = null
                this.info.hospital = null
                this.info.descripcion = null

                // Consultando hospitales
                if (this.listado.hospitales.length == 0) {
                    this.Consulta().Empresas()
                        .then(() => {
                            this.info.hospital = (this.listado.hospitales.filter(f => f.Codigo == this.sesion.sesion_sucursal).length > 0) ? this.sesion.sesion_sucursal : null
                        })
                } else {
                    this.info.hospital = this.sesion.sesion_sucursal
                }

                
            } else {
                this.opcion = 0
            }
        },

        'sesion.sesion_empresa'(value) {
            if (value) {
                // alert(1)
                setTimeout(() => {
                    this.$validar_funcionalidad('/TI/TI001', 'RESPUESTA', (d) => {
                        this.permisos.respuesta = d.status
                    })
                    this.$validar_funcionalidad('/TI/TI001', 'ASIGNAR', (d) => {
                        this.permisos.asignar = d.status
                    })
                    this.$validar_funcionalidad('/TI/TI001', 'CAMBIAR_IMAGEN', (d) => {
                        this.permisos.cambiar_imagen = d.status
                    })
                }, 5000)
            }
        },
        'rechazar.mostrar'(value) {
            if (!value) {
                this.rechazar.codigo = null
                this.rechazar.analisis = null
            }
        },
        opcion(value) {
            if (value == 1) {
                this.Consulta().Mis_Solicitudes()
            }
            if (value == 2) this.Consulta().Pendientes()
            if (value == 3) this.Consulta().Todas()
            if (value == 4) this.Consulta().PendientesAsig()
        }

    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        ConsultaHorario() {
            return {
                archivo: () => {
                    this.axios.post('/app/externo/ArchivosVariables_DB', {
                        idControl: 'WEBROLTECNICOS',
                        tipo: 'CONSULTA',

                    })
                        .then(resp => {
                            this.archivos = resp.data.json
                            resp.data.json.map(data => {
                                if (data.src != '') {
                                    this.imagen_src = data.src;

                                } else {
                                    this.imagen_src = '';
                                }
                            });

                        })
                }
            }
        },

        Consulta() {
            return {
                /**
                 * Listado de Hospitales
                 */
                Empresas: () => {
                    return this.axios.post('/app/administracion/Busqueda_Empresa', {
                        Codigo_Empresa: 'MED'
                    })
                        .then(resp => {
                            if (resp.data && resp.data.codigo == 0) {
                                this.listado.hospitales = resp.data.json.map(m => {
                                    return {
                                        Codigo: m.Codigo,
                                        Nombre: m.Nombre
                                    }
                                })
                            }
                        })
                },
                /**
                 * Listado de Solicitudes por persona
                 */
                Mis_Solicitudes: () => {
                    return this.axios.post('/app/administracion/TI_Soporte_Personal', {})
                        .then(resp => {
                            if (resp.data && resp.data.codigo == 0) {
                                this.listado.misSolicitudes = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Estado: (m.Status == 'S') ? 'Finalizado' : (m.TecnicoAsigna != null && m.TecnicoAsigna != '' && m.Status == 'P') ? 'Asignado' : (m.Status == 'R') ? 'Rechazado' : 'Pendiente'
                                    }
                                })
                            }
                        })
                },

                ConsultaTecnicos: () => {
                    return {
                        ConsultaCorporativo: () => {
                            const corporativoUsuario = this.sesion.corporativo;

                            if (corporativoUsuario) {
                                this.axios.post('/app/administracion/CorporativoConsulta', {
                                    IdCorporativo: corporativoUsuario
                                })
                                    .then(resp => {
                                        if (resp.data.json) {
                                            // Filtrar los resultados para que solo traigan aquellos con idPuesto igual a 79
                                            const resultadosFiltrados = resp.data.json.filter(item => item.idPuesto === 79);

                                            this.$map({
                                                objeto: this.info,
                                                respuestaAxios: resultadosFiltrados, // Usamos los resultados filtrados
                                                omitir: ['corporativoUsuario']
                                            });
                                        }
                                    });
                            } 
                        },
                    };
                },



                /**
                 * 
                 */
                Todas: () => {
                    return this.axios.post('/app/administracion/TI_Soporte_Todos', {})
                        .then(resp => {
                            if (resp.data && resp.data.codigo == 0) {
                                this.series = [0, 0, 0, 0]
                                this.listado.todas = resp.data.json.map(m => {
                                    const Estado = (m.Status == 'S') ? 'Finalizado' : (m.TecnicoAsigna != null && m.TecnicoAsigna != '' && m.Status == 'P') ? 'Asignado' : (m.Status == 'R') ? 'Rechazado' : 'Pendiente'
                                    if (Estado == 'Asignado') this.series[0]++
                                    if (Estado == 'Finalizado') this.series[1]++
                                    if (Estado == 'Pendiente') this.series[2]++
                                    if (Estado == 'Rechazado') this.series[3]++
                                    return {
                                        ...m,
                                        Detalle: `
                                                ${m.Usuario} - ${m.NombreUsuario}
                                                <br>
                                                <small style="color:rgb(52, 152, 219)">${(m.Hospital) ? this.listado.hospitales.filter(f => f.Codigo == m.Hospital).length > 0 ? this.listado.hospitales.filter(f => f.Codigo == m.Hospital)[0].Nombre : null : null}</small>
                                                <br>
                                                <small style="color:rgb(52, 152, 219)">${(m.IdTipo) ? this.listado.tipo.filter(f => f.value == m.IdTipo)[0].text : null}</small>
                                                <br>
                                                <small style="color:rgb(52, 152, 219)">${this.$formato_fecha(m.Fecha)}</small>
                                            </small>`,
                                        Estado
                                    }
                                })
                            }
                        })
                },
                /**
                 * Listado de Solicitudes Pendientes
                 */
                Pendientes: () => {
                    return this.axios.post('/app/administracion/TI_Soporte_Pendientes', {})
                        .then(resp => {
                            if (resp.data && resp.data.codigo == 0) {
                                this.listado.pendientes = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        Detalle: `${m.Usuario} - ${m.NombreUsuario}
                                            <br>
                                            <small style="color:rgb(52, 152, 219)">${(m.Hospital) ? this.listado.hospitales.filter(f => f.Codigo == m.Hospital).length > 0 ? this.listado.hospitales.filter(f => f.Codigo == m.Hospital)[0].Nombre : null : null}</small>
                                            <br>
                                            <small style="color:rgb(52, 152, 219)">${(m.IdTipo) ? this.listado.tipo.filter(f => f.value == m.IdTipo)[0].text : null}</small>
                                            <br>
                                            <small style="color:rgb(52, 152, 219)">${this.$formato_fecha(m.Fecha)}</small>`,
                                        asignar: true,
                                        usuarioAsignar: m.TecnicoAsigna,
                                        Estado: (m.Status == 'S') ? 'Finalizado' : (m.TecnicoAsigna != null && m.TecnicoAsigna != '' && m.Status == 'P') ? 'Asignado' : (m.Status == 'R') ? 'Rechazado' : 'Pendiente'
                                    }
                                })
                                // Conteo
                                this.conteo.pendientes = this.listado.pendientes.filter(f => f.TecnicoAsigna == '' || f.TecnicoAsigna == null).length
                            }
                        })
                },
                /**
                 * Pendientes asignados al usuario
                 */
                PendientesAsig: () => {
                    return this.axios.post('/app/administracion/TI_Soporte_Pendientes_Asignados', {})
                        .then(resp => {
                            if (resp.data && resp.data.codigo == 0) {
                                this.listado.pendientesAsig = resp.data.json.map(m => {
                                    return {
                                        ...m,
                                        respuesta: true,
                                        usuarioAsignar: m.TecnicoAsigna
                                    }
                                })
                                // Conteo
                                this.conteo.pendientesAsig = this.listado.pendientesAsig.filter(f => f.FechaFinalizado == '' || f.FechaFinalizado == null).length
                            }
                        })
                }
            }
        },

        //=======================================================================================================
        // Consulta Imagen
        //=======================================================================================================
        ConsultaHorarios(imagen) {
            this.imagen_src = imagen
        },



        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar() {
            return {
                /**
                 * Guarda la información del ajeno
                 */
                Solicitud: () => {
                    this.axios.post('/app/administracion/TI_Soporte_NuevaSolicitud', this.info)
                        .then(resp => {

                            if (resp.data && resp.data.codigo > 0) {
                                this.$mensaje_Slack('#ticket', `*Código:*\n ${resp.data.codigo}\n*Nombre:*\n ${this.sesion.corporativo} ${this.sesion.usuario}\n*Area:*\n ${this.info.area}\n*Extensión:*\n ${this.info.extension}\n*Tipo:*\n ${this.listado.tipo.filter(f => f.value == this.info.tipo)[0].text}\n*Descripción:*\n ${this.info.descripcion}`)

                                this.info.area = null
                                this.info.extension = null
                                this.info.tipo = null
                                this.info.descripcion = ''
                                this.$refs.solicitud.reset();

                                setTimeout(() => {
                                    this.$vs.notify({
                                        time: 4000,
                                        title: 'Soporte',
                                        text: 'Se ha asignado el ticket no. ' + resp.data.codigo,
                                        iconPack: 'feather',
                                        icon: 'icon-alert-circle',
                                        color: 'success',
                                        position: 'top-center'
                                    })
                                }, 1000)
                            }
                        })
                },

                /**
                 * Guardar usuario asignado
                 */
                Asignar: (info) => {
                    this.axios.post('/app/administracion/TI_Soporte_Asignar', {
                        Codigo: info.Codigo,
                        usuarioAsignar: info.usuarioAsignar
                    })
                        .then(resp => {
                            if (resp.data && resp.data.codigo == 0) {
                                // this.$mensaje_Slack(info.usuarioAsignar, `*Asignación de Ticket:* ${info.Codigo}\n\n *Nombre:* ${info.NombreUsuario}\n*Area:* ${info.Area}\n*Extensión:* ${info.Extension}\n*Tipo:* ${this.listado.tipo.filter(f=>f.value==info.IdTipo)[0].text}\n*Descripción:* ${info.SDescrpcion}`)
                                this.detalle.info = null
                                this.detalle.mostrar = false
                            }
                            return this.Consulta().Pendientes()
                        })
                        .then(() => this.$vs.loading.close())
                },

                /**
                 * Guardar usuario asignado
                 */
                Rechazar: (info) => {
                    this.axios.post('/app/administracion/TI_Soporte_Rechazar', {
                        Codigo: info.Codigo,
                        Analisis: info.Analisis
                    })
                        .then(resp => {
                            if (resp.data && resp.data.codigo == 0) {
                                // this.$mensaje_Slack("#ticket", `Se ha rechazado el Ticket ${info.Codigo}`)
                                info.mostrar = false
                                this.detalle.mostrar = false
                            }
                            this.Consulta().Pendientes()
                        })
                        .then(() => this.$vs.loading.close())
                },

                /**
                 * Guardar usuario asignado
                 */
                Respuesta: (info) => {
                    this.axios.post('/app/administracion/TI_Soporte_Respuesta', {
                        Codigo: info.Codigo,
                        Analisis: info.SAnalisis,
                        Problema: info.Sproblema
                    })
                        .then(resp => {
                            if (resp.data && resp.data.codigo == 0) {
                                this.detalle.info = null
                                this.detalle.mostrar = false
                                this.Consulta().PendientesAsig()
                            }
                        })
                }
            }
        },

        Otros() {
            return {
                limpiarDescripcion: (valor) => {
                    return valor.replace(/\\n/g, '<br>')
                }
            }
        },

        //=======================================================================================================
        //Consulta tecnicos 
        //=======================================================================================================
        
        ConsultaTickes(){

            this.listaTecnicoAjuntar = this.SelectTecnicos.map((tecnico)=>{
                let NuevoListado = {};
                NuevoListado.Corporativo = tecnico
                return NuevoListado;
            })
        
              this.axios.post('/app/administracion/EstadosTickes', {
                Operacion:this.info.tipoD,
                FechaF:this.info.FechaFinalizado,
                Fecha: this.info.Fecha,
                Tecnicos: this.listaTecnicoAjuntar
            }).then((resp)=>{
                if(resp.data && resp.data.codigo == 0){
                    this.TickesTecnicos = resp.data.json
                }
            })
        },

        Limpiar(){
            this.SelectTecnicos = []
            this.TickesTecnicos = [] 
            this.info.FechaFinalizado = null
            this.info.Fecha = null
            this.info.tipoD = null
        },

        
        CargarExcel() {
        this.$reporte_modal({
            Nombre: 'Reporte Status de Tickes',
            Formato: 'EXCEL',
            Opciones: {
                Operacion: this.info.tipoD,
                FECHA: this.info.Fecha,
                FECHAF: this.info.FechaFinalizado,
                ListaTecnicos: this.SelectTecnicos.join(',')
            }
        });
        },

        showTicketDetails(ticket) {
      this.selectedTicket = ticket;
      this.popupActivoTecnicos = true
    }

    },

    mounted() {
        setTimeout(() => {
            this.$validar_funcionalidad('/TI/TI001', 'RESPUESTA', (d) => {
                this.permisos.respuesta = d.status
                
            })
            this.$validar_funcionalidad('/TI/TI001', 'CAMBIAR_IMAGEN', (d) => {
                this.permisos.cambiar_imagen = d.status
            })

            this.$validar_funcionalidad('/TI/TI001', 'LISTADO', (d) => {
                this.permisos.listado = d.status
            })
            this.$validar_funcionalidad('/TI/TI001', 'ASIGNAR', (d) => {
                this.permisos.asignar = d.status

            })
            this.$validar_funcionalidad('/TI/TI001', 'STATUS', (d) => {
                this.permisos.Status = d.status

            })
        }, 1000)
    },
    created() {

    }
}

</script>

<style scoped>
.notificacion {
    text-align: center;
    font-size: 25px;
    height: 40px;
    color: rgba(var(--vs-primary), 0.7);
    margin-left: 10px;
    cursor: pointer;
    position: relative;
}

.notificacion:hover {
    color: rgba(var(--vs-primary), 1);
    ;
}

.notificacion .titulo {
    font-weight: bold;
    font-size: 11px;
    position: relative;
    top: -5px;
}

.notificacion .notificaciones {
    font-size: 10px;
    background: #E74C3C;
    border-radius: 100%;
    position: absolute;
    top: 0px;
    right: -2px;
    height: 20px;
    min-width: 20px;
    padding: 5px;
    text-align: center;
    color: white;
    line-height: 11px;
}

.fondo {
    font-size: 250px;
    position: absolute;
    z-index: -1;
    opacity: 0.1;
    left: 50px;
    /* right:50px; */
    transform: rotate(-5deg);
}

.container {
    display: flex;
}

.container .calendario {
    padding: 10px;
    border-radius: 5px;
}

.container .Contactos {
    width: 100%;
    height: auto;
    padding: 5px;
    border-radius: 5px;
}

.tamaño {
    width: 100%;
    height: 120px;
    border: 2px solid #F4D03F;
    border-radius: 5px;
    padding: 10px;
    font-size: 12px;
    color: black;
    text-align: justify;
    background-color: #F9E79F;
}

.img {
    width: 800px;
    height: 450px;
}

.content-table {
    border-collapse: collapse;
    margin: 25px 0;
    font-size: 0.9em;
    min-width: 350px;
    border-radius: 5px 5px 0 0;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, );

}

.content-table thead tr {
    background-color: rgba(34, 185, 245, 0.2);
    color: black;
    text-align: left;
    font-weight: bold;
}

.content-table th {
    padding: 12px 15px;
}

.content-table td {
    padding: 12px 15px;

}

.content-table tbody tr {
    border-bottom: 1px solid #dddddd;
}

.content-table tbody tr:nth-of-type(even) {
    background-color: #f3f3f3;
}

.content-table tbody tr:last-of-type {
    border-bottom: 2px solid rgba(34, 185, 245, 0.2);
    ;
}

.container {
    display: flex;
}

.icons {
    text-align: center;
    font-size: 25px;
    height: 40px;
    color: rgba(var(--vs-primary), 0.7);
    margin-left: 10px;
    cursor: pointer;
    position: relative;
    color: #3498DB;
}

.icons:hover {
    color: rgba(var(--vs-primary), 1);
    ;
}

.icons .titulo {
    font-weight: bold;
    font-size: 11px;
    position: relative;
    top: -5px;
}


.Ticket .vs-popup {
    width: 990px !important;
}



</style>
