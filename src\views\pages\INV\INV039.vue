<template>
  <vx-card title="Consolidar Pedidos Sugeridos Cocina">
    <div class="content content-pagex">
      <div class="flex flex-wrap">
        <div class="pr-2">
          <label class="typo__label">Fecha Del:</label>
          <vs-input type="date" v-model="FechaDel" name="date1" />
        </div>
        <div class="pr-2">
          <label class="typo__label">Fecha Al:</label>
          <vs-input type="date" v-model="FechaAl" name="date2" />
        </div>
        <div :class="flexItem_md">
          <P>
            <b>Grupo:</b>
          </P>
          <multiselect v-model="grupo" :options="ListadoGrupos" :allow-empty="false" :showLabels="false"
            placeholder="Seleccione un Grupo" track-by="Nombre" label="Nombre" @input="cargar_subgrupos"></multiselect>
        </div>
        <div :class="flexItem_md" class="md:pl-2">
          <P>
            <b>SubGrupo:</b>
          </P>
          <multiselect style="z-index: 1;" v-model="subgrupo" :options="ListadoSubGrupos" :allow-empty="false"
            :showLabels="false" placeholder="Seleccione un SubGrupo" track-by="Nombre" label="Nombre" @input="LimpiarPedidosSugeridos()">
            <template #noOptions>
              <p>Elegir un grupo primero</p>
            </template>
          </multiselect>
        </div>
        <div class="break"></div>
        <div :class="flexItem_md">
          <P>
            <b>Pedidos a Consolidar:</b>
          </P>
          <vs-textarea label="Numero Solicitudes" type="text" class="w-full" v-model="PedidosConsolidados" />
        </div>        
        <div class="md:pl-4 md:pr-2">
          <vs-button style="margin-top: 20px" color="green" type="filled" @click="VerListadoPedidoSugerido"
            icon="info">Pedidos Cocina</vs-button>
        </div>
        <div class="md:pl-2">
          <vs-button style="margin-top: 20px" color="primary" type="filled" @click="VerPedidoConsolidado" icon="settings">
            Cargar Consolidado</vs-button>
        </div>
        <div class="md:pl-2">
          <vs-button @click="reporte_pedido_consolidado('PDF')" color="danger" 
                     style="margin-right:5px; margin-top: 20px"><i class="fas fa-file-pdf "></i> PDF</vs-button>                
        </div>
        <div class="md:pl-2">
          <vs-button @click="reporte_pedido_consolidado('EXCEL')" color="success" 
                    style="margin-top: 20px"><i class="far fa-file-excel"></i> Excel</vs-button>
        </div>
        <div :class="flexItem_full" style="z-index: 0;">
          <vs-divider>Listado de Pedidos Sugeridos</vs-divider>
          <template lang="html">
            <div>
              <vs-table multiple v-model="selected" max-items="15" pagination :data ="ListadoPedidos" search noDataText="Sin datos disponibles">
                    <template slot="thead">
                      <vs-th>No Solicitud</vs-th>
                      <vs-th>Hospital</vs-th>
                      <vs-th>Nombre de la Bodega</vs-th>
                      <vs-th>Fecha</vs-th>
                      <vs-th>Observación</vs-th>
                      <vs-th>Solicitante Corporativo</vs-th>                      
                    </template>
                    <template slot-scope="{data}">
                      <vs-tr :data="tr" :key="indextr" v-for="(tr, indextr) in data">
                        <vs-td :data="indextr+'nosolicitud'">{{tr.NoSolicitud}}</vs-td>
                        <vs-td :data="indextr+'hospital'">{{tr.Hospital}}</vs-td>
                        <vs-td :data="indextr+'bodega'">{{tr.NombreBodegaDestino}}</vs-td>
                        <vs-td :data="indextr+'fecha'">{{tr.Fecha}}</vs-td>
                        <vs-td :data="indextr+'observacion'">{{tr.Observaciones}}</vs-td>
                        <vs-td :data="indextr+'solicitante'">{{tr.Solicitante}}</vs-td>
                      </vs-tr>
                    </template>
                  </vs-table>
            </div>
          </template>
        </div>
        <div :class="flexItem_full" style="z-index: 0;">
          <vs-divider>Pedido Consolidado</vs-divider>
          <template lang="html">
            <div>
              <vs-table max-items="15" pagination :data="ListadoConsolidado" noDataText="Sin datos disponibles">
                <template slot="thead">
                  <vs-th>Producto</vs-th>
                  <vs-th>Nombre</vs-th>
                  <vs-th>U. Medida</vs-th>
                  <vs-th>Total</vs-th>
                  <vs-th>Costo Unitario</vs-th>
                  <vs-th>Total Consolidado</vs-th>
                </template>
                <template slot-scope="{data}">
                  <vs-tr :key="indextr" v-for="(tr, indextr) in data">
                    <vs-td :data="indextr+'producto'">{{ tr.Producto }}</vs-td>
                    <vs-td :data="indextr+'nombre'">{{ tr.Nombre }}</vs-td>
                    <vs-td :data="indextr+'descripcion'">{{ tr.Descripcion }}</vs-td>
                    <vs-td :data="indextr+'total'">{{ tr.Total }}</vs-td>
                    <vs-td :data="indextr+'costounitario'" style="text-align:right">{{ $formato_moneda(tr.CostoUnitario) }}</vs-td>
                    <vs-td :data="indextr+'totalconsolidado'" style="text-align:right">{{ $formato_moneda(tr.TotalConsolidado) }}</vs-td>
                  </vs-tr>
                </template>
              </vs-table>
            </div>
          </template>
        </div>
      </div>
    </div>
    <!------------------ POPUP VER PEDIDO INICIO ------------------>


    <vs-popup fullscreen title="Revisión de Pedidos Sugeridos" :active.sync="popupActivo4">

      <h1>Revisión de Pedidos Sugeridos</h1>
      <vs-row>
        <vs-col class="columnas col1" vs-justify="center" vs-align="center" vs-w="4">
          <p>
            <b>Pedido:</b>
            {{ PedidoInfo.CodigoPS }}
          </p>
          <p>
            <b>Hospital:</b>
            {{ PedidoInfo.EmpresaSucursal }}
          </p>
          <p>
            <b>Clase:</b>

          </p>
          <p>
            <b>Fecha:</b>
            {{ PedidoInfo.Fecha }}
          </p>
          <p>
            <b>Solicitante:</b>
            {{ PedidoInfo.NombreUsuario }}
          </p>
          <p>
            <b>Bodega Destino:</b>
            {{ PedidoInfo.NombreBodegaDestino }}
          </p>
          <p>
            <b>Número de Movimiento:</b>

          </p>
        </vs-col>
        <vs-col class="columnas col2" vs-justify="center" vs-align="center" vs-w="3">
          <p>
            <b>Tipo:</b>
            {{ PedidoInfo.NombreTipoPedido }}
          </p>
          <p>
            <b>Observaciones:</b>
            {{ PedidoInfo.Observaciones }}
          </p>
        </vs-col>
      </vs-row>

      <p>
        <datatable title="Detalle Pedido" :columns="tableColumns1" :rows="DetallePedido" locale="es">
        </datatable>
      </p>

    </vs-popup>
    <!------------------ POPUP VER PEDIDO FIN ------------------>
  </vx-card>
</template>
  
<script>
import DataTable from "vue-materialize-datatable";
import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";


export default {
  data() {
    return {
      flexItem_sm: 'xs:w-full sm:w-full md:w-1/7 lg:w-1/7 xl:w-1/7',
      flexItem_md: 'xs:w-full sm:w-full md:w-3/12 lg:w-3/12 xl:w-3/12',
      flexItem_6_12: 'xs:w-full sm:w-full md:w-6/12 lg:w-6/12 xl:w-6/12',
      flexItem_9_12: 'xs:w-full sm:w-full md:w-9/12 lg:w-9/12 xl:w-9/12',
      flexItem_full: 'w-full',
      seleccion_sp: [],
      selected: [],
      PedidosConsolidados: null,
      allCheck: false,
      configdateTimePicker: {
        allowInput: true,
        dateFormat: 'Y-m-d'
      },
      ListadoConsolidado: [],
      ListadoGrupos: [],
      ListadoSubGrupos: [],
      grupo: null,
      subgrupo: null,
      PedidoInfo: [],
      popupActivo4: false,
      FechaDel: '',
      FechaAl: '',
      ClasePedido: 'PS',
      TipoBodega: {},
      ListadoPedidos: [],
      DetallePedido: [],
      tableColumns1: [
        {
          label: "Linea",
          field: "Linea",
          numeric: false,
          html: false
        },
        {
          label: "Producto",
          field: "Producto",
          numeric: false,
          html: false
        },
        {
          label: "Nombre Producto",
          field: "Nombre",
          numeric: false,
          html: false
        },
        {
          label: "UMedida",
          field: "UnidadMedida",
          numeric: false,
          html: false
        },
        {
          label: "CPM",
          field: "PromUso",
          numeric: false,
          html: false
        },
        {
          label: "Existencia",
          field: "Existencia",
          numeric: false,
          html: false
        },
        {
          label: "Costo Ultimo",
          field: "CostoUltimo",
          numeric: false,
          html: false
        },
        {
          label: "Total",
          field: "Total",
          numeric: false,
          html: false
        },
        {
          label: "PedidoSugerido",
          field: "PedidoSugerido",
          numeric: false,
          html: false
        },
        {
          label: "Mínimo",
          field: "Minimo",
          numeric: false,
          html: false
        }
        ,
        {
          label: "Máximo",
          field: "Maximo",
          numeric: false,
          html: false
        }
      ],
      Tipos: [
        {
          CodigoAgrupacion: 2,
          Tipo: "M",
          Nombre: "Medicamentos"
        },
        {
          CodigoAgrupacion: 6,
          Tipo: "R",
          Nombre: "Repuestos y Suministros"
        },
        {
          CodigoAgrupacion: 7,
          Tipo: "Z",
          Nombre: "Limpieza"
        },
        {
          CodigoAgrupacion: 2,
          Tipo: "D",
          Nombre: "Diagnostico"
        },
        {
          CodigoAgrupacion: 3,
          Tipo: "L",
          Nombre: "Libreria"
        },
        {
          CodigoAgrupacion: 4,
          Tipo: "A",
          Nombre: "Cocina"
        }
      ]
    };
  },
  watch: {
    selected(nuevoValor){
      if(nuevoValor && nuevoValor.length >0){
        this.PedidosConsolidados = nuevoValor.map(pedido => pedido.NoSolicitud).toString()
      }else{
        this.PedidosConsolidados = null
      }
      this.ListadoConsolidado = []
    }
  },
  async beforeCreate() {
    this.listado_reportes = await this.$recupera_parametros_reporte('Pedidos Consolidados')
  },
  methods: {
    async reporte_pedido_consolidado(tiporeporte) {
      if(!this.PedidosConsolidados){
        this.$vs.notify({
          position:'top-center',
          color:'#B71C1C',
          title:'Inventario',
          text:'Debe ingresar numeros de pedidos a consolidar'
        })
        return
      }
      this.$reporte_modal({
        Nombre: "Pedidos Consolidados",
        Opciones: {NoPedidosSugeridos:this.PedidosConsolidados.trim()},
        Formato: tiporeporte
      })
    },
    cargar_grupos() {
      this.axios
        .post("/app/v1_OrdenCompra/ConsultaGrupo", { sub_grupo: 'N' })
        .then(resp => {
          this.ListadoGrupos = resp.data.json;
        })
        .catch(()=>{});
    }, //Fin de metodo cargar grupos
    cargar_subgrupos() {
      this.subgrupo = null
      this.axios
        .post("/app/v1_OrdenCompra/ConsultaGrupo", {
          sub_grupo: 'S',
          codigo_grupo: this.grupo.Codigo
        })
        .then(resp => {
          this.ListadoSubGrupos = resp.data.json;                   
          this.ListadoConsolidado = []
          this.PedidosConsolidados = null   
          this.ListadoPedidos = []
          this.selected = []        
        })
        .catch(() => {});
    },
    LimpiarPedidosSugeridos(){
      this.ListadoConsolidado = []
      this.PedidosConsolidados = null   
      this.selected = []  
    },
    VerPedidoConsolidado() {
      if(!this.PedidosConsolidados){
        this.$vs.notify({
          position:'top-center',
          color:'#B71C1C',
          title:'Inventario',
          text:'Debe ingresar numeros de solicitudes a consolidar'
        })
        return;
      }
      this.axios
        .post("/app/v1_OrdenCompra/ConsultaPedidoSugerido", {
          Opcion: 'C',
          SubOpcion: 'A',
          TipoPs: 'C',
          FechaInicio: this.FechaDel,
          FechaFin: this.FechaAl,
          CodigoGrupo: 0,
          CodigoSubGrupo: 0,
          BodegaFuente: 0,
          BodegaDestino: 0,
          CodigoAgrupacion: 0,
          Movimiento: 0,
          IdRequisicionEnc: 0,
          NoPedido: 0,
          pedido: [],
          PedidosBusqueda: this.PedidosConsolidados.split(',')
        })
        .then(resp => {
          this.ListadoConsolidado = resp.data.json;
        })
        .catch(()=>{});
    },    
    VerListadoPedidoSugerido() {
      this.ListadoPedidos = []
      this.ListadoConsolidado = []
      this.PedidosConsolidados = null   
      this.selected = []        

      if(!this.grupo){
        this.$vs.notify({
          position:'top-center',
          color:'#B71C1C',
          title:'Inventario',
          text:'Debe seleccionar un Grupo'
        })
        return
      }

      if(!this.subgrupo){
        this.$vs.notify({
          position:'top-center',
          color:'#B71C1C',
          title:'Inventario',
          text:'Debe seleccionar un SubGrupo'
        })
        return
      }
      
      this.axios
        .post("/app/v1_OrdenCompra/ConsultaPedidoSugerido", {
          Opcion: 'C',
          SubOpcion: 'C',
          TipoPs: 'C',
          CodigoGrupo: this.grupo ? this.grupo.Codigo : 0,
          CodigoSubGrupo: this.subgrupo ? this.subgrupo.Codigo : 0,
          FechaInicio: this.FechaDel,
          FechaFin: this.FechaAl,
          BodegaFuente: 0,
          BodegaDestino: 0,
          CodigoAgrupacion: 0,
          Movimiento: 0,
          IdRequisicionEnc: 0,
          NoPedido: 0,
          pedido: [],
          PedidosBusqueda: []
        })
        .then(resp => {
          this.ListadoPedidos = resp.data.json;
        })
        .catch(()=>{});
    },
    CerrarVerPedido() {
      this.PedidoInfo = null
    }
  },
  components: {
    Multiselect,
    "datatable": DataTable
  },
  mounted() {
    this.cargar_grupos();
  }
};
</script>
<style>
.break {
  flex-basis: 100%;
  height: 0;
}
</style>