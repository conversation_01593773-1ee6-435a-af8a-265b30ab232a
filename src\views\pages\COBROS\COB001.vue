<template>
<vx-card title="Semáforo de Honorarios">

    <div class="flex flex-wrap">
        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
            Ficha Médica
        </div>
        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
            <table>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Ingreso de Médico a registro
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Modificar tipo de Médico
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Actualización de datos
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Modificar Status de Médico
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <vs-divider />
    <div class="flex flex-wrap">
        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
            Tabla de Pagos a Honorarios
        </div>
        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
            <table>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Actualizar valores
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Ingresar nuevos pagos
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Descargar tabla de pagos
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <vs-divider />
    <div class="flex flex-wrap">
        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
            Pago de Honorarios Médicos
        </div>
        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
            <table>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Pagar honorarios SASI y Seguro
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Pagar honorarios de cuentas privadas
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Pago de Citas y Cupones
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Planilla de Cuenta Ajena
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <vs-divider />
    <div class="flex flex-wrap">
        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
            Gestiones y Autorizaciones
        </div>
        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
            <table>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('/COBROS/COB002')" icon="icon-link" ></vs-button> Bloquear honorarios por cargos
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('/COBROS/COB003')" icon="icon-link" ></vs-button> Desbloqueo de honorarios bloqueados
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('/COBROS/COB004')" icon="icon-link" ></vs-button> Ver honorarios bloqueados
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Validación de Lote
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <vs-divider />
    <div class="flex flex-wrap">
        <div class="w-full md:w-2/5 lg:w-2/5 xl:w-2/5">
            Reporte e Indicadores
        </div>
        <div class="w-full md:w-3/5 lg:w-3/5 xl:w-3/5">
            <table>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Reporte de Status de facturas
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Reporte de admisiones disponibles no pagadas
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Reporte de facturas
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> OTIF
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Reporte de información de médicos
                    </td>
                </tr>
                <tr>
                    <td>
                        <vs-button style="display:inline" color="primary" icon-pack="feather" @click="url('')" icon="icon-link" disabled></vs-button> Generar Estado de cuenta de médico
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <vs-divider />
</vx-card>
</template>

<script>
export default {
    data() {
        return {

        }
    },
    methods:{
        url(url){
            window.location = url
        }
    }
}
</script>

<style>
.button {
    margin: 0px !important;
}

.btnx {
    margin-left: 10px !important;
    background: red;
    border-radius: 5px 0px 0px 5px;
}

.btn-drop {
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: red;

}

.menu-funcionalidad {
    /* padding-top:0 !important; */
    /* box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); */
    /* padding: 15px; */
    display: inline;
    float: right;
    border-radius: 10px;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px;
}

.menu-funcionalidad a {
    text-align: center;
    padding-right: 10px;
}

.menu-funcionalidad i {
    font-size: 20px;
}

.vs-switch {
    height: 30px;
    width: 60px !important;
}

.vs-circle-switch {
    height: 25px;
    width: 25px;
}

.vs-switch--icon {
    font-size: 15px
}

.vs-switch.vs-switch-active .vs-switch--circle {
    margin-left: calc(100% - 30px);
}

.popup-generar {
    height: 100%
}

select {
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
}
</style>
