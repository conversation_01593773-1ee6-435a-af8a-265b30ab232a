<template>
<div class="flex flex-wrap pl-5 pr-5">

    <vs-popup ref="buscador" title="" :active.sync="noticia.mostrar" style="z-index:52001"  id="div-with-loading" class="popupMD">
        <div v-if="noticia.info" >
            <div class="cardDetalle">
                
                <!-- <img src="@/assets/images/news/ticket.jpg" alt=""> -->
                <div class="content">
                    <h4 v-html="noticia.info.titulo"></h4>
                    <small>{{noticia.info.fecha}}</small>
                    <br>
                    <br>
                    <div class="contenido" v-html="noticia.info.contenido"></div>
                </div>
                <br>

                <video v-if="noticia.info.video" :src="noticia.info.video" width="600" controls></video>                
            </div>
        </div>
    </vs-popup>

    <div class="w-full p-1 sm:w-1/2 md:w-1/3" v-for="(item,key) in noticias" :key="key">
        <div class="card">
            <img src="@/assets/images/news/ticket.jpg" alt="">
            <div class="content">
                <h4 v-html="item.titulo"></h4>
                <small>{{item.fecha}}</small>
                <div class="contenido" v-html="item.contenido"></div>
                <div class="conocer" @click="noticia.mostrar = true; noticia.info = item">
                    Conocer más
                </div>
            </div>
        </div>
    </div>
</div>
</template>

<script>
export default {
    data() {
        return {
            noticia: {
                mostrar: false,
                info: null
            },
            noticias: [
                // {
                //     titulo: 'Nuevo ticket de soporte <i style="color:#3498DB" class="fas fa-tags"></i>',
                //     imagen: '@/assets/images/news/ticket.jpg',
                //     // video: 'http://**************:8082/As400.mp4',
                //     fecha: '30/07/2021',
                //     contenido: 'Ahora en nuestro sistema SIGHOS, puedes realizar tus tickets de soporte técnico para resolver problemas de Software, Hardware, Redes y Seguridad. <i class="fas fa-tools"></i> <br><br> Podras crear nuevos ticket y visualizar el estado en el que se encuentran. '
                // }
            ]
        }
    },
    components: {

    },
    methods: {
        //=======================================================================================================
        // CONSULTA
        //=======================================================================================================
        Consulta: function() {
            return {}
        },

        //=======================================================================================================
        // NUEVO
        //=======================================================================================================
        Nuevo: function() {
            return {}
        },

        //=======================================================================================================
        // GUARDAR
        //=======================================================================================================
        Guardar: function() {
            return {}
        },

        //=======================================================================================================
        // EDITAR
        //=======================================================================================================
        Editar: function() {
            return {}
        },

        //=======================================================================================================
        // ELIMINAR
        //=======================================================================================================
        Eliminar: function() {
            return {}
        },

        //=======================================================================================================
        // OTROS
        //=======================================================================================================
        Otros: function() {
            return {}
        }
    },
    mounted() {

    }
}
</script>

<style scoped>
.card {
    border: 1px solid #ccc;
    background-color: #34495E;
    /* padding: 15px; */
    border-radius: 10px;
    box-shadow: 0px 4px 25px 0px rgb(0 0 0 / 10%);
    position: relative;
    overflow: hidden;
    height: 220px;
}

.card h4 {
    position: relative;
    color: #2C3E50;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 100%;
    overflow: hidden;
}

.card small {
    position: relative;
    top: -5px;
    font-size: 11px;
}

.card img {
    top: 0;
    left: 0;
    position: absolute;
    width: 100%;
    z-index: 0;
    opacity: 0.3;
}

.card .content {
    margin-top: 80px;
    background-color: white;
    position: relative;
    height: 140px;
    padding: 15px;
    overflow: hidden;
}

.card .contenido {
    margin-top: -5px;
    font-size: 13px;
    z-index: 10;
    color: #2C3E50;
    height: 60px;
    overflow: hidden;
    /* white-space: nowrap; */
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.card .conocer {
    color: #3498DB;
    float: right;
    cursor: pointer;
}
</style>
<style>
.popupMD .vs-popup {
    width: 650px !important;
}

</style>