<template>
<form-wizard color="rgba(var(--vs-primary), 1)" errorColor="rgba(var(--vs-danger), 1)" :title="null" :subtitle="null" backButtonText="Atras" nextButtonText="Siguiente" finishButtonText="Guardar">

    <!-- tab 1 Titulo -->
    <h1 align="center" Style="COLOR:#3390FF">CRM-MÉDICOS TECNODIAGNOSIS </h1>

    <tab-content title="Paso 1" class="mb-5" icon="feather icon-home" :before-change="validateStep1">
        <!-- tab 1 content -->
        <form data-vv-scope="step-1">
            <div class="vx-row">
                
                <!--Que radiologo lo conoce -->
                <div class="vx-col md:w-1/1 w-full mt-5 fixed-top"  >
                    <P>Buscar Médico</P>
                    <multiselect  v-model="radiologo" :options="ListadoRadiologos" track-by="Nombre1" label="Nombre1"></multiselect>
                
                     <!--<multiselect v-model="users" :options="ListadoRadiologos"  track-by="direcciones" label="direcciones"></multiselect>-->
                </div> 
            
                <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-input disabled v-validate="'required'" label="Nombre" v-model="firstName" class="w-full" name="first_name"  />  
                    <span class="text-danger" v-show="validInput.includes('first_name')">Ingrese Nombre</span>
                </div>
                <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-input disabled v-validate="'required'" label="Apellido" v-model="lastName" class="w-full" name="last_name" />
                    <span class="text-danger" v-show="validInput.includes('last_name')">Ingrese Apellido</span>
                </div>
                <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-input disabled type="email" label="Email" v-model="email" class="w-full" name="email" />
                    <span class="text-danger" v-show="validInput.includes('email')">Ingrese Correo Electronico</span>
                </div>
                <div class="vx-col w-1/2">
                    <vs-input disabled size="medium" v-validate="'required'" label="Especialización" name="Especialización" v-model="especializacion" class="mt-5 w-full" />
                    <span class="text-danger text-sm" v-show="validInput.includes('Especialización')">Ingrese Especializacion</span>
                </div>
     
  
    
                              
                    <div class="vx-col w-1/2">
                        <vs-input disabled size="medium"  label="Celular" name="numeric2" v-model="numeric2" class="mt-5 w-full" />
                    <span class="text-danger text-sm" v-show="errors.has('numeric2')">{{ errors.first('numeric2') }}</span>
                   <!-- <span class="text-danger" v-show="validInput.includes('numeric2')">Solo Permite Numeros</span>-->
                    </div>
        

                <!-- Must only consist of numbers se agrega celular-->
                <div class="vx-col w-1/2">
                    <vs-input disabled size="medium" label="Telefono" name="numeric" v-model="numeric" class="mt-5 w-full" />
                    <span class="text-danger text-sm" v-show="errors.has('numeric')">{{ errors.first('numeric') }}</span>
                    <span class="text-danger" v-show="validInput.includes('numeric')">Solo Permite Numeros</span>
                </div>


  <vs-table :data="users" v-if="users.length>0" class="vx-col md:w-1/1 w-full mt-5" stripe  >
     
      <template slot="thead"   >
        <vs-th >
          Dirección
        </vs-th>
             </template>
      <template slot-scope="{data}">
         <vs-tr :key="indextr"  v-for="(tr, indextr) in data" >
                 <vs-td :data="tr.direcciones"  >
                         {{ tr.direcciones  }}               

          </vs-td>
           </vs-tr>
      </template>
    </vs-table>

                <!-- Must only consist of numbers se agrega Telefono Clinica-->
                <div class="vx-col w-1/2">"
                <vs-input disabled size="medium"  label="Genero" v-model="EstadoGen" class="mt-5 w-full" />
                        <vs-select-item :key="index" :value="item.id" :text="item.nombre" v-for="(item,index) in EstadoGenero" class="w-full"/>
                    
                </div>
                 
                <!-- se agrega calendario de fecha de nacimiento -->
                <div class="vx-col md:w-1/2 w-full md:mt-5">
                    <vs-input disabled size="medium"  label="Fecha de Nacimiento" v-model="date" class="mt-5 w-full" />
               </div>
                
                <!--se agrega estado civil  -->
                <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-select v-model="Estadoc" class="w-full select-large mt-5" label="Estado Civil">
                        <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="(item,index) in Estadocivil" class="w-full" />
                    </vs-select>
                </div>
                <div class="vx-col md:w-1/2 w-full mt-5">
                <vs-input label="Que radiologo lo conoce" v-model="jobTitle1" class="w-full mt-4" name="job_title" />
                   <!-- <span class="text-danger" v-show="validInput.includes('job_title')">Ingrese Nombre de Radiologo</span>-->
                    </div>
                    <div class="vx-col md:w-1/2 w-full mt-5">
                <vs-input label="Nombre Asistente/Secretaria" v-model="jobTitle" class="w-full mt-4" name="job_title"  />
                    <!--<span class="text-danger" v-show="validInput.includes('job_title')">Ingrese Nombre de Asistente/Secretaria</span>-->
                    </div>  

         <div class="vx-col md:w-1/2 w-full">
                    <vs-textarea v-model="GustosPreferenciasSecretaria" label="Gustos o Preferencias Asistente/Secretaria" class="md:mt-10 mt-6 mb-0" rows="3" />
                </div>

            </div>
        </form>
    </tab-content>

    <!-- tab 2 content -->
    <tab-content title="Paso 2" class="mb-5" icon="feather icon-briefcase" :before-change="validateStep2">
        <form data-vv-scope="step-2">
            <div class="vx-row">
                <div class="vx-col md:w-1/2 w-full">
                    <vs-input size="medium"  label="Horario de Consulta" name="HorarioConsulta" v-model="Consultamed" class="mt-5 w-full" />
                    <span class="text-danger text-sm" v-show="errors.has('HorarioConsulta')">{{ errors.first('HorarioConsulta') }}</span> 
                 <!--   <span class="text-danger" v-show="validInput.includes('HorarioConsulta')">Ingrese Horario</span>-->

                    
                </div>
                <!--Modificar esto hay que corregir que no aparezca igual -->
                <div class="vx-col md:w-1/2 w-full">
                    <vs-textarea v-model="Gustosopreferencias" label="Gustos o Preferencias" class="md:mt-10 mt-6 mb-0" rows="3" />
                </div>
                <!--se agrega Caracteristicas personales -->
                <div class="vx-col md:w-1/2 w-full">
                    <vs-textarea v-model="Caracpersonales" label="Características Personales" class="md:mt-10 mt-6 mb-2" rows="3" />
                </div>
                <!--se Opinion sobre el médico -->
                <div class="vx-col md:w-1/2 w-full">
                    <vs-textarea v-model="Opinionsobremedico" label="Opinión sobre el Médico" class="md:mt-10 mt-6 mb-3" rows="3" />
                </div>
                <!--Que radiologo lo conoce 
                <div class="vx-col md:w-1/2 w-full mt-5">
                    <multiselect v-model="radiologo" :options="ListadoRadiologos" track-by="text" label="text"></multiselect>
                </div> -->
                <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-input label="PotencialMedico" v-model="PotencialMedico" class="w-full" name="Potencial_Medico"  />  
                    <span class="text-danger" v-show="validInput.includes('Potencial_Medico')">Potencial Médico</span>
                </div>

    <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-input disabled label="Referencia promedio ultimos 6 meses" v-model="Reciprocidad" class="w-full" name="Reciprocidad"  />  
                    <span class="text-danger" v-show="validInput.includes('Reciprocidad')">Potencial Médico</span>
                </div>

                
    <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-input disabled label="Referencia Real" v-model="ReciprocidadReal" class="w-full" name="ReciprocidadReal"  />  
                    <span class="text-danger" v-show="validInput.includes('ReciprocidadReal')">Potencial Médico</span>
                </div>

            </div>
        </form>
    </tab-content>

    <!-- tab 3 content -->
    <tab-content title="Paso 3" class="mb-5" icon="feather icon-image" :before-change="validateStep3">
        <form data-vv-scope="step-3">
            <div class="vx-row">
                
                <div class="vx-col md:w-1/2 w-full">
                    <vs-select v-model="califmed" class="w-full select-large mt-5" label="CALIFICACIÓN COMO MÉDICO?" v-validate="'required'">
                        <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="(item,index) in CalificacionMedico" class="w-full" />
                                            </vs-select>
                                            <span class="text-danger" v-show="validInput.includes('event_name')">Ingresé calificación</span> 
                </div>
                <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-select v-model="Exitos" class="w-full select-large" label="QUE TAN EXITOSO?" v-validate="'required'">
                        <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="(item,index) in Exitoso" class="w-full" />
                                        </vs-select>
                                            <span class="text-danger" v-show="validInput.includes('event_name')">Ingresé el exito</span> 
                </div>
                <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-select v-model="Potencia" class="w-full select-large" label="QUE POTENCIAL?" v-validate="'required'">
                        <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="(item,index) in Potencial" class="w-full" />
                        
                    </vs-select>
                    <span class="text-danger" v-show="validInput.includes('event_name')">Ingresé potencial</span>
                </div>
                <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-select v-model="reftecno" class="w-full select-large" label="ES REFERENTE DE TECNODIAGNOSIS?" v-validate="'required'">
                        <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="(item,index) in Potencialderef" class="w-full" />
                        
                    </vs-select>
                    <span class="text-danger" v-show="validInput.includes('event_name')">Ingresé referente</span>
                </div>
               
        
                <div class="vx-col md:w-1/2 w-full">
                    <vs-textarea v-model="SucesosRelevantesPos" label="Sucesos Relevantes Positivos" class="md:mt-10 mt-6 mb-0" rows="3" />
                </div>
                <div class="vx-col md:w-1/2 w-full">
                    <vs-textarea v-model="SucesosRelevantesNeg" label="Sucesos Relevantes Negativos" class="md:mt-10 mt-6 mb-0" rows="3" />
                </div>

 <div class="vx-col md:w-1/2 w-full mt-5">
                    <vs-select v-model="relcontecno" class="w-full select-large" label="RELACIÓN CON TECNODIAGNOSIS?">
                        <vs-select-item :key="index" :value="item.value" :text="item.text" v-for="(item,index) in relcontecnodiagnosis" class="w-full" v-validate="'required'" />
                    </vs-select>
                    <span class="text-danger" v-show="validInput.includes('event_name')">Ingresé relación</span>
                </div>
                <div class="vx-col md:w-1/2 w-full">
                    <vs-input label="Quien Escribió la Información" v-model="eventName" class="w-full mt-5" name="event_name" v-validate="'required|alpha_spaces'" />
                    <span class="text-danger" v-show="validInput.includes('event_name')">Ingresé Información</span> 
                </div>

            </div>
        </form>
    </tab-content>
</form-wizard>
</template>

<script>


import {
    FormWizard,
    TabContent
} from 'vue-form-wizard'
import 'vue-form-wizard/dist/vue-form-wizard.min.css'
// import Datepicker from 'vuejs-datepicker';
import Multiselect from 'vue-multiselect'
import "vue-multiselect/dist/vue-multiselect.min.css"

// For custom error message
import {
    Validator
} from 'vee-validate';
const dict = {
    custom: {
        first_name: {
            required: 'First name is required',
            alpha: "First name may only contain alphabetic characters"
        },

        //se agrega segundo nombre
        second_name: {
            required: 'second name is required',
            alpha: "second name may only contain alphabetic characters"
        },

        last_name: {
            required: 'Last name is required',
            alpha: "Last name may only contain alphabetic characters"
        },

        //se agrega segundo apellido
        secondlast_name: {
            required: 'second Last name is required',
            alpha: "Last name may only contain alphabetic characters"
        },

        email: {
            required: 'Email is required',
            email: "Please enter valid email"
        },

        //se agrega especializacion
        Especializacion: {
            required: 'Especializacion is required',
            especializacion: "Please enter valid especializacion"
        },

        //se agrega celular numeric
        numeric: {
            required: 'Numeric is required',
            numeric: "Please enter valid Numeric"
        },

        //se agrega Telefono clinica numeric
        numeric2: {
            required2: 'Numeric2 is required',
            numeric2: "Please enter valid Numeric2"
        },

        //se agrega calendario de fecha de nacimiento
        datepicker: {
            required2: 'Numeric2 is required',
            datepicker: "Please enter valid datepicker"
        },

        //se agrega Genero
        Genero: {
            required: 'Genero is required',
            Genero: "Please enter valid Genero"
        },
  

        job_title: {
            required: 'Job title name is required',
            alpha: "Job title may only contain alphabetic characters"
        },
        proposal_title: {
            required: 'Proposal title name is required',
            alpha: "Proposal title may only contain alphabetic characters"
        },

        //se agrega horario de consulta
        HorarioConsulta: {
            required: 'HorarioConsulta title name is required',
            alpha: "HorarioConsulta title may only contain alphabetic characters"
        },

        event_name: {
            required: 'Event name is required',
            alpha: "Event name may only contain alphabetic characters"
        },
    }
};

// register custom messages
Validator.localize('en', dict);


export default {
    
    data() {
        return {
     
            codigoajeno:"",
            date: null, //se agrega Fecha Nacimiento
            firstName: "",
            secondName: "", //se agrego segundo nombre
            lastName: "",
            secondlastName: "", //se agrego segundo apellido
            email: "",
            especializacion: "", //se agrega especializacion
            numeric: "", //se agrega celular
            numeric2: "", //se agrega Telefono Clinica
            Direccion: "",
            EstadoGen: null,
            Estadoc: "",
            radiologo: null,
            jobTitle1: "",
            jobTitle: "",
            Opinionsobremedico: "",
            Gustosopreferencias: "",
            GustosPreferenciasSecretaria: "",
            Caracpersonales: "",
            eventName: "",
            Activo:'S',
            califmed: "",
            Exitos: "",
            Potencia: "",
            reftecno: "",
            relcontecno: "",
            Consultamed: "",
            Descripcion: "",
            SucesosRelevantesPos: "",
            SucesosRelevantesNeg: "",
            Codigo:null,
            PotencialMedico: 0,
            Reciprocidad:0,
            ReciprocidadReal:0,
            

             users:[{}]
       
    ,

            EstadoGenero: [{
                    id: 1,
                    nombre: 'M'
                },
                {
                    id: 2,
                    nombre: 'F'
                }
            ],

            ListadoRadiologos: [],
            

            //se agrega Estado Civil
            Estadocivil: [{
                    text: "Soltero/a", 
                    value: "Soltero/a"
                },
                {
                    text: "Comprometido/a",
                    value: "Comprometido/a"
                },
                {
                    text: "En una Relación",
                    value: "En una Relación"
                },
                {
                    text: "Casado/a",
                    value: "Casado/a"
                },
                {
                    text: "Unido/a",
                    value: "Unido/a"
                },
                {
                    text: "Separado/a",
                    value: "Separado/a"
                },
                {
                    text: "Divorciado/a",
                    value: "Divorciado/a"
                },
                {
                    text: "Viudo/a",
                    value: "Viudo/a"
                },
            ],

            // se agrega Radiologia
            Radiologia: [],

            //Se agrega calificación como médico
            CalificacionMedico: [{
                    text: "EXCELENTE",
                    value: "EXELENTE"
                },
                {
                    text: "BUENO",
                    value: "BUENO"
                },
                {
                    text: "REGULAR",
                    value: "REGULAR"
                },
                {
                    text: "DEFICIENTE",
                    value: "DEFICIENTE"
                },
                {
                    text: "NO INFORMACIÓN",
                    value: "NO INFORMACION"
                },
            ],

            //se agrega que tan exitoso?
            Exitoso: [{
                    text: "MUY EXITOSO",
                    value: "MUY EXITOSO"
                },
                {
                    text: "EXITOSO",
                    value: "EXITOSO"
                },
                {
                    text: "REGULAR",
                    value: "REGULAR"
                },
                {
                    text: "NO EXITOSO",
                    value: "NO EXITOSO"
                },
                {
                    text: "NO INFORMACIÓN",
                    value: "NO INFORMACIÓN"
                },
            ],

            //se agrega potencial
            Potencial: [{
                    text: "MUCHO POTENCIAL",
                    value: "MUCHO POTENCIAL"
                },
                {
                    text: "MODERADO",
                    value: "MODERADO"
                },
                {
                    text: "REGULAR",
                    value: "REGULAR"
                },
                {
                    text: "NO POTENCIAL",
                    value: "NO POTENCIAL"
                },
                {
                    text: "NO INFORMACIÓN",
                    value: "NO INFORMACIÓN"
                },
            ],

            //se agrega es referente de Tecnodiagnosis
            Potencialderef: [{
                    text: "FIJO",
                    value: "FIJO"
                },
                {
                    text: "VARIABLE",
                    value: "VARIABLE"
                },
                {
                    text: "OCASIONAL",
                    value: "OCASIONAL"
                },
                {
                    text: "NULO",
                    value: "NULO"
                },
            ],
            ListadoDirecciones: [],
            //se agrega relacion con Tecnodiagnosis
            relcontecnodiagnosis: [{
                    text: "ACTIVO",
                    value: "ACTIVO"
                },
                {
                    text: "EN TRAMITE",
                    value: "EN TRAMITE"
                },
                {
                    text: "NULO",
                    value: "NULO"
                },
                {
                    text: "NO VA A SER REFERENTE DE TECNODIAGNOSIS",
                    value: "NO VA A SER REFERENTE DE TECNODIAGNOSIS"
                },
            ],

        }
     },
    

     computed: {
        validInput() {
            return this.errors.items.map(data => {
                return data.field
            })
        }
    },
    watch:{          

        radiologo(value){
             
            this.firstName = value.Nombre
            this.lastName = value.Apellido
            this.email = value.CorreoElectronico
            this.especializacion = value.Especialidad
            this.Direccion = value.Direccion
            this.numeric = value.Telefono
            this.numeric2 = value.TelefonoCasa
            this.EstadoGen = value.Genero
            this.date = value.FechaNacimiento
            this.Estadoc = value.EstadoCivil
            this.Codigo = value.Codigo
            this.Consultamed = value.HorarioConsulta 
            this.jobTitle = value.NombreAsistenteSecretaria
            this.jobTitle1 = value.QueRadiologoLoConoce
            this.Gustosopreferencias = value.GustosPreferencias
            this.GustosPreferenciasSecretaria = value.GustosPreferenciasSecretaria
            this.Caracpersonales = value.CaracteristicasPersonales  
            this.Opinionsobremedico = value.OpinionSobreMedico
            this.eventName = value.QuienEscribioInfo
            this.califmed = value.CalificacionMedico  
            this.Exitos = value.QueTanExitoso 
            this.Potencia = value.PotencialDeReferencia
            this.reftecno = value.EsReferenteDeTecno 
            this.relcontecno = value.RelacionConTecno
            this.Descripcion = value.Descripcion
            this.SucesosRelevantesPos = value.RelevantesPositivos
            this.SucesosRelevantesNeg = value.RelevantesNegativos
            this.PotencialMedico = value.QuePotencial
            this.cargar_direcciones()
            this.cargar_reciprocidad()
             
            //console.log(value.Codigo)
            
        },
              

    },
    
    methods: {
        // carga de información radíologos en mi combobox
        cargar_radiologos() {
            const url = this.$store.state.global.url_tecno
            const token = this.$store.state.global.tokenTecno
            const config = {
                headers: {
                    Authorization: 'Basic ' + token
                }
            };
            this.axios.post(url + 'CRM-Medicos', {}, config)
                .then(resp => {
                    this.ListadoRadiologos = resp.data
                    
                      
                })
                .catch(err => {
                    console.warn(err)
                })

        }, //Fin de metodo cargar_Radiologo
changeItem: function changeItem(rowId, event) {
      this.selected = "rowId: " + rowId + ", target.value: " + event.target.Codigo;
    
  },

  // carga de información de direcciones
        cargar_direcciones ()
    {
            const url = this.$store.state.global.url_tecno
            const token = this.$store.state.global.tokenTecno
            const config = {
                headers: {
                    Authorization: 'Basic ' + token
                }
            };
            
            this.axios.post(url + 'CRM-MedicosAjenos', {Codigo: this.Codigo}, config)
                .then(resp => {
                  this.users = []
                    resp.data.json.map(data => {
                        this.users.push({
                            direcciones: data.direcciones,
                            codigoajeno: data.codigoajeno
  
                   
                })
                    })
                })
                
        }, //Fin de metodo cargar_direcciones
         // carga de información de reciprocidad
        cargar_reciprocidad ()
    {
            const url = this.$store.state.global.url_tecno
            const token = this.$store.state.global.tokenTecno
            const config = {
                headers: {
                    Authorization: 'Basic ' + token
                }
            };
            
            this.axios.post(url + 'CRM-MedicosReciprocidad', {Codigo: this.Codigo}, config)
                .then(resp => {
             if (resp.data.codigo == 0 && resp.data.json.length > 0) {
              this.Reciprocidad= resp.data.json[0].Reciprocidad
              this.ReciprocidadReal= resp.data.json[0].ReciprocidadReal
                } else {
                     this.Reciprocidad = 0
                     this.ReciprocidadReal = 0
                }     
                })
                
        }, //Fin de metodo cargar_reciprocidad

            validateStep1() {
            return new Promise((resolve, reject) => {
                this.$validator.validateAll('step-1').then(result => {
                    if (result) {
                        
                        resolve(true)
                    } else {
                        reject("correct all values");
                    }
                })
            })
        },
        validateStep2() {
            return new Promise((resolve, reject) => {
                this.$validator.validateAll("step-2").then(result => {
                    if (result) {
                        resolve(true)
                    } else {
                        reject("correct all values");
                    }
                })
            })
        },
        validateStep3() {
            return new Promise((resolve, reject) => {
                this.$validator.validateAll("step-3").then(result => {
                    if (result) {
                        const url = this.$store.state.global.url_tecno
                        const token = this.$store.state.global.tokenTecno
                        const config = {
                            headers: {
                                Authorization: 'Basic ' + token
                            }
                        };
                        return this.axios.post(url+ 'crm-medicosinsert', {
                            Empresa: 'MED',
                            Codigo: this.Codigo,
                            Estadocivil: this.Estadoc,
                            HorarioConsulta: this.Consultamed,
                            NombreAsistenteSecretaria: (this.jobTitle!=null)? this.jobTitle.value:null,
                            GustosPreferencias: this.Gustosopreferencias,
                            GustosPreferenciasSecretaria: this.GustosPreferenciasSecretaria,
                            CaracteristicasPersonales: this.Caracpersonales,
                            OpinionSobreMedico: this.Opinionsobremedico,
                            //QueRadiologoLoConoce: (this.radiologo!=null)? this.radiologo.value:null,
                            QueRadiologoLoConoce: this.jobTitle1,
                            QuienEscribioInfo: this.eventName,
                            CalificacionMedico: this.califmed,
                            QueTanExitoso: this.Exitos,
                            QuePotencial: this.PotencialMedico,
                            EsReferenteDeTecno: this.reftecno,
                            PotencialDeReferencia: this.Potencia,
                            RelacionConTecno: this.relcontecno,
                            //Descripcion: this.Descripcion,
                            RelevantesPositivos: this.SucesosRelevantesPos,
                            RelevantesNegativos: this.SucesosRelevantesNeg,
                            Activo: 'S',
                            Opcion: 'u',
                            
                        },config)
                        .then(()=>{
                             if ( !this.$validar_privilegio('GUARDAR').status) {
                this.$vs.notify({
                    color: 'danger',
                    title: 'Privilegios',
                    text: 'No cuenta con los privilegios para GUARDAR',
                })
                //this.info.opcion.congelado = !this.info.opcion.congelado
                return false
            }                            
                            alert("Información Registrada Exitosamente");
                            resolve(true)
                             this.cargar_radiologos()
                        })
                        .catch(()=>{
                            alert("Error")
                            reject(true)
                        })
                        
                    } else {
                        reject("correct all values");
                    }
                })
            })
        }
    },
    

    components: {
        FormWizard,
        TabContent,
        // Datepicker,
        Multiselect
    },
    mounted() {
         this.cargar_radiologos()
         
    }
}

</script>

