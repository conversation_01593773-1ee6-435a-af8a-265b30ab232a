<template>
<div class="buttons">
    <div clientWidth="150" class="p-2 w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/6 div-button" v-for="(item, index) in menu" v-bind:key="index">
        <vs-button class="button" color="primary" type="filled" @click="OpcionSeleccionada(item)" :disabled="item.disabled? item.disabled($props): false">
            <div v-if="item.animation==='bf'">
                <font-awesome-icon :icon="['fas', item.icon]" class="i-size" beat-fade />
            </div>
            <div v-else-if="item.animation==='sh'">
                <font-awesome-icon :icon="['fas', item.icon]" class="i-size" shake />
            </div>
            <div v-else-if="item.animation==='sp'">
                <font-awesome-icon :icon="['fas', item.icon]" class="i-size" />
            </div>
            <div v-else-if="item.animation===''">
                <font-awesome-icon :icon="['fas', item.icon]" class="i-size" />
            </div>
            <span>{{item.text}}</span>
        </vs-button>
    </div>
</div>
</template>

<script>
export default ({
    name: "Menu",
    components: {

    },
    data() {
        return {
            selectedOption: 0,
        }
    },
    methods: {
        OpcionSeleccionada(item) {
            this.$emit('onSelectedItem', item)
        },
    },
    watch: {},
    props: {
        menu: null,
        SerieAdmision: null,
        CodigoAdmision: null,
        NumeroExpediente: null,

        StatusAdmision: null,
    },
})
</script>

<style scoped>
.buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.button {
    height: 150px;
    width: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 20px !important;
}

.button:hover {
    background-color: blue !important;
}

.div-button {
    display: flex;
    justify-content: center;
}

.i-size {
    font-size: 40px;
    padding-bottom: 5px;
}
</style>
