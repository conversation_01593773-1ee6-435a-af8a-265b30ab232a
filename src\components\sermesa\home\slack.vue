<template>
<vx-card class="p-2" title="Asignar Slack" collapse-action data-action="collapse">

    <div class="vx-row text-left">
        <div class="vx-col w-full ">

            <vs-input label="Corporativo" type="number" v-model="corporativo" />
            <label class="vs-input--label">ID Slack</label>
            <vx-input-group class="">
                <vs-input  v-model="slack" />
                <template slot="append">
                    <div class="append-text btn-addon">
                        <vs-button id="button-with-loading" @click="Guardar()"></vs-button>
                    </div>
                </template>
            </vx-input-group>

        </div>

    </div>

    <!-- Support Tracker Meta Data -->

</vx-card>
</template>

<script>
export default {
    data() {
        return {
            corporativo: null,
            slack: null,

            timer_chart: null,

        }
    },
    components: {

    },
    methods: {
        Guardar() {
            this.axios.post('/app/usuario/asignar_slack', {
                corporativo: this.corporativo,
                slack: this.slack
            })

        }
    },

    mounted() {

    }
}
</script>
