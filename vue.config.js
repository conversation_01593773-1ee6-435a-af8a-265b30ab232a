/*=========================================================================================
  File Name: vue.config.js
  Description: configuration file of vue
  ----------------------------------------------------------------------------------------
  Item Name: Vuexy - Vuejs, HTML & Laravel Admin Dashboard Template
  Author: Pixinvent
  Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/
const path = require('path');

module.exports = {
    publicPath: '/',
    transpileDependencies: [
        'vue-echarts',
        'resize-detector'
    ],
    devServer: {
        disableHostCheck: true
    },
    configureWebpack: {
        devtool: 'devtool',
        resolve: {
            alias: {
                '@f': path.resolve(__dirname, 'src/components/sermesa/funciones'),
            },
        },
        optimization: {
            splitChunks: {
                chunks: 'all'
            }
        }
    }
}

